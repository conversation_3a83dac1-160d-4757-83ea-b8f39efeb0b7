#!/bin/bash

## you need to first set a AWS_ACCOUNT_ID environment variable in order to use this script
set -e

# build the docker image
export DOCKER_CLI_EXPERIMENTAL=enabled
docker buildx create --use
docker buildx build --platform linux/amd64 -t walter:latest --load .

# tag the docker image
docker tag walter:latest ************.dkr.ecr.us-east-1.amazonaws.com/walter:latest

# push the docker image to ECR
aws ecr get-login-password | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
docker push ************.dkr.ecr.us-east-1.amazonaws.com/walter:latest
