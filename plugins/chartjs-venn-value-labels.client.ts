import { Chart, type ChartOptions } from "chart.js";
import { ArcSlice } from "chartjs-chart-venn";

interface LocalChartOptions extends ChartOptions<"venn"> {
  plugins?: ChartOptions<"venn">["plugins"] & {
    vennDataLabels?: {
      enabled?: boolean;
      fontSize?: number;
      fontFamily?: string;
      fontWeight?: string;
      fontColor?: string;
      formatType?: "percentage" | "default";
    };
  };
}

export default defineNuxtPlugin(() => {
  return {
    provide: {
      VennDataLabelsPlugin: {
        id: "vennDataLabels",
        afterDatasetsDraw: (chart: Chart<"venn">) => {
          const { ctx } = chart;
          const options = (chart.options as LocalChartOptions).plugins
            ?.vennDataLabels;
          const enabled = options?.enabled ?? false;
          if (!enabled) {
            return;
          }

          chart.data.datasets.forEach((dataset) => {
            // Assuming 'venn' type has data directly on the dataset
            (dataset.data as any[]).forEach((dataPoint, index) => {
              const meta = chart.getDatasetMeta(0).data[index] as ArcSlice;
              if (!meta) return;

              const { x, y } = meta.getCenterPoint();
              const value = dataPoint.value;

              ctx.save();
              ctx.textAlign = "center";
              ctx.textBaseline = "middle";
              ctx.font = `${options?.fontWeight ?? "400"} ${
                options?.fontSize ?? 14
              }px ${options?.fontFamily ?? "Inter"}`;
              ctx.fillStyle = options?.fontColor ?? "black";

              let formattedValue: string;
              if (value === undefined || value === null) {
                formattedValue = "N/A";
              } else if (options?.formatType === "percentage") {
                formattedValue = new Intl.NumberFormat(undefined, {
                  style: "percent",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 4,
                }).format(value / 100);
              } else {
                formattedValue = new Intl.NumberFormat(undefined, {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 4,
                }).format(value);
              }

              ctx.fillText(formattedValue, x, y);
              ctx.restore();
            });
          });
        },
      },
    },
  };
});
