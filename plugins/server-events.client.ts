import type { TopicInfo } from "@/types/server-events";

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hook("app:mounted", () => {
    const serverEventsStore = useServerEventsStore();
    const userSession = useUserSession();

    const { value: selectedAdvertiserIds } = storeToRefs(
      useAdvertiserFilterStore()
    );
    const { activeAgency: activeAgencyId } = storeToRefs(useAgencyStore());

    watch(
      activeAgencyId,
      async (next, prev) => {
        if (prev === next || !userSession.loggedIn.value) return;

        if (prev) {
          await serverEventsStore.unsubscribeFromTopic({
            topicLevel: "AGENCY",
            topicIdentifier: prev.id,
          });
        }

        if (next) {
          await serverEventsStore.subscribeToTopic({
            topicLevel: "AGENCY",
            topicIdentifier: next.id,
          });
        }
      },
      { immediate: true }
    );

    watch(
      selectedAdvertiserIds,
      async (next, prev) => {
        if (prev === next || !userSession.loggedIn.value) return;

        if (prev && prev.length > 0) {
          const topicsInfo = prev.map((advertiserId) => ({
            topicLevel: "ADVERTISER",
            topicIdentifier: advertiserId,
          })) as TopicInfo[];
          await serverEventsStore.unsubscribeFromTopic(topicsInfo);
        }

        if (next && next.length > 0) {
          const topicsInfo = next.map((advertiserId) => ({
            topicLevel: "ADVERTISER",
            topicIdentifier: advertiserId,
          })) as TopicInfo[];
          await serverEventsStore.subscribeToTopic(topicsInfo);
        }
      },
      { immediate: true }
    );

    watch(
      userSession.loggedIn,
      () => {
        if (userSession.loggedIn.value) {
          serverEventsStore.subscribeToTopic({
            topicLevel: "USER",
            topicIdentifier: userSession.user.value!.sub,
          });
          serverEventsStore.connect();
        } else {
          serverEventsStore.disconnect();
        }
      },
      { immediate: true }
    );
  });
});
