name: <PERSON>uild and Deploy Walter to ECS DEV Cluster

on:
  push:
    branches:
      - dev
env:
  ECR_REPO_IMAGE: ${{ vars.ECR_REPO_IMAGE }}
jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: DEV

    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_REGION_DEFAULT }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG .
          docker push $ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Deploy to ECS
        uses: imehedi/actions-awscli-v2@latest
        with:
          args: ecs update-service --cluster ${{ vars.ECS_CLUSTER }} --service ${{ vars.ECS_TARGET_SERVICE }} --force-new-deployment
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ vars.AWS_REGION_DEFAULT }}
