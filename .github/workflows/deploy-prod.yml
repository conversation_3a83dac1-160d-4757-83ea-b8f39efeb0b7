name: Build and Deploy <PERSON> to ECS PROD Cluster

on:
  push:
    branches:
      - main
env:
  ECR_REPO_IMAGE: ${{ vars.ECR_REPO_IMAGE }}
jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: PROD

    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_REGION_DEFAULT }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition Walter-PROD --query taskDefinition > task-definition.json

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG .
          docker push $ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/${{ vars.ECR_REPO_IMAGE }}:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: Walter
          image: ${{ steps.build-image.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ vars.ECS_TARGET_SERVICE }}
          cluster: ${{ vars.ECS_CLUSTER }}
          wait-for-service-stability: true
