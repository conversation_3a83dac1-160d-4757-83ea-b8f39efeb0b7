import type {
  DeliveryItem,
  DeliveryItemUpdate,
} from "~/types/delivery-item/delivery-item";
import {
  DeliveryItemSchema,
  DeliveryItemUpdateSchema,
} from "~/types/delivery-item/delivery-item";
import type { ScopeFilterHeader } from "@/composables/clients/dtos";
import { useScopeHeader } from "~/composables/clients/useScopeHeader";
import type { WatchStopHandle } from "vue";

async function fetchFeedPage(
  scopeFilter: ScopeFilterHeader,
  timeFilter: string,
  sortOption: string
): Promise<DeliveryItem[]> {
  return await $fetch("/relay/rob/api/feed", {
    headers: {
      ...scopeFilter,
    },
    query: {
      hoursBackFromNow: timeFilter,
      sort: sortOption,
    },
    parseResponse: (responseText: string) => {
      if (!responseText) {
        console.warn("Empty response text from server.");
        throw new Error("Empty response from server");
      }

      let jsonData;
      try {
        jsonData = JSON.parse(responseText);
      } catch (jsonError: any) {
        console.error("Invalid JSON response from server:", jsonError);
        console.error("Raw response text was:", responseText);
        throw new Error(`Invalid JSON response: ${jsonError.message}`);
      }

      if (!Array.isArray(jsonData)) {
        console.error(
          "Parsed JSON data is not an array. Raw response was:",
          responseText,
          "Parsed as:",
          jsonData
        );
        throw new Error("Expected an array response from server");
      }

      const validItems: DeliveryItem[] = [];
      let hasParsingErrors = false;

      for (const item of jsonData) {
        const parsedItem = DeliveryItemSchema.safeParse(item);
        if (parsedItem.success) {
          validItems.push(parsedItem.data);
        } else {
          hasParsingErrors = true;
          console.warn(
            "Failed to parse a feed item:",
            parsedItem.error.flatten(),
            "Problematic item data:",
            JSON.stringify(item)
          );
        }
      }

      if (hasParsingErrors) {
        console.warn(
          `Processed ${jsonData.length} items. ${validItems.length} valid, ${
            jsonData.length - validItems.length
          } failed parsing.`
        );
      }

      return validItems;
    },
  });
}

function createFeedState() {
  const itemMap = ref<Record<string, DeliveryItem>>({});
  const order = ref<string[]>([]);

  const items = computed(() => {
    return order.value.map((id) => itemMap.value[id]).filter(Boolean);
  });

  function setFeed(newItems: DeliveryItem[]) {
    itemMap.value = newItems.reduce((acc, item) => {
      acc[item.id] = item;
      return acc;
    }, {} as Record<string, DeliveryItem>);
    order.value = newItems.map((item) => item.id);
  }

  function addItems(newItems: DeliveryItem[]) {
    newItems.forEach((item) => {
      itemMap.value[item.id] = item;
      order.value.push(item.id);
    });
  }

  function updateItem(itemPatch: DeliveryItemUpdate) {
    const item = itemMap.value[itemPatch.id];
    if (!item) {
      console.error("Feed item with ID ${itemPatch.id} not found for update");
      return;
    }

    itemMap.value[item.id] = { ...item, ...itemPatch };
  }

  return { items, setFeed, addItems, updateItem };
}

// 新增：获取 markdown cards 的函数
async function fetchMarkdownCards(): Promise<DeliveryItem[]> {
  try {
    // 调用真实的 linda 接口
    const response = await $fetch<{title: string, content: string, user_query?: string}[]>("/relay/linda/workorder/markdown-cards");
    
    if (response && response.length > 0) {
      return response.map((item, index) => ({
        id: `markdown-card-${Date.now()}-${index}`,
        type: "MARKDOWN_CONTENT" as const,
        category: "BRIEFING" as const,
        summary: `AI-generated insights and recommendations based on your campaign data`,
        fullMessage: JSON.stringify({ 
          title: item.title,
          content: item.content,
          user_query: item.user_query 
        }),
        actionSuggestions: [{ actionType: "ACKNOWLEDGE" as const }],
        hasExecutionError: false,
        actionBy: null,
        actionAt: null,
        isRelevant: true,
        seenAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        ownerType: "ADVERTISER" as const,
        ownerId: "system"
      }));
    }
    
    // 如果后端没有返回数据，则返回空数组
    return [];
  } catch (error: any) {
    console.error("Failed to fetch markdown cards from Linda:", error);
    console.error("Error details:", {
      message: error.message,
      statusCode: error.statusCode,
      statusText: error.statusText,
      url: error.url || "/relay/linda/workorder/markdown-cards"
    });
    return [];
  }
}

export const useFeed = (
  advertiserIds: Ref<string[]>,
  timeFilter: Ref<string>,
  sortOption: Ref<string>
) => {
  const { items, setFeed, updateItem } = createFeedState();
  const { getScopeHeaderWith } = useScopeHeader();
  const status = ref<"pending" | "error" | "success">("pending");
  const error = ref<string | null>(null);

  const serverEventsStore = useServerEventsStore();

  const stopWatch = ref<WatchStopHandle | null>(null);

  onMounted(() => {
    stopWatch.value = watch(
      [advertiserIds, timeFilter, sortOption],
      handleFetchFeed,
      {
        immediate: true,
      }
    );

    serverEventsStore.on("feed:item-update", handleFeedItemUpdateEvent);
  });

  onUnmounted(() => {
    if (stopWatch.value) {
      stopWatch.value();
    }

    serverEventsStore.off("feed:item-update", handleFeedItemUpdateEvent);
  });

  async function handleFetchFeed() {
    if (!advertiserIds.value || !timeFilter.value || !sortOption.value) {
      return;
    }

    const scopeHeader = getScopeHeaderWith(
      {
        advertiserIds: advertiserIds.value,
      },
      true
    );

    status.value = "pending";
    error.value = null;

    // 同时调用两个服务
    const [robData, lindaData] = await Promise.allSettled([
      fetchFeedPage(
        scopeHeader.value,
        timeFilter.value,
        sortOption.value
      ).catch(err => {
        console.warn("Rob service failed:", err);
        return [];
      }),
      fetchMarkdownCards().catch(err => {
        console.warn("Linda service failed:", err);
        return [];
      })
    ]);

    // 合并结果
    const robItems = robData.status === "fulfilled" ? robData.value : [];
    const lindaItems = lindaData.status === "fulfilled" ? lindaData.value : [];
    const allItems = [...robItems, ...lindaItems];

    setFeed(allItems);
    
    // 只要有任何内容就算成功
    if (allItems.length > 0) {
      status.value = "success";
    } else {
      status.value = "error";
      error.value = "No content available from any service";
    }
  }

  function handleFeedItemUpdateEvent(event: MessageEvent) {
    const data = JSON.parse(event.data);
    const parsedData = DeliveryItemUpdateSchema.safeParse(data);
    console.log("feed item update event:", parsedData);
    if (parsedData.success) {
      updateItem(parsedData.data);
    } else {
      console.error(
        "Invalid feed item update event:",
        parsedData.error.flatten()
      );
    }
  }

  return {
    items,
    status,
    error,
  };
};
