export const useMockAdvertiser = () => {
  const mockAdvertiser: Advertiser = {
    id: "test-advertiser-123",
    agencyId: "test-agency-456", 
    agencyName: "Test Agency",
    advertiserId: "test-advertiser-123",
    amazonAmcAccountId: "amc-123",
    amazonDspAccountId: "dsp-123",
    amazonDspAdvertiserName: "Test Advertiser for UI Testing",
    amazonDspProfileId: "profile-123",
    affinitySeedAudienceId: "audience-123",
    amcUploadAwsId: "aws-123",
    amcInstanceId: "instance-123",
    cleanroomResultBucket: "bucket-123",
    amazonAdsApiUrl: "https://advertising-api.amazon.com",
    currencyCode: "USD",
    countryCode: "US",
    shopUrl: "https://test-shop.com",
    shopifyDataSource: false,
    adTagDataSource: true,
    billingMode: "CPM",
    amazonBillingBaseRateInCents: 1000,
    amazonBillingAudienceRateInCents: 500,
    enabled: true,
    enabledForCampaignCreation: true,
    timezone: "America/New_York",
  };

  const mockAdvertiserKnowledge: AdvertiserKnowledge[] = [
    {
      id: "targeting-knowledge-1",
      knowledgeType: KnowledgeType.TARGETING_SETTINGS,
      displayName: "Targeting Settings",
      description: "Organization targeting definitions for tailored campaigns",
      payload: {
        _type: KnowledgeItemPayloadType.JSON,
        remarketing: "Users who visited our website in the last 30 days and viewed product pages but did not make a purchase",
        retargeting: "Previous customers who made a purchase more than 60 days ago, focusing on cross-sell and upsell opportunities",
        NTB: "New-to-brand users who have never interacted with our products, identified through lookalike audiences based on our top customers",
        additionalInfo: "Focus on premium demographics in major metropolitan areas, exclude competitor brand searches, prioritize mobile users aged 25-45"
      },
      vectorize: true,
      ownerType: "ADVERTISER",
      ownerId: "test-advertiser-123",
      updatedAt: new Date("2024-01-15T10:30:00Z")
    },
    {
      id: "objectives-knowledge-1", 
      knowledgeType: KnowledgeType.CURRENT_OBJECTIVES_SETTINGS,
      displayName: "Business Objectives",
      description: "Current business objectives and goals",
      payload: {
        _type: KnowledgeItemPayloadType.JSON,
        objectives: "Q1 2024: Increase brand awareness by 25%, achieve 15% market share growth in premium segment, launch 3 new product lines with 10% of total revenue contribution"
      },
      vectorize: true,
      ownerType: "ADVERTISER", 
      ownerId: "test-advertiser-123",
      updatedAt: new Date("2024-01-10T14:20:00Z")
    },
    {
      id: "kpi-knowledge-1",
      knowledgeType: KnowledgeType.KPI_SETTINGS,
      displayName: "Funnel KPI's", 
      description: "Key performance indicators and metrics",
      payload: {
        _type: KnowledgeItemPayloadType.JSON,
        funnelKpis: [
          {
            funnelName: "Upper Funnel",
            kpis: [
              { kpiName: "Impressions", importance: 8 },
              { kpiName: "Reach", importance: 9 },
              { kpiName: "Brand Awareness", importance: 10 }
            ]
          },
          {
            funnelName: "Lower Funnel", 
            kpis: [
              { kpiName: "ROAS", importance: 10 },
              { kpiName: "Conversion Rate", importance: 9 },
              { kpiName: "CPA", importance: 8 }
            ]
          }
        ]
      },
      vectorize: false,
      ownerType: "ADVERTISER",
      ownerId: "test-advertiser-123", 
      updatedAt: new Date("2024-01-12T09:15:00Z")
    },
    {
      id: "dsp-strategy-knowledge-1",
      knowledgeType: KnowledgeType.DSP_STRATEGY_SETTINGS,
      displayName: "DSP Strategy",
      description: "Demand-side platform strategy and approach",
      payload: {
        _type: KnowledgeItemPayloadType.JSON,
        strategy: "Programmatic buying strategy focused on premium inventory, real-time bidding optimization, cross-device targeting with frequency capping at 3 impressions per user per day"
      },
      vectorize: true,
      ownerType: "ADVERTISER",
      ownerId: "test-advertiser-123",
      updatedAt: new Date("2024-01-08T16:45:00Z")
    }
  ];

  const mockAdvertisers: Advertiser[] = [
    mockAdvertiser,
    {
      ...mockAdvertiser,
      id: "test-advertiser-456",
      advertiserId: "test-advertiser-456", 
      amazonDspAdvertiserName: "另一个测试广告主",
      shopUrl: "https://another-test-shop.com",
    }
  ];

  return {
    mockAdvertiser,
    mockAdvertisers,
    mockAdvertiserKnowledge,
  };
};