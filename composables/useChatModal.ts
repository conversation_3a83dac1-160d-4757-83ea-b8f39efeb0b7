import { computed } from "vue";
import type { Component } from "vue";

export const useChatModal = (conversationId: string) => {
  if (!conversationId) {
    throw new Error("Conversation ID is required");
  }

  const conversationsStore = useConversationStore();

  const conversation = computed(() =>
    conversationsStore.getConversationById(conversationId)
  );

  const activeCanvasComponentType = computed(() => {
    return conversation.value?.activeCanvasComponentType ?? undefined;
  });

  const activeCanvasComponentProps = computed(() => {
    return conversation.value?.activeCanvasComponentProps ?? {};
  });

  const isOpen = computed(() => !!conversation.value?.activeCanvasComponentId);

  function openWith(
    componentType: Component,
    componentProps: Record<string, any>,
    componentId: string
  ) {
    conversationsStore.updateConversation(conversationId, {
      activeCanvasComponentType: componentType,
      activeCanvasComponentProps: componentProps,
      activeCanvasComponentId: componentId,
    });
  }

  function close() {
    conversationsStore.updateConversation(conversationId, {
      activeCanvasComponentType: undefined,
      activeCanvasComponentProps: undefined,
      activeCanvasComponentId: undefined,
    });
  }

  function isOpenForId(componentId: string) {
    return conversation.value?.activeCanvasComponentId === componentId;
  }

  return {
    isOpen,
    activeCanvasComponentType,
    activeCanvasComponentProps,
    isOpenForId,
    openWith,
    close,
  };
};
