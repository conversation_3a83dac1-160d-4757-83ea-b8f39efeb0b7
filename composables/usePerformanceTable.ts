import type { TreeNode } from "primevue/treenode";
import { PerformanceDataTransformer } from "~/utils/performance/dataTransformer";
import { TreeNodeBuilder } from "~/utils/performance/treeNodeBuilder";
import {
  usePerformanceData,
  type PerformanceDataProps,
} from "./usePerformanceData";

export type ViewType = "line-items" | "creatives" | "both";

export const viewTypeOptions = [
  { label: "Line Items", value: "line-items" as ViewType },
  { label: "Creatives", value: "creatives" as ViewType },
  { label: "Both", value: "both" as ViewType },
];

export function usePerformanceTable(props: PerformanceDataProps) {
  const advertiserStore = useAdvertiserStore();

  const {
    selectedMetricColumns,
    lineItemNames,
    creativeNames,
    creativeMetricsData,
    lineItemMetricsData,
    isLoading,
    hasError,
    errorMessage,
    status,
  } = usePerformanceData(props);

  const advertiser = computed(() =>
    advertiserStore.getAdvertiserById(props.advertiserId)
  );

  const searchQuery = ref("");
  const viewType = ref<ViewType>("both");
  const expandedRows = ref<Record<string, boolean>>({});

  const creativesByLineItem = computed(() =>
    PerformanceDataTransformer.collectCreativesByLineItem(
      creativeMetricsData.value
    )
  );

  const collectedLineItemMetrics = computed(() =>
    PerformanceDataTransformer.collectLineItemMetrics(lineItemMetricsData.value)
  );

  const tableData = computed<TreeNode[]>(() => {
    const currencyCode = advertiser.value?.currencyCode;

    switch (viewType.value) {
      case "line-items":
        return TreeNodeBuilder.createLineItemOnlyNodes(
          collectedLineItemMetrics.value,
          lineItemNames.value,
          currencyCode
        );
      case "creatives":
        return TreeNodeBuilder.createCreativeOnlyNodes(
          creativesByLineItem.value,
          creativeNames.value,
          currencyCode
        );
      case "both":
      default:
        return TreeNodeBuilder.createHierarchicalNodes(
          creativesByLineItem.value,
          collectedLineItemMetrics.value,
          lineItemNames.value,
          creativeNames.value,
          currencyCode
        );
    }
  });

  const filteredTableData = computed<TreeNode[]>(() => {
    if (!searchQuery.value || searchQuery.value.trim() === "") {
      return tableData.value;
    }

    const searchTerm = searchQuery.value.toLowerCase().trim();
    const results: TreeNode[] = [];

    for (const node of tableData.value) {
      const lineItemMatches = node.data.name.toLowerCase().includes(searchTerm);

      const matchingChildren =
        node.children?.filter((child) =>
          child.data.name.toLowerCase().includes(searchTerm)
        ) || [];

      if (lineItemMatches || matchingChildren.length > 0) {
        results.push({
          ...node,
          children: lineItemMatches ? node.children : matchingChildren,
        });
      }
    }

    return results;
  });

  const isAllExpanded = computed(
    () => Object.keys(expandedRows.value).length === tableData.value.length
  );

  const tableKey = ref(crypto.randomUUID());

  watch(
    filteredTableData,
    () => {
      tableKey.value = crypto.randomUUID();
    },
    { deep: true }
  );

  watch(
    viewType,
    () => {
      searchQuery.value = "";
    },
    { deep: true }
  );

  function expandCollapseAll() {
    expandedRows.value = isAllExpanded.value
      ? {}
      : Object.fromEntries(tableData.value.map((row) => [row.key, true]));
  }

  return {
    // Data
    selectedMetricColumns,
    filteredTableData,

    // UI State
    searchQuery,
    viewType,
    expandedRows,
    tableKey,
    isAllExpanded,

    // Actions
    expandCollapseAll,

    // Constants
    viewTypeOptions,

    // Additional data from usePerformanceData
    isLoading,
    hasError,
    errorMessage,
    status,
  };
}
