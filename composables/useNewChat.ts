import { z } from "zod";

import { MessagePayloadSchema } from "~/types/chat/api-schemas";
import { createLocalConversation } from "~/utils/chatUtils";

interface CreateConversationOptions {
  title?: string;
  messagePayload?: z.infer<typeof MessagePayloadSchema>;
  shouldNavigate?: boolean;
}

export const useNewChat = () => {
  const chatService = useChatService();
  const conversationStore = useConversationStore();

  const isBusy = ref(false);

  async function createConversation({
    title,
    messagePayload,
    shouldNavigate = true,
  }: CreateConversationOptions) {
    console.log("Chat: Creating new conversation", { title });

    isBusy.value = true;

    try {
      const conversation = await chatService.createConversation({
        title,
      });

      console.log("Chat: Conversation created", {
        conversationId: conversation.id,
      });

      const localConversation = createLocalConversation(conversation);
      localConversation.isNew = true;
      if (messagePayload) {
        localConversation.pendingMessage = messagePayload;
      }
      conversationStore.addConversation(localConversation);

      if (shouldNavigate) {
        await navigateToConversation(conversation.id);
      }
    } catch (error) {
      console.error("Chat: Failed to create conversation", error);
      throw error;
    } finally {
      isBusy.value = false;
    }
  }

  async function navigateToConversation(conversationId: string) {
    await navigateTo(`/chat/${conversationId}`);
  }

  return {
    isBusy,
    createConversation,
  };
};
