import type { ToastMessageOptions } from "primevue/toast";

export const tryWithToast = (
  fn: (...args: any[]) => Promise<void>,
  errorMapper?: (error: unknown) => string,
  severity: ToastMessageOptions["severity"] = "error",
  summary: ToastMessageOptions["summary"] = "Error",
  passedInToast?: ReturnType<typeof useToast>
) => {
  let toastInstance = passedInToast;

  async function execute(...args: any[]) {
    if (!toastInstance) {
      try {
        const nuxtApp = useNuxtApp();
        if (nuxtApp && nuxtApp.vueApp) {
          toastInstance = useToast();
        } else if (!passedInToast) {
          console.warn(
            "tryWithToast: Nuxt context not available or useToast failed. Toast notifications will be unavailable unless a toast instance was passed directly."
          );
        }
      } catch (e) {
        if (!passedInToast) {
          console.warn(
            "tryWithToast: Error during Nuxt toast initialization. Toast notifications may be unavailable unless a toast instance was passed directly. Error:",
            e
          );
        }
      }
    }

    try {
      await fn(...args);
    } catch (error) {
      console.error(error);

      let errorMessage;
      if (errorMapper) {
        errorMessage = errorMapper(error);
      } else {
        errorMessage = String(error);
      }

      if (!toastInstance) {
        console.error(
          `tryWithToast: Toast instance not available. Failed to show error message for "${summary}": "${errorMessage}"`
        );
        return;
      }

      toastInstance.add({
        severity,
        summary,
        detail: errorMessage,
        life: 3000,
      });
    }
  }

  return { execute };
};
