import type { z } from "zod";
import {
  MessagePayloadSchema,
  MessageDataSchema,
  PartEventSchema,
} from "~/types/chat/api-schemas";
import type {
  MessageStateExtension,
  StatefulMessage,
  StatefulUserMessage,
  StatefulAiMessage,
} from "~/types/chat/models";
import { ChatParticipant } from "~/types/chat/common";
import { applyChatStreamEvent } from "~/utils/chatStreamingUtils";

export const useExistingChat = (conversationId: string) => {
  if (!conversationId) {
    throw new Error("Conversation ID is required");
  }

  const conversationsStore = useConversationStore();
  const chatService = useChatService();
  const { user } = useUserSession();
  const serverEventsStore = useServerEventsStore();

  const streamingTimeouts = new Map<string, ReturnType<typeof setTimeout>>();
  const STREAMING_TIMEOUT_MS = 10 * 60 * 1000;

  const conversation = computed(() =>
    conversationsStore.getConversationById(conversationId)
  );

  const messages = computed(() => conversation.value?.messages || []);

  const isLoadingConversation = ref(false);
  const conversationLoadError = ref<Error | null>(null);

  const isMessagesBusy = computed(() =>
    messages.value.some((msg) => msg.state === "pending")
  );

  const messageErrors = computed(() => {
    const latestMessage = messages.value[messages.value.length - 1];
    if (
      latestMessage &&
      latestMessage.state === "error" &&
      "error" in latestMessage &&
      latestMessage.error
    ) {
      return [latestMessage.error];
    }
    return [];
  });

  const isBusy = computed(
    () => isLoadingConversation.value || isMessagesBusy.value
  );
  const error = computed(
    () => conversationLoadError.value || messageErrors.value[0] || null
  );

  async function initialize() {
    if (conversation.value?.isInitialized) {
      throw new Error("Chat is already initialized");
    }

    await loadConversationAndMessages();
    subscribeToConversationEvents();

    conversationsStore.updateConversation(conversationId, {
      isInitialized: true,
    });
  }

  async function destroy() {
    if (!conversation.value?.isInitialized) {
      throw new Error("Chat is not initialized");
    }

    await unsubscribeFromConversationEvents();
    clearAllStreamingTimeouts();

    conversationsStore.updateConversation(conversationId, {
      isInitialized: false,
    });
  }

  function startStreamingTimeout(messageId: string) {
    console.log("Chat: Starting streaming timeout", {
      messageId,
      timeoutMs: STREAMING_TIMEOUT_MS,
    });

    clearStreamingTimeout(messageId);

    const timeoutId = setTimeout(() => {
      console.error("Chat: Streaming timeout occurred", { messageId });
      handleStreamingTimeout(messageId);
    }, STREAMING_TIMEOUT_MS);

    streamingTimeouts.set(messageId, timeoutId);
  }

  function refreshStreamingTimeout(messageId: string) {
    console.log("Chat: Refreshing streaming timeout", { messageId });

    if (streamingTimeouts.has(messageId)) {
      startStreamingTimeout(messageId);
    }
  }

  function clearStreamingTimeout(messageId: string) {
    const timeoutId = streamingTimeouts.get(messageId);
    if (timeoutId) {
      console.log("Chat: Clearing streaming timeout", { messageId });
      clearTimeout(timeoutId);
      streamingTimeouts.delete(messageId);
    }
  }

  function clearAllStreamingTimeouts() {
    console.log("Chat: Clearing all streaming timeouts", {
      activeTimeouts: streamingTimeouts.size,
    });

    streamingTimeouts.forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });
    streamingTimeouts.clear();
  }

  function handleStreamingTimeout(messageId: string) {
    console.error("Chat: Message streaming timed out", {
      messageId,
      timeoutMs: STREAMING_TIMEOUT_MS,
    });

    updateMessageState(messageId, {
      state: "error",
      id: messageId,
      error: {
        code: "STREAMING_TIMEOUT",
        message: `Message streaming timed out. Please try again.`,
      },
    });

    clearStreamingTimeout(messageId);
  }

  async function subscribeToConversationEvents() {
    console.log("Chat: (re)subscribing to conversation events", {
      conversationId,
    });

    // clean up any old handler first
    serverEventsStore.off("chat", handleConversationChatEvent);

    try {
      await serverEventsStore.subscribeToTopic({
        topicLevel: "USER",
        topicIdentifier: conversationId,
      });

      serverEventsStore.on("chat", handleConversationChatEvent);

      console.log("Chat: Successfully subscribed to conversation events");
    } catch (error) {
      console.error("Chat: Failed to subscribe to conversation events", error);
    }
  }

  async function unsubscribeFromConversationEvents() {
    console.log("Chat: Unsubscribing from conversation events", {
      conversationId,
    });

    try {
      serverEventsStore.off("chat", handleConversationChatEvent);

      await serverEventsStore.unsubscribeFromTopic({
        topicLevel: "USER",
        topicIdentifier: conversationId,
      });

      console.log("Chat: Successfully unsubscribed from conversation events");
    } catch (error) {
      console.error(
        "Chat: Failed to unsubscribe from conversation events",
        error
      );
    }
  }

  function handleConversationChatEvent(event: MessageEvent) {
    console.log("Chat: Received chat event", event.data);

    try {
      const eventData = JSON.parse(event.data);
      const { success, data, error } = PartEventSchema.safeParse(eventData);

      if (!success) {
        console.error("Chat: Invalid event data received", error);
        return;
      }

      const latestPendingAiMessage = findLatestPendingAiMessage();
      if (!latestPendingAiMessage) {
        console.warn("Chat: No pending AI message found for event", {
          eventType: data.type,
          reason: "Message may have completed, timed out, or errored",
        });
        return;
      }

      if (latestPendingAiMessage.state !== "pending") {
        console.warn("Chat: Ignoring event for non-pending message", {
          eventType: data.type,
          messageId: latestPendingAiMessage.id,
          messageState: latestPendingAiMessage.state,
        });
        return;
      }

      applyStreamEventToMessage(latestPendingAiMessage, data);
    } catch (error) {
      console.error("Chat: Error processing chat event", error);
    }
  }

  function findLatestPendingAiMessage(): StatefulAiMessage | null {
    // Find the latest AI message in pending state
    const pendingAiMessages = messages.value
      .filter(
        (msg): msg is StatefulAiMessage =>
          msg.type === ChatParticipant.AI && msg.state === "pending"
      )
      .sort((a, b) => {
        // Sort by creation time if available, otherwise by position in array
        return messages.value.indexOf(b) - messages.value.indexOf(a);
      });

    return pendingAiMessages[0] || null;
  }

  function applyStreamEventToMessage(
    message: StatefulAiMessage,
    eventData: z.infer<typeof PartEventSchema>
  ) {
    console.log("Chat: Applying stream event to message", {
      messageId: message.id,
      eventType: eventData.type,
    });

    const updatedMessage = applyChatStreamEvent(message, eventData);

    const updatedMessages = messages.value.map((msg) =>
      msg.id === message.id ? updatedMessage : msg
    );

    conversationsStore.updateConversation(conversationId, {
      messages: updatedMessages,
    });

    refreshStreamingTimeout(message.id);

    if (eventData.type === "messageEnd") {
      console.log("Chat: Message streaming completed, persisting message", {
        messageId: updatedMessage.id,
      });

      clearStreamingTimeout(updatedMessage.id);

      handleStreamingComplete(updatedMessage);
    }

    if (eventData.type === "documentCreated") {
      console.log("Chat: Document created, adding to conversation", {
        instance: eventData.document.instance,
        version: eventData.document.version,
        documentName: eventData.document.title,
        conversationId,
      });

      const currentConversation = conversation.value;
      if (currentConversation) {
        const updatedDocuments = [
          ...(currentConversation.documents || []),
          {
            ...eventData.document,
          },
        ];
        conversationsStore.updateConversation(conversationId, {
          documents: updatedDocuments,
        });
      }
    }
  }

  async function handleStreamingComplete(message: StatefulAiMessage) {
    try {
      console.log("Chat: Persisting completed streaming message", {
        messageId: message.id,
        conversationId,
      });

      const persistedId = await persistAiMessageToRob(message);

      const finalMessage: StatefulAiMessage = {
        ...message,
        state: "persisted",
        id: persistedId,
      };

      replaceOptimisticMessage(message.id, finalMessage);

      console.log("Chat: Streaming message successfully persisted", {
        originalId: message.id,
        persistedId,
        conversationId,
      });
    } catch (error) {
      console.error("Chat: Failed to persist streaming message", {
        messageId: message.id,
        conversationId,
        error: error instanceof Error ? error.message : String(error),
      });

      clearStreamingTimeout(message.id);
      handleGenerationError(message.id, error as Error);
    }
  }

  function addOptimisticUserMessage(
    payload: z.infer<typeof MessagePayloadSchema>
  ): StatefulUserMessage {
    const tempId = generateTempId();
    const userMessage = createUserMessage(payload, tempId);

    conversationsStore.updateConversation(conversationId, {
      messages: [...messages.value, userMessage],
    });

    return userMessage;
  }

  function addOptimisticAiMessage(): StatefulAiMessage {
    const tempId = generateTempId();
    const aiMessage = createAiMessage(tempId);

    conversationsStore.updateConversation(conversationId, {
      messages: [...messages.value, aiMessage],
    });

    return aiMessage;
  }

  function updateMessageState(
    messageId: string,
    newState: MessageStateExtension
  ) {
    const updatedMessages = messages.value.map((msg: StatefulMessage) =>
      msg.id === messageId ? { ...msg, ...newState } : msg
    );

    conversationsStore.updateConversation(conversationId, {
      messages: updatedMessages,
    });
  }

  function replaceOptimisticMessage(
    tempId: string,
    persistedMessage: StatefulMessage
  ) {
    const updatedMessages = messages.value.map((msg: StatefulMessage) =>
      msg.id === tempId ? persistedMessage : msg
    );

    conversationsStore.updateConversation(conversationId, {
      messages: updatedMessages,
    });
  }

  async function persistUserMessageToRob(
    message: StatefulUserMessage
  ): Promise<string> {
    const messageToSend = {
      type: ChatParticipant.USER,
      content: message.content,
      formData: message.formData,
      checkpointId: message.checkpointId,
    } as z.infer<typeof MessageDataSchema>;

    const response = await chatService.addMessagesToConversation(
      conversationId,
      [messageToSend]
    );

    const messageId = response?.[0]?.messageId;
    if (!messageId) {
      throw new Error("Failed to persist user message");
    }

    console.log("Chat: User message persisted", {
      conversationId,
      messageId,
    });

    return messageId;
  }

  async function persistAiMessageToRob(
    message: StatefulAiMessage
  ): Promise<string> {
    const messageToSend = {
      type: ChatParticipant.AI,
      parts: message.parts,
      thoughtSteps: message.thoughtSteps,
    } as z.infer<typeof MessageDataSchema>;

    const response = await chatService.addMessagesToConversation(
      conversationId,
      [messageToSend]
    );

    const messageId = response?.[0]?.messageId;
    if (!messageId) {
      throw new Error("Failed to persist AI message");
    }

    console.log("Chat: AI message persisted", {
      conversationId,
      messageId,
    });

    return messageId;
  }

  function handlePersistenceError(messageId: string, error: Error) {
    console.error("Chat: Message persistence failed", {
      messageId,
      error: error.message,
    });

    updateMessageState(messageId, {
      state: "error",
      id: messageId,
      error: {
        code: "PERSISTENCE_FAILED",
        message: error.message,
      },
    });
  }

  function handleGenerationError(aiMessageId: string, error: Error) {
    console.error("Chat: AI generation failed", {
      aiMessageId,
      error: error.message,
    });

    clearStreamingTimeout(aiMessageId);

    updateMessageState(aiMessageId, {
      state: "error",
      id: aiMessageId,
      error: {
        code: "GENERATION_FAILED",
        message: error.message,
      },
    });
  }

  function removeLatestErrorMessage() {
    const latestErrorMessage = messages.value
      .slice()
      .reverse()
      .find((msg) => msg.state === "error");

    if (latestErrorMessage) {
      console.log("Chat: Removing latest error message", {
        messageId: latestErrorMessage.id,
      });
      const updatedMessages = messages.value.filter(
        (msg) => msg.id !== latestErrorMessage.id
      );
      conversationsStore.updateConversation(conversationId, {
        messages: updatedMessages,
      });
    }
  }

  async function handleUserMessageFlow(
    payload: z.infer<typeof MessagePayloadSchema>
  ): Promise<StatefulUserMessage> {
    const userMessage = addOptimisticUserMessage(payload);

    try {
      const persistedId = await persistUserMessageToRob(userMessage);

      const persistedUserMessage: StatefulUserMessage = {
        ...userMessage,
        state: "persisted",
        id: persistedId,
      };

      replaceOptimisticMessage(userMessage.id, persistedUserMessage);

      return persistedUserMessage;
    } catch (error) {
      handlePersistenceError(userMessage.id, error as Error);
      throw error;
    }
  }

  async function handleAiResponseFlow(
    userMessage: StatefulUserMessage,
    payload: z.infer<typeof MessagePayloadSchema>
  ): Promise<void> {
    if (!isMessagePersisted(userMessage)) {
      throw new Error(
        "User message must be persisted before generating AI response"
      );
    }

    const aiMessage = addOptimisticAiMessage();

    try {
      const response = await chatService.generateResponse(
        conversationId,
        user.value!.sub,
        userMessage.id,
        payload
      );

      if (response.status !== "ok") {
        throw new Error(
          `Streaming request failed with status: ${response.status}`
        );
      }

      console.log(
        "Chat: AI streaming initiated successfully, waiting for events",
        {
          conversationId,
          messageId: aiMessage.id,
          status: response.status,
        }
      );

      startStreamingTimeout(aiMessage.id);
    } catch (error) {
      handleGenerationError(aiMessage.id, error as Error);
      throw error;
    }
  }

  async function loadConversation() {
    const existingConversation =
      conversationsStore.getConversationById(conversationId);

    if (existingConversation?.isNew) {
      console.log("Chat: Skipping server fetch for new conversation", {
        conversationId,
        title: existingConversation.title,
      });
      return;
    }

    console.log("Chat: Loading conversation from server", {
      conversationId,
      userId: user.value?.sub,
    });

    try {
      const fetchedConversation = await chatService.getConversationById(
        conversationId
      );

      console.log("Chat: Conversation fetched", {
        conversationId,
        title: fetchedConversation.title,
      });

      if (existingConversation) {
        conversationsStore.updateConversation(conversationId, {
          ...fetchedConversation,
        });
      } else {
        conversationsStore.addConversation(
          createLocalConversation(fetchedConversation)
        );
      }

      console.log("Chat: Conversation loaded successfully", {
        conversationId,
        isNew: conversationsStore.getConversationById(conversationId)?.isNew,
      });
    } catch (err) {
      console.error("Chat: Failed to load conversation", {
        conversationId,
        userId: user.value?.sub,
        error: err instanceof Error ? err.message : String(err),
      });
      throw err;
    }
  }

  async function loadMessages() {
    const currentConversation =
      conversationsStore.getConversationById(conversationId);

    if (currentConversation?.isNew) {
      console.log("Chat: Skipping message loading for new conversation", {
        conversationId,
      });
      return;
    }

    console.log("Chat: Loading messages", {
      conversationId,
      userId: user.value?.sub,
    });

    try {
      const messagesResponse = await chatService.getMessagesForConversation(
        conversationId
      );

      console.log("Chat: Messages fetched", {
        conversationId,
        messageCount: messagesResponse.content.length,
        totalElements: messagesResponse.page.totalElements,
      });

      const transformedMessages: StatefulMessage[] =
        messagesResponse.content.map((msg) => ({
          ...transformMessage(msg.messageData, {
            state: "persisted",
            id: msg.id,
          }),
        }));

      const existingMessages = [...messages.value];
      const pendingMessages = existingMessages.filter(
        (msg) => msg.state === "pending"
      );
      const serverMessages = transformedMessages.filter(
        (msg) => msg.state === "persisted"
      );
      const mergedMessages = [...serverMessages, ...pendingMessages];

      conversationsStore.updateConversation(conversationId, {
        messages: mergedMessages,
        totalMessages: messagesResponse.page.totalElements,
        isNew: false,
      });

      console.log("Chat: Messages loaded successfully", {
        conversationId,
        finalMessageCount: transformedMessages.length,
      });
    } catch (err) {
      console.error("Chat: Failed to load messages", {
        conversationId,
        userId: user.value?.sub,
        error: err instanceof Error ? err.message : String(err),
      });
      throw err;
    }
  }

  async function loadConversationAndMessages() {
    if (isLoadingConversation.value) return;

    console.log("Chat: Loading conversation and messages", {
      conversationId,
      userId: user.value?.sub,
    });

    isLoadingConversation.value = true;
    conversationLoadError.value = null;

    try {
      await loadConversation();
      await loadMessages();
    } catch (err) {
      conversationLoadError.value = err as Error;
    } finally {
      isLoadingConversation.value = false;
    }
  }

  async function sendMessage(payload: z.infer<typeof MessagePayloadSchema>) {
    if (!user.value) {
      throw new Error("User not found");
    }

    if (isBusy.value) {
      console.warn("Chat: Skipping message send, chat is busy", {
        conversationId,
        userId: user.value.sub,
      });
      return;
    }

    console.log("Chat: Starting workorder flow", {
      conversationId,
      userId: user.value.sub,
    });

    removeLatestErrorMessage();

    isLoadingConversation.value = true;
    conversationLoadError.value = null;

    try {
      console.log("Chat: Calling workorder/run API");
      
      // 调用新的 workorder/run 接口
      const workorderRequest = {
        name: "Chat Request",
        prompt: payload.message,
        context_objects: {},
        run_async: true
      };

      const response = await $fetch("/relay/linda/workorder/run", {
        method: "POST",
        body: workorderRequest
      });

      console.log("Chat: Workorder completed successfully", response);
      
      // 刷新整个页面
      if (typeof window !== 'undefined') {
        window.location.reload();
      }

    } catch (err) {
      console.error("Chat: Workorder failed", {
        conversationId,
        userId: user.value.sub,
        error: err instanceof Error ? err.message : String(err),
      });
      conversationLoadError.value = err as Error;
    } finally {
      isLoadingConversation.value = false;
    }
  }

  return {
    conversation,
    messages,
    isBusy,
    error,
    sendMessage,
    initialize,
    destroy,
  };
};
