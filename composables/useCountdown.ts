const useCountdown = () => {
  const countdown = ref(0);
  let startStamped: Date | undefined;
  let interval: NodeJS.Timeout | undefined;
  let from = 0;

  const start = (seconds: number) => {
    startStamped = new Date();
    from = seconds;
    countdown.value = from;
    interval = setInterval(() => {
      tick();
      if (countdown.value <= 0) stop();
    }, 1000);
  };

  const stop = () => {
    clearInterval(interval);
    countdown.value = 0;
    startStamped = undefined;
    interval = undefined;
  };

  const tick = () => {
    const now = new Date();
    countdown.value = Math.ceil(
      from - (now.getTime() - startStamped!.getTime()) / 1000
    );
  };

  return {
    countdown,
    isRunning: computed(() => countdown.value > 0),
    start,
    stop,
  };
};

export default useCountdown;
