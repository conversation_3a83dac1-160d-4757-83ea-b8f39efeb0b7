import {
  computedAsync,
  StorageSerializers,
  useLocalStorage,
} from "@vueuse/core";
import {
  DataRequirement,
  RequirementsFactory,
} from "~/utils/advertiser-datasets";
import type {
  PerLineItemCreativeMetrics,
  PerLineItemMetrics,
} from "~/utils/advertiser-datasets/metrics/types";
import { DEFAULT_METRICS_PER_FUNNEL_TYPE } from "~/utils/advertiser-datasets/metrics/constants";
import { useAdvertiserDatasetStore } from "~/stores/advertiser-dataset-store";
import { hashKeySync } from "~/utils/hashUtils";

export type PerformanceDataProps = {
  advertiserId: string;
  campaignId: string;
  funnel: string | null;
  startDate: Date;
  endDate: Date;
  viewType: "day" | "week";
};

export type DataStatus = "idle" | "loading" | "success" | "error";

function getUserFriendlyErrorMessage(
  error: unknown,
  dataType: "lineItems" | "creatives" | "metrics"
): string {
  const errorStr = String(error);

  // Network/connectivity errors
  if (
    errorStr.includes("fetch") ||
    errorStr.includes("network") ||
    errorStr.includes("NetworkError")
  ) {
    return `Unable to load ${
      dataType === "lineItems"
        ? "line item names"
        : dataType === "creatives"
        ? "creative names"
        : "performance metrics"
    }.`;
  }

  // Authentication errors
  if (
    errorStr.includes("401") ||
    errorStr.includes("unauthorized") ||
    errorStr.includes("Unauthorized")
  ) {
    return `Authentication failed while loading ${
      dataType === "lineItems"
        ? "line item names"
        : dataType === "creatives"
        ? "creative names"
        : "performance metrics"
    }.`;
  }

  // Permission errors
  if (
    errorStr.includes("403") ||
    errorStr.includes("forbidden") ||
    errorStr.includes("Forbidden")
  ) {
    return `Access denied to ${
      dataType === "lineItems"
        ? "line item data"
        : dataType === "creatives"
        ? "creative data"
        : "performance metrics"
    }.`;
  }

  // Not found errors
  if (
    errorStr.includes("404") ||
    errorStr.includes("not found") ||
    errorStr.includes("Not Found")
  ) {
    return `${
      dataType === "lineItems"
        ? "Line item names"
        : dataType === "creatives"
        ? "Creative names"
        : "Performance metrics"
    } not found.`;
  }

  // Server errors
  if (
    errorStr.includes("500") ||
    errorStr.includes("502") ||
    errorStr.includes("503") ||
    errorStr.includes("504")
  ) {
    return `Server error loading ${
      dataType === "lineItems"
        ? "line item names"
        : dataType === "creatives"
        ? "creative names"
        : "performance metrics"
    }.`;
  }

  // Timeout errors
  if (errorStr.includes("timeout") || errorStr.includes("Timeout")) {
    return `Request timed out loading ${
      dataType === "lineItems"
        ? "line item names"
        : dataType === "creatives"
        ? "creative names"
        : "performance metrics"
    }.`;
  }

  // Data-specific fallbacks
  switch (dataType) {
    case "lineItems":
      return `Failed to load line item names.`;
    case "creatives":
      return `Failed to load creative names.`;
    case "metrics":
      return `Failed to load performance metrics.`;
    default:
      return `Failed to load data.`;
  }
}

export function usePerformanceData(props: PerformanceDataProps) {
  const { user } = useUserSession();
  const advertiserDatasetStore = useAdvertiserDatasetStore();

  // Get default metrics based on funnel
  const defaultMetricsForFunnel = computed(() => {
    if (!props.funnel) return [];

    const funnelConfig = DEFAULT_METRICS_PER_FUNNEL_TYPE.find((config) =>
      config.funnels.has(props.funnel!)
    );

    return funnelConfig?.metrics || [];
  });

  const selectedMetricColumns = useLocalStorage<string[]>(
    hashKeySync(
      `${user.value?.sub}:${props.campaignId}:performance-table:metrics-in-pool`
    ),
    [],
    { serializer: StorageSerializers.object, initOnMounted: true }
  );

  // Initialize metrics based on funnel when it becomes available
  watch(
    [() => props.funnel, defaultMetricsForFunnel],
    ([funnel, defaultMetrics]) => {
      // If funnel is available and no metrics are set yet, initialize with defaults
      if (
        funnel &&
        defaultMetrics.length > 0 &&
        selectedMetricColumns.value.length === 0
      ) {
        selectedMetricColumns.value = [...defaultMetrics];
      }
    },
    { immediate: true }
  );

  // Loading states for each data type
  const lineItemNamesLoading = shallowRef(false);
  const creativeNamesLoading = shallowRef(false);
  const metricsDataLoading = shallowRef(false);

  // Error states
  const lineItemNamesError = shallowRef<string | null>(null);
  const creativeNamesError = shallowRef<string | null>(null);
  const metricsDataError = shallowRef<string | null>(null);

  const lineItemNames = computedAsync(
    async () => {
      const requirement = new DataRequirement({
        datasets: ["line-item-names"],
        args: {
          advertiserId: props.advertiserId,
          campaignId: props.campaignId,
        },
      });

      try {
        lineItemNamesError.value = null;
        return await advertiserDatasetStore.fulfillRequirement(requirement);
      } catch (error) {
        // Log the real technical error
        console.error(`Technical error loading line item names:`, error);

        // Set user-friendly error message
        const userFriendlyMessage = getUserFriendlyErrorMessage(
          error,
          "lineItems"
        );
        lineItemNamesError.value = userFriendlyMessage;
        return {};
      }
    },
    {},
    { evaluating: lineItemNamesLoading }
  );

  const creativeNames = computedAsync(
    async () => {
      const requirement = new DataRequirement({
        datasets: ["creative-names"],
        args: {
          advertiserId: props.advertiserId,
          campaignId: props.campaignId,
        },
      });

      try {
        creativeNamesError.value = null;
        return await advertiserDatasetStore.fulfillRequirement(requirement);
      } catch (error) {
        // Log the real technical error
        console.error(`Technical error loading creative names:`, error);

        // Set user-friendly error message
        const userFriendlyMessage = getUserFriendlyErrorMessage(
          error,
          "creatives"
        );
        creativeNamesError.value = userFriendlyMessage;
        return {};
      }
    },
    {},
    { evaluating: creativeNamesLoading }
  );

  const creativeDataRequirementsMap = computed(() =>
    Object.fromEntries(
      selectedMetricColumns.value.map((metric) => [
        metric,
        RequirementsFactory.createRequirement("per-line-item-creative-metric", {
          advertiserId: props.advertiserId,
          campaignId: props.campaignId,
          startDate: props.startDate,
          endDate: props.endDate,
          metricName: metric,
          viewType: props.viewType,
        }),
      ])
    )
  );

  const lineItemDataRequirementsMap = computed(() =>
    Object.fromEntries(
      selectedMetricColumns.value.map((metric) => [
        metric,
        RequirementsFactory.createRequirement("per-line-item-metric", {
          advertiserId: props.advertiserId,
          campaignId: props.campaignId,
          startDate: props.startDate,
          endDate: props.endDate,
          metricName: metric,
          viewType: props.viewType,
        }),
      ])
    )
  );

  const creativeMetricsData = computedAsync<
    Record<string, PerLineItemCreativeMetrics | null>
  >(
    async () => await getMetricsData(creativeDataRequirementsMap.value),
    {},
    { evaluating: metricsDataLoading }
  );

  const lineItemMetricsData = computedAsync<
    Record<string, PerLineItemMetrics | null>
  >(
    async () => await getMetricsData(lineItemDataRequirementsMap.value),
    {},
    { evaluating: metricsDataLoading }
  );

  // Overall status computed properties
  const isLoading = computed(
    () =>
      lineItemNamesLoading.value ||
      creativeNamesLoading.value ||
      metricsDataLoading.value
  );

  const hasError = computed(
    () =>
      !!lineItemNamesError.value ||
      !!creativeNamesError.value ||
      !!metricsDataError.value
  );

  const errorMessage = computed(() => {
    const errors = [
      lineItemNamesError.value,
      creativeNamesError.value,
      metricsDataError.value,
    ].filter(Boolean);
    return errors.length > 0 ? errors.join(" ") : null;
  });

  const status = computed<DataStatus>(() => {
    if (hasError.value) return "error";
    if (isLoading.value) return "loading";
    if (selectedMetricColumns.value.length === 0) return "idle";
    return "success";
  });

  async function getMetricsData(
    dataRequirementsMap: Record<string, DataRequirement>
  ) {
    try {
      metricsDataError.value = null;

      if (Object.values(dataRequirementsMap).some((r) => !r.isValid())) {
        return {};
      }

      return Object.fromEntries(
        await Promise.all(
          Object.entries(dataRequirementsMap).map(
            async ([metric, requirement]) => {
              const data = await fulfillRequirement(metric, requirement);
              return [metric, data];
            }
          )
        )
      );
    } catch (error) {
      // Log the real technical error
      console.error(`Technical error loading metrics data:`, error);

      // Set user-friendly error message
      const userFriendlyMessage = getUserFriendlyErrorMessage(error, "metrics");
      metricsDataError.value = userFriendlyMessage;
      return {};
    }
  }

  function fulfillRequirement(metric: string, requirement: DataRequirement) {
    try {
      return advertiserDatasetStore.fulfillRequirement(requirement);
    } catch (error) {
      console.error(
        `Error fulfilling requirement for metric ${metric}:`,
        error
      );
      return null;
    }
  }

  return {
    selectedMetricColumns,
    lineItemNames,
    creativeNames,
    creativeMetricsData,
    lineItemMetricsData,

    // Status properties
    isLoading,
    hasError,
    errorMessage,
    status,

    // Individual loading states (for more granular control if needed)
    lineItemNamesLoading,
    creativeNamesLoading,
    metricsDataLoading,
  };
}
