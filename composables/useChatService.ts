import type { z } from "zod";

import {
  AddMessagesToConversationRequestSchema,
  AddMessagesToConversationResponseSchema,
  CreateConversationRequestSchema,
  CreateConversationResponseSchema,
  GenerateResponseRequestSchema,
  GenerateStreamResponseResponseSchema,
  GetConversationByIdResponseSchema,
  GetConversationsResponseSchema,
  GetMessagesForConversationPagedResponseSchema,
  MessageDataSchema,
  DocumentSchema,
  MessagePayloadSchema,
} from "~/types/chat/api-schemas";
import { ChatParticipant } from "~/types/chat/common";

export const useChatService = () => {
  const { activeAgencyScopeHeader } = useScopeHeader();
  const { user } = useUserSession();

  async function getConversations() {
    console.log("ChatService: Fetching conversations");

    const response = await $fetch(`/relay/rob/api/v2/chat/conversations`, {
      headers: { ...activeAgencyScopeHeader.value },
    });

    const conversations = GetConversationsResponseSchema.parse(response);

    console.log("ChatService: Conversations fetched for user", {
      count: conversations.length,
      user: user.value?.sub,
    });
    return conversations;
  }

  async function getConversationById(conversationId: string) {
    if (!conversationId || conversationId.trim() === "") {
      throw new Error("Conversation ID is required");
    }

    console.log("ChatService: Fetching conversation", { conversationId });

    const response = await $fetch(`/relay/rob/api/v2/chat/${conversationId}`, {
      headers: { ...activeAgencyScopeHeader.value },
    });

    const conversation = GetConversationByIdResponseSchema.parse(response);
    console.log("ChatService: Conversation fetched", {
      conversationId,
    });

    return conversation;
  }

  async function getMessagesForConversation(
    conversationId: string,
    page: number = 0
  ) {
    console.log("ChatService: Fetching messages for conversation", {
      conversationId,
      page,
    });

    const response = await $fetch(
      `/relay/rob/api/v2/chat/${conversationId}/messages`,
      {
        query: { page, sort: "timestamp,asc", limit: 100},
        headers: { ...activeAgencyScopeHeader.value },
      }
    );

    const result =
      GetMessagesForConversationPagedResponseSchema.parse(response);

    console.log("ChatService: Messages fetched", {
      conversationId,
      page,
      newMessagesCount: result.content.length,
    });

    return result;
  }

  async function createConversation({
    title,
    tags = [],
  }: Partial<z.infer<typeof CreateConversationRequestSchema>>) {
    if (!user.value) {
      throw new Error("User not found");
    }

    const request = {
      title,
      tags,
      ownerId: user.value.sub,
      ownerType: "USER",
    };

    const parsedRequest = CreateConversationRequestSchema.parse(request);

    console.log("ChatService: Creating conversation", { title });

    const response = await $fetch(`/relay/linda/workorder/run`, {
      method: "POST",
      body: parsedRequest,
      headers: { ...activeAgencyScopeHeader.value },
    });

    const conversation = CreateConversationResponseSchema.parse({
      id: response.id,
      ownerId: response.ownerId,
      ownerType: "USER",
      title: response.title,
      createdAt: response.createdAt,
      updatedAt: response.updatedAt,
    });

    console.log("ChatService: Conversation created", {
      conversationId: conversation.id,
      title: conversation.title,
    });

    return conversation;
  }

  function deleteConversation(conversationId: string): Promise<void> {
    return $fetch(`/relay/rob/api/v2/chat/${conversationId}`, {
      method: "DELETE",
      headers: { ...activeAgencyScopeHeader.value },
    });
  }

  async function addMessagesToConversation(
    conversationId: string,
    messages: z.infer<typeof MessageDataSchema>[]
  ) {
    console.log("ChatService: Adding messages", {
      conversationId,
      messagesCount: messages.length,
    });

    const parsedRequest =
      AddMessagesToConversationRequestSchema.parse(messages);

    const response = await $fetch(
      `/relay/rob/api/v2/chat/${conversationId}/messages`,
      {
        method: "POST",
        body: parsedRequest,
        headers: { ...activeAgencyScopeHeader.value },
      }
    );

    const result = AddMessagesToConversationResponseSchema.parse(response);
    console.log("ChatService: Messages added", {
      conversationId,
      messagesCount: result.length,
    });

    return result;
  }

  async function generateResponse(
    conversationId: string,
    clientId: string,
    messageId: string,
    payload: z.infer<typeof MessagePayloadSchema>,
    checkpointId?: string
  ) {
    console.log("ChatService: Generating AI response", {
      conversationId,
      messageId,
    });

    const { success, data, error } = GenerateResponseRequestSchema.safeParse({
      conversationId,
      clientId,
      messageId,
      participant: ChatParticipant.USER,
      payload,
      checkpointId,
      timestamp: new Date(),
    });
    if (!success) {
      console.error("ChatService: Invalid Linda request", error);
      throw new Error("Invalid message request");
    }

    const response = await $fetch("/relay/linda/stream_chat", {
      method: "POST",
      body: data,
    });

    const result = GenerateStreamResponseResponseSchema.parse(response);
    console.log("ChatService: AI streaming generation request accepted", {
      conversationId,
      messageId,
      status: result.status,
    });

    return result;
  }

  async function getChatDocument(conversationId: string, instance: number, version: number) {
    return $fetch<z.infer<typeof DocumentSchema>>(`/relay/rob/api/v2/chat/${conversationId}/documents/${instance}/versions/${version}`);
  }

  async function updateChatMessage(conversationId: string, messageId: string, messageData: z.infer<typeof MessageDataSchema>) {
    return $fetch<void>(`/relay/rob/api/v2/chat/${conversationId}/messages/${messageId}`, {
      method: "PATCH",
      body: messageData,
    });
  }

  async function updateMessageParts(
    conversationId: string, 
    messageId: string, 
    currentMessage: any,
    partModifier: (parts: any[]) => any[]
  ) {
    console.log("ChatService: Updating message parts", {
      conversationId,
      messageId,
    });

    const updatedParts = partModifier([...currentMessage.parts]);
    
    const messageData = {
      type: currentMessage.type,
      parts: updatedParts,
      thoughtSteps: currentMessage.thoughtSteps || [],
    };

    await updateChatMessage(conversationId, messageId, messageData);
    
    // Update local store after successful server update
    const conversationStore = useConversationStore();
    conversationStore.updateMessageParts(conversationId, messageId, updatedParts);
  }

  return {
    getConversations,
    getConversationById,
    getMessagesForConversation,
    createConversation,
    deleteConversation,
    addMessagesToConversation,
    generateResponse,
    getChatDocument,
    updateChatMessage,
    updateMessageParts,
  };
};
