async function refreshToken() {
  console.info("refreshing token");
  const session = useUserSession();

  if (!session.loggedIn.value) return;

  try {
    const { ok } = await $fetch<{ ok: boolean }>("/api/auth/refresh", {
      method: "POST",
    });

    if (!ok) {
      await navigateTo("/login");
    }
    await session.fetch();
  } catch (error) {
    console.error(error);
    await session.clear();
    await navigateTo("/login");
  }
}

export const useRefreshTokenInitializer = () => {
  if (!import.meta.client) return;

  const isInitialized = useState("isRefreshTokenInitialized", () => false);
  if (isInitialized.value)
    throw new Error("Refresh token initializer already initialized");

  let interval: NodeJS.Timeout;
  const { loggedIn, user } = useUserSession();

  watch(
    loggedIn,
    async (newLoggedIn, oldLoggedIn) => {
      if (newLoggedIn && !oldLoggedIn) {
        console.info("setting refresh token interval");
        await refreshToken();
        interval = setInterval(async () => {
          await refreshToken();
        }, user.value!.exp / 2);
      }

      if (!newLoggedIn && oldLoggedIn) {
        console.info("clearing refresh token interval");
        clearInterval(interval);
      }
    },
    { immediate: true }
  );
};
