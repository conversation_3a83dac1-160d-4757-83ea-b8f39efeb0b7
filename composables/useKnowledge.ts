export const useKnowledge = () => {
  const robClient = useRob();

  async function getAgencyKnowledge(
    agencyId: string
  ): Promise<[AgencyKnowledge[], Error | null]> {
    try {
      const filters: AgencyKnowledgeTypes[] = [
        KnowledgeType.NAMING_SETTINGS,
        KnowledgeType.LINE_ITEM_SETTINGS,
        KnowledgeType.AGENCY_ADDITIONAL_INFO,
      ];

      const scope: ScopeFilter = {
        agencyIds: [agencyId],
      };

      const knowledgeItems = (await robClient.getKnowledge(
        filters,
        scope
      )) as AgencyKnowledge[];

      return [knowledgeItems, null];
    } catch (error) {
      console.error(error);
      return [[], Error("Failed to get agency knowledge")];
    }
  }

  async function getKnowledgeFiles(
    scopeFilter?: ScopeFilter
  ): Promise<[KnowledgeItem<KnowledgeType.FILE>[], Error | null]> {
    try {
      const typeFilter = [KnowledgeType.FILE];
      const knowledgeFiles = (await robClient.getKnowledge(
        typeFilter,
        scopeFilter
      )) as KnowledgeItem<KnowledgeType.FILE>[];

      return [knowledgeFiles, null];
    } catch (error) {
      console.error(error);
      return [[], Error("Failed to get knowledge files")];
    }
  }

  async function getAdvertiserKnowledge(
    advertiserId: string
  ): Promise<[AdvertiserKnowledge[], Error | null]> {
    const filters: AdvertiserKnowledgeTypes[] = [
      KnowledgeType.KPI_SETTINGS,
      KnowledgeType.CURRENT_OBJECTIVES_SETTINGS,
      KnowledgeType.DSP_STRATEGY_SETTINGS,
      KnowledgeType.ASIN_GROUPING_SETTINGS,
      KnowledgeType.COMPETITORS_ASINS_SETTINGS,
      KnowledgeType.BRAND_SAFETY_SETTINGS,
      KnowledgeType.TARGETING_SETTINGS,
    ];

    const scope: ScopeFilter = {
      advertiserIds: [advertiserId],
    };

    const advertiserKnowledge = (await robClient.getKnowledge(
      filters,
      scope
    )) as AdvertiserKnowledge[];

    return [advertiserKnowledge, null];
  }

  async function batchSetKnowledge({
    add,
    remove,
    update,
  }: {
    add?: KnowledgeItemRequest[];
    remove?: KnowledgeItemRequest[];
    update?: KnowledgeItemRequest[];
  }): Promise<void> {
    await robClient.batchSetKnowledge(add ?? [], remove ?? [], update ?? []);
  }

  async function deleteKnowledgeFile(fileId: string): Promise<void> {
    await robClient.deleteFile(fileId);
  }

  async function createKnowledgeForOwner(
    types: KnowledgeType[],
    ownerId: string
  ): Promise<void> {
    const items = types.map((type) => createKnowledgeItem(type, ownerId));
    await batchSetKnowledge({ add: items });
  }

  return {
    getAgencyKnowledge,
    getAdvertiserKnowledge,
    getKnowledgeFiles,
    batchSetKnowledge,
    deleteKnowledgeFile,
    createKnowledgeForOwner,
  };
};
