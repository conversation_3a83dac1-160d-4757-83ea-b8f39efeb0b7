import { ref, computed, watch, type Ref } from "vue";
import type { DataTablePageEvent } from "primevue/datatable";
import { useDebounceFn } from "@vueuse/core";

// Define the campaign interface based on the API response
export interface Campaign {
  campaignId: string;
  advertiserId: string;
  campaignName: string;
  advertiserName: string;
  startDate: string;
  endDate: string;
  budget?: number;
  pacingPercentage?: number;
  status: string;
  funnelStage: string;
  updatedAt: string;
}

export interface PageInfo {
  size: number;
  number: number;
  totalElements: number;
  totalPages: number;
}

export interface CampaignsResponse {
  content: Campaign[];
  page: PageInfo;
}

export interface CampaignFilters {
  searchQuery: string;
  selectedStatuses: string[];
  selectedFunnelTactics: string[];
  sortBy: string;
  sortDir: "asc" | "desc";
  pageSize: number;
}

export function useCampaigns(advertiserIds: Ref<string[]>, storageKey?: string) {
  const { getCampaigns } = useRob();

  // State
  const campaigns = ref<Campaign[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const currentPage = ref(0);
  const pageSize = ref(10);
  const totalPages = ref(0);
  const totalElements = ref(0);

  // Search and filter state
  const searchQuery = ref("");
  const selectedStatuses = ref<string[]>([]);
  const selectedFunnelTactics = ref<string[]>([]);

  // Persistence functions
  const saveStateToStorage = () => {
    if (!storageKey || typeof window === 'undefined') return;
    
    const state = {
      searchQuery: searchQuery.value,
      selectedStatuses: selectedStatuses.value,
      selectedFunnelTactics: selectedFunnelTactics.value,
      sortBy: sortBy.value,
      sortDir: sortDir.value,
      pageSize: pageSize.value,
    };
    
    localStorage.setItem(storageKey, JSON.stringify(state));
  };

  const restoreStateFromStorage = () => {
    if (!storageKey || typeof window === 'undefined') return;
    
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const state = JSON.parse(stored);
        
        // Restore state with fallbacks to current values
        searchQuery.value = state.searchQuery ?? searchQuery.value;
        selectedStatuses.value = state.selectedStatuses ?? selectedStatuses.value;
        selectedFunnelTactics.value = state.selectedFunnelTactics ?? selectedFunnelTactics.value;
        sortBy.value = state.sortBy ?? sortBy.value;
        sortDir.value = state.sortDir ?? sortDir.value;
        pageSize.value = state.pageSize ?? pageSize.value;
      }
    } catch (error) {
      console.warn('Failed to restore campaigns state from localStorage:', error);
    }
  };

  // Caching
  const pageCache = ref(new Map<string, Campaign[]>());
  const loadingPages = ref(new Set<string>());

  // Sorting state
  const sortBy = ref("campaignName");
  const sortDir = ref<"asc" | "desc">("asc");

  // Restore state on initialization (after all state variables are declared)
  restoreStateFromStorage();

  // Generate cache key
  const getCacheKey = (
    advertiserIds: string[],
    page: number,
    size: number,
    sortBy: string,
    sortDir: string,
    statuses: string[],
    funnelStages: string[],
    searchQuery: string
  ) => {
    return `${advertiserIds
      .sort()
      .join(",")}-${page}-${size}-${sortBy}-${sortDir}-${statuses
      .sort()
      .join(",")}-${funnelStages.sort().join(",")}-${searchQuery}`;
  };

  // Clear cache when advertiser selection changes
  const clearCache = () => {
    pageCache.value.clear();
    loadingPages.value.clear();
    currentPage.value = 0;
    totalPages.value = 0;
    totalElements.value = 0;
  };

  // Watch for table options changes
  watch(
    [
      pageSize,
      sortBy,
      sortDir,
      selectedStatuses,
      selectedFunnelTactics,
      advertiserIds
    ],
    async () => {
      clearCache();
      if (advertiserIds.value.length > 0) {
        await fetchPageDebouncedButtons(0);
      } else {
        campaigns.value = [];
      }
    },
    { deep: true, immediate: true }
  );

  // Watch for table options changes
  watch(searchQuery,
    async () => {
      clearCache();
      if (advertiserIds.value.length > 0) {
        await fetchPageDebouncedSearch(0);
      } else {
        campaigns.value = [];
      }
    },
    { deep: true, immediate: true }
  );

  // Watch for state changes to persist to localStorage
  if (storageKey) {
    watch(
      [searchQuery, selectedStatuses, selectedFunnelTactics, sortBy, sortDir, pageSize], 
      saveStateToStorage, 
      { deep: true }
    );
  }

  // Fetch a specific page (internal function)
  async function _fetchPage(page: number, prefetch = false) {
    const cacheKey = getCacheKey(
      advertiserIds.value,
      page,
      pageSize.value,
      sortBy.value,
      sortDir.value,
      selectedStatuses.value,
      selectedFunnelTactics.value,
      searchQuery.value
    );

    // Return cached data if available
    if (pageCache.value.has(cacheKey)) {
      if (!prefetch) {
        campaigns.value = pageCache.value.get(cacheKey)!;
        currentPage.value = page;
      }
      return;
    }

    // Prevent duplicate requests
    if (loadingPages.value.has(cacheKey)) {
      return;
    }

    loadingPages.value.add(cacheKey);

    if (!prefetch) {
      // Only set loading if we don't have any data yet
      if (campaigns.value.length === 0) {
        loading.value = true;
      }
      error.value = null;
    }

    try {
      const response = (await getCampaigns(advertiserIds.value, {
        page,
        size: pageSize.value,
        sortBy: sortBy.value,
        sortDir: sortDir.value,
        statuses: selectedStatuses.value,
        funnelStages: selectedFunnelTactics.value,
        campaignNameKeywords: searchQuery.value,
      })) as CampaignsResponse;

      // Cache the results
      pageCache.value.set(cacheKey, response.content);

      if (!prefetch) {
        campaigns.value = response.content;
        currentPage.value = page;
        totalPages.value = response.page.totalPages;
        totalElements.value = response.page.totalElements;
        error.value = null;

        // Prefetch next page if available
        if (page < response.page.totalPages - 1) {
          setTimeout(() => _fetchPage(page + 1, true), 100);
        }
      }
    } catch (err: any) {
      console.error("Error fetching campaigns:", err);
      if (!prefetch) {
        error.value = err.message || "Failed to load campaigns";
        // Don't clear campaigns on error unless it's the first load
        if (campaigns.value.length === 0) {
          campaigns.value = [];
        }
      }
    } finally {
      loadingPages.value.delete(cacheKey);
      if (!prefetch) {
        loading.value = false;
      }
    }
  }

  // Debounced fetch to prevent spam requests
  const fetchPageDebouncedSearch = useDebounceFn(_fetchPage, 300);
  const fetchPageDebouncedButtons = useDebounceFn(_fetchPage, 100);

  // Toggle sorting
  const toggleSort = (column: string) => {
    if (sortBy.value === column) {
      sortDir.value = sortDir.value === "asc" ? "desc" : "asc";
    } else {
      sortBy.value = column;
      sortDir.value = "asc";
    }
  };

  // Handle page changes from built-in paginator
  const onPageChange = (event: DataTablePageEvent) => {
    const newPage = Math.floor(event.first / event.rows);
    const newPageSize = event.rows;

    // If page size changed, update it and let the watcher handle the refetch
    if (newPageSize !== pageSize.value) {
      pageSize.value = newPageSize;
      return; // Let the pageSize watcher handle the reset and refetch
    }

    // Only handle page navigation if page size didn't change
    if (newPage !== currentPage.value) {
      _fetchPage(newPage);
    }
  };

  // Computed properties
  const clearFilters = () => {
    selectedStatuses.value = [];
    selectedFunnelTactics.value = [];
    // Save state after clearing filters
    if (storageKey) {
      saveStateToStorage();
    }
  };

  // Helper function to create sortable header
  const createSortableHeader = (label: string, field: string) => {
    return {
      label,
      field,
      isActive: computed(() => sortBy.value === field),
      icon: computed(() =>
        sortDir.value === "asc" ? "arrow_upward" : "arrow_downward"
      ),
      onClick: () => toggleSort(field),
    };
  };

  return {
    // State
    campaigns,
    loading,
    error,
    currentPage,
    pageSize,
    totalPages,
    totalElements,
    searchQuery,
    selectedStatuses,
    selectedFunnelTactics,
    sortBy,
    sortDir,
    // Methods
    toggleSort,
    onPageChange,
    clearCache,
    clearFilters,
    createSortableHeader,
  };
}
