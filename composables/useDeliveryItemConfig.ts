import {
  DeliveryItemCategorySchema,
  DeliveryItemTypeSchema,
} from "~/types/delivery-item/delivery-item";
import {
  DeliveryItemTimeFilter,
  DeliveryItemSortOption,
} from "~/types/delivery-item/filter";
import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";
import type { DeliveryConfig } from "~/types/delivery-item/config";
import * as actionPayloadFactory from "~/utils/delivery-action-factory/factory";

export const timeFilterConfig: Record<DeliveryItemTimeFilter, string> = {
  [DeliveryItemTimeFilter.LAST_24_HOURS]: "Last 24 hours",
  [DeliveryItemTimeFilter.LAST_7_DAYS]: "Last 7 days",
  [DeliveryItemTimeFilter.LAST_30_DAYS]: "Last 30 days",
  [DeliveryItemTimeFilter.ALL_TIME]: "All time",
};

export const sortOptionConfig: Record<DeliveryItemSortOption, string> = {
  [DeliveryItemSortOption.RELEVANCE]: "Attention Needed",
  [DeliveryItemSortOption.CHRONOLOGICAL]: "Chronological",
};

export const useDeliveryItemConfig = () => {
  const robClient = useRob();

  const deliveryItemConfig = ref<DeliveryConfig>({
    categories: {
      [DeliveryItemCategorySchema.Enum.ALERT]: {
        label: "Alert",
        icon: "warning",
        backgroundColorClass: "bg-orange-50",
        iconColorClass: "text-orange-600",
      },
      [DeliveryItemCategorySchema.Enum.OPPORTUNITY]: {
        label: "Opportunity",
        icon: "sunny",
        backgroundColorClass: "bg-indigo-50",
        iconColorClass: "text-indigo-600",
      },
      [DeliveryItemCategorySchema.Enum.UPDATE]: {
        label: "Update",
        icon: "error",
        backgroundColorClass: "bg-emerald-50",
        iconColorClass: "text-emerald-600",
      },
      [DeliveryItemCategorySchema.Enum.BRIEFING]: {
        label: "Briefing",
        icon: "info",
        backgroundColorClass: "bg-violet-50",
        iconColorClass: "text-violet-600",
      },
    },
    types: {
      [DeliveryItemTypeSchema.Enum.BID_CHANGE]: {
        category: DeliveryItemCategorySchema.Enum.OPPORTUNITY,
        actionSuggestions: [
          {
            label: "Accept",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.PATCH_AD_GROUP,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.PATCH_AD_GROUP
                )
              );
            },
          },
        ],
        allowExpand: true,
      },
      [DeliveryItemTypeSchema.Enum.EXTEND_CAMPAIGN]: {
        category: DeliveryItemCategorySchema.Enum.UPDATE,
        actionSuggestions: [
          {
            label: "Setup flight extension",
            icon: "flight_takeoff",
            actionType: SuggestionTypeSchema.Enum.FLIGHT_MODIFY,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.FLIGHT_MODIFY
                )
              );
            },
          },
        ],
        allowExpand: true,
      },
      [DeliveryItemTypeSchema.Enum.BID_MODIFIER]: {
        category: DeliveryItemCategorySchema.Enum.OPPORTUNITY,
        actionSuggestions: [
          {
            label: "Accept bid modifier",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.BID_MODIFIER,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.BID_MODIFIER
                )
              );
            },
          },
        ],
        allowExpand: true,
      },
      [DeliveryItemTypeSchema.Enum.ITEM_FAILED]: {
        category: DeliveryItemCategorySchema.Enum.ALERT,
        actionSuggestions: [
          {
            label: "Retry",
            icon: "autorenew",
            actionType: SuggestionTypeSchema.Enum.PLACEHOLDER,
          },
        ],
      },
      [DeliveryItemTypeSchema.Enum.AUDIENCE_SWAP]: {
        category: DeliveryItemCategorySchema.Enum.OPPORTUNITY,
        allowExpand: true,
        actionSuggestions: [
          {
            label: "Accept",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.MUTATE_TARGETS,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.MUTATE_TARGETS
                )
              );
            },
          },
        ],
      },
      [DeliveryItemTypeSchema.Enum.UNDER_PACING]: {
        category: DeliveryItemCategorySchema.Enum.ALERT,
        allowExpand: true,
        actionSuggestions: [
          {
            label: "Accept",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.UNDER_PACING,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.UNDER_PACING
                )
              );
            },
          },
        ],
      },
      [DeliveryItemTypeSchema.Enum.GENERIC_UPDATE]: {
        category: DeliveryItemCategorySchema.Enum.UPDATE,
        actionSuggestions: [
          {
            label: "Got it, thanks",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.ACKNOWLEDGE,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.ACKNOWLEDGE
                )
              );
            },
          },
        ],
        acceptedTextCreator: gotItAcceptedText,
      },
      [DeliveryItemTypeSchema.Enum.GENERIC_ALERT]: {
        category: DeliveryItemCategorySchema.Enum.ALERT,
        actionSuggestions: [
          {
            label: "Got it, thanks",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.ACKNOWLEDGE,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.ACKNOWLEDGE
                )
              );
            },
          },
        ],
        acceptedTextCreator: gotItAcceptedText,
      },
      [DeliveryItemTypeSchema.Enum.ECPM_SPIKE_MONITOR]: {
        category: DeliveryItemCategorySchema.Enum.ALERT,
        allowExpand: true,
        actionSuggestions: [
          {
            label: "Accept changes",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.ECPM_SPIKE_MONITOR,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.ECPM_SPIKE_MONITOR
                )
              );
            },
          },
        ],
      },
      [DeliveryItemTypeSchema.Enum.MARKDOWN_CONTENT]: {
        category: DeliveryItemCategorySchema.Enum.BRIEFING,
        allowExpand: false,
        actionSuggestions: [
          {
            label: "Got it, thanks",
            icon: "check",
            actionType: SuggestionTypeSchema.Enum.ACKNOWLEDGE,
            onClick: async (item) => {
              await robClient.executeDeliveryItemAction(
                item.id,
                actionPayloadFactory.createActionPayload(
                  item,
                  SuggestionTypeSchema.Enum.ACKNOWLEDGE
                )
              );
            },
          },
        ],
        acceptedTextCreator: gotItAcceptedText,
      },
    },
  });

  return {
    deliveryItemConfig: computed(() => deliveryItemConfig.value),
    timeFilterConfig: computed(() => timeFilterConfig),
    sortOptionConfig: computed(() => sortOptionConfig),
  };
};
