import { ref, watch, type Ref, onBeforeUnmount } from "vue";

export interface TypewriterConfig {
  addSpeed?: number;
  deleteSpeed?: number;
  splitType?: "word" | "char";
}

export function useTypewriterText(
  source: Ref<string>,
  { addSpeed = 10, deleteSpeed = 5, splitType = "char" }: TypewriterConfig = {}
): Ref<string> {
  const display = ref("");
  let previous = "";
  let abortController = new AbortController();

  onBeforeUnmount(() => {
    abortController.abort();
  });

  const splitBy = (text: string) =>
    splitType === "word"
      ? text.split(/(\s+)/).filter((t) => t.length > 0)
      : text.split("");

  const delay = (ms: number) =>
    new Promise<void>((resolve, reject) => {
      const id = window.setTimeout(() => {
        if (abortController.signal.aborted) {
          reject(new Error("aborted"));
        } else {
          resolve();
        }
      }, ms);
      abortController.signal.addEventListener("abort", () => {
        clearTimeout(id);
        reject(new Error("aborted"));
      });
    });

  async function runTypewriter(next: string) {
    abortController.abort();
    abortController = new AbortController();

    const minLen = Math.min(previous.length, next.length);
    let i = 0;
    while (i < minLen && previous[i] === next[i]) {
      i++;
    }
    const prefix = next.slice(0, i);
    const oldSuffix = splitBy(previous.slice(i));
    const newSuffix = splitBy(next.slice(i));

    for (let d = oldSuffix.length; d > 0; d--) {
      display.value = prefix + oldSuffix.slice(0, d - 1).join("");
      try {
        await delay(deleteSpeed);
      } catch {
        return;
      }
    }

    for (let j = 0; j < newSuffix.length; j++) {
      display.value = prefix + newSuffix.slice(0, j + 1).join("");
      try {
        await delay(addSpeed);
      } catch {
        return;
      }
    }

    previous = next;
  }

  watch(
    source,
    (next) => {
      runTypewriter(next).catch(() => {
        // ignore aborted
      });
    },
    { immediate: true }
  );

  return display;
}
