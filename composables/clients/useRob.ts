import type {
  FileUploadMetadata,
  FileUploadR<PERSON>ponse,
  ScopeFilter,
} from "~/composables/clients/dtos";
import type { User } from "#auth-utils";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import { DeliveryItemSchema } from "~/types/delivery-item/delivery-item";
import type { DeliveryItemActionExecutionRequest } from "~/types/api/delivery-item";
import {
  GetFilesMetadataResponseSchema,
  type FilePurpose,
  type GetFilesMetadataResponse,
} from "~/types/api/storage";
import { useScopeHeader } from "~/composables/clients/useScopeHeader";

export const useRob = () => {
  const { activeAgencyScopeHeader, getScopeHeaderWith } = useScopeHeader();

  async function getReferenceableAsDocument(
    referenceId: string
  ): Promise<{ blob: Blob; name: string }> {
    const res = await fetch(
      `/relay/rob/api/referenceable/${referenceId}/document`
    );

    if (!res.ok) {
      throw new Error(`Failed to download attachment: ${res.statusText}`);
    }

    const blob = await res.blob();

    const contentDisposition = res.headers.get("Content-Disposition");

    const filenameMatch =
      contentDisposition &&
      contentDisposition.match(/filename=["']?([^"';\n]*)["']?/i);
    const name = filenameMatch ? filenameMatch[1] : "default-filename.ext";

    return { blob, name };
  }

  async function uploadFile(
    file: File,
    fileMetadata: FileUploadMetadata
  ): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append(
      "metadata",
      new Blob([JSON.stringify(fileMetadata)], {
        type: "application/json",
      })
    );

    try {
      const response = await $fetch<FileUploadResponse>(
        `/relay/rob/api/storage/upload`,
        {
          method: "POST",
          body: formData,
        }
      );

      return response;
    } catch (error: any) {
      console.error("Error uploading file:", error);

      // Provide a more helpful error message
      if (error.response) {
        throw new Error(
          `Upload failed with status ${error.response.status}: ${error.response.statusText}`
        );
      } else if (error.message) {
        throw new Error(`Upload failed: ${error.message}`);
      } else {
        throw new Error("File upload failed due to an unknown error");
      }
    }
  }

  async function deleteFile(fileId: string): Promise<void> {
    await $fetch<void>(`/relay/rob/api/storage/file/${fileId}`, {
      method: "DELETE",
    });
  }

  async function getKnowledge(
    knowledgeTypes: KnowledgeType[],
    scopeFilter?: ScopeFilter
  ): Promise<KnowledgeItem<KnowledgeType>[]> {
    const response = await $fetch<KnowledgeQueryResponse>(
      `/relay/rob/api/knowledge/list`,
      {
        method: "POST",
        body: knowledgeTypes as KnowledgeQueryRequest,
        headers: {
          ...getScopeHeaderWith(scopeFilter).value,
        },
      }
    );

    return response.map((item) => ({
      id: item.id,
      knowledgeType:
        KnowledgeType[item.knowledgeType as keyof typeof KnowledgeType],
      payload: item.payload as KnowledgeItemPayload[KnowledgeType],
      displayName: item.displayName,
      vectorize: item.vectorize,
      updatedAt: new Date(item.updatedAt),
      ownerType: item.ownerType,
      ownerId: item.ownerId,
    }));
  }

  async function batchSetKnowledge(
    add: KnowledgeItemRequest[],
    remove: KnowledgeItemRequest[],
    update: KnowledgeItemRequest[]
  ): Promise<void> {
    await $fetch<void>(`/relay/rob/api/knowledge/batch-set`, {
      method: "POST",
      body: { add, remove, update } as BatchSetKnowledgeRequest,
    });
  }

  async function getReferenceables() {
    return await $fetch<GetReferenceablesResponse>(
      `/relay/rob/api/referenceable`,
      {
        headers: {
          ...activeAgencyScopeHeader.value,
        },
      }
    );
  }

  async function getDeliveryItems(
    advertiserFilter: Ref<string[]>,
    timeFilter: Ref<string>,
    sort: Ref<string>,
    nextToken: Ref<string>
  ) {
    return useAsyncData(
      "feed",
      () =>
        $fetch<DeliveryItem[]>("/relay/rob/api/feed", {
          headers: {
            ...getScopeHeaderWith(
              { advertiserIds: advertiserFilter.value },
              true
            ).value,
          },
          query: {
            // advertiserFilter: advertiserFilter.value,
            hoursBackFromNow: timeFilter.value,
            sort: sort.value,
            nextToken: nextToken.value || undefined,
          },
        })
          .then((r) =>
            r
              .map((item) => DeliveryItemSchema.safeParse(item))
              .filter((result) => {
                if (result.success) {
                  return true;
                }

                console.error(result.error);
                return false;
              })
              .map((result) => result.data!)
          )
          .catch((error) => {
            console.error(error);
            return [];
          }),
      {
        dedupe: "defer",
        immediate: true, // default; shown for clarity
      }
    );
  }

  async function executeDeliveryItemAction(
    deliveryItemId: string,
    request: DeliveryItemActionExecutionRequest
  ) {
    const typedRequest = request.map((action) => ({
      ...action,
      payload: {
        ...(action.payload ?? {}),
        // Only set type to actionType if payload.type doesn't already exist
        type: (action.payload as any)?.type || action.actionType,
      },
    }));

    console.info(
      "Executing delivery item action for item:",
      deliveryItemId,
      "with request:",
      typedRequest
    );

    await $fetch<void>(`/relay/rob/api/feed/${deliveryItemId}/execute`, {
      method: "POST",
      body: typedRequest,
    });
  }

  async function putDeliveryItem(deliveryItem: DeliveryItem) {
    await $fetch<void>(`/relay/rob/api/feed/${deliveryItem.id}`, {
      method: "PUT",
      body: deliveryItem,
    });
  }

  async function getFilesMetadata(
    purpose: FilePurpose,
    user: Ref<User | null>,
    advertiserId: Ref<string[] | undefined>
  ) {
    const scopeFilter = computed(() => ({
      advertiserIds: advertiserId.value,
      userIds: user.value?.sub ? [user.value.sub] : undefined,
    }));

    return await useAsyncData<GetFilesMetadataResponse>(
      `/relay/rob/api/storage`,
      async () => {
        if (!advertiserId.value || !user.value) {
          return [];
        }

        const response = await $fetch<GetFilesMetadataResponse>(
          `/relay/rob/api/storage`,
          {
            query: { purpose },
            headers: {
              ...getScopeHeaderWith(scopeFilter).value,
            },
          }
        );

        return GetFilesMetadataResponseSchema.parse(response);
      },
      {
        watch: [advertiserId, user],
      }
    );
  }

  async function getCampaigns(
    advertiserIds: string[],
    options?: {
      page?: number;
      size?: number;
      sortBy?: string;
      sortDir?: "asc" | "desc";
      refresh?: boolean;
      campaignIds?: string[];
      funnelStages?: string[];
      statuses?: string[];
      campaignNameKeywords?: string;
    }
  ) {
    const {
      page = 0,
      size = 20,
      sortBy = "campaignName",
      sortDir = "asc",
      refresh = false,
      campaignIds,
      funnelStages,
      statuses,
      campaignNameKeywords,
    } = options || {};

    const queryParams = new URLSearchParams();
    advertiserIds.forEach((id) => {
      queryParams.append("advertiserIds", id);
    });

    if (campaignIds && campaignIds.length > 0) {
      campaignIds.forEach((id) => {
        queryParams.append("campaignIds", id);
      });
    }

    // Add filter parameters
    if (funnelStages && funnelStages.length > 0) {
      funnelStages.forEach((stage) => {
        queryParams.append("funnelStage", stage);
      });
    }

    if (statuses && statuses.length > 0) {
      statuses.forEach((status) => {
        queryParams.append("status", status);
      });
    }

    // Add search query parameter
    if (campaignNameKeywords && campaignNameKeywords.trim()) {
      queryParams.append("campaignNameKeywords", campaignNameKeywords.trim());
    }

    // Add pagination and sorting parameters
    queryParams.append("page", page.toString());
    queryParams.append("size", size.toString());
    queryParams.append("sortBy", sortBy);
    queryParams.append("sortDir", sortDir);
    queryParams.append("refresh", refresh.toString());

    const response = await fetch(
      `/relay/rob/api/metadata/campaigns?${queryParams.toString()}`
    );

    // Handle 204 No Content specifically
    if (response.status === 204) {
      return {
        content: [],
        page: {
          size,
          number: page,
          totalElements: 0,
          totalPages: 0,
        },
      };
    }
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return await response.json();
  }

  return {
    uploadFile,
    deleteFile,
    getKnowledge,
    batchSetKnowledge,
    getReferenceableAsDocument,
    getReferenceables,
    getDeliveryItems,
    executeDeliveryItemAction,
    putDeliveryItem,
    getFilesMetadata,
    getCampaigns
  };
};
