export const useScopeHeader = () => {
  const agencyStore = useAgencyStore();

  const activeAgencyScopeHeader = getScopeHeaderWith(undefined, true);

  function getScopeHeaderWith(
    filter?: MaybeRef<ScopeFilter>,
    includeActiveAgency = false
  ) {
    return computed(() => {
      const filterValue = unref(filter);
      const agencyIds = includeActiveAgency
        ? [agencyStore.activeAgency?.id]
        : filterValue?.agencyIds;
      const advertiserIds = filterValue?.advertiserIds ?? [];
      const userIds = filterValue?.userIds ?? [];

      return {
        "Gigi-Scope-Filter": JSON.stringify({
          agencyIds,
          advertiserIds,
          userIds,
        }),
      } as ScopeFilterHeader;
    });
  }

  return {
    activeAgencyScopeHeader,
    getScopeHeaderWith,
  };
};
