import type { ChatParticipant } from "~/types/chat/common";

export interface CreateConversationRequest {
  title: string;
  ownerId: string;
  ownerType: string;
  tags: string[];
}

export interface CreateConversationResponse {
  id: string;
  userId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AddMessageToConversationRequest {
  conversation_id: string;
  participant_type: ChatParticipant;
  participant_id: string;
  client_id: string;
  payload: {
    message: string;
    referenced_resources?: {
      key: string;
      display_name: string;
      reference_id: string;
    }[];
  };
}

export interface AddMessageToConversationResponse {
  message_id: string;
}

export interface FileUploadMetadata {
  ownerType: string;
  ownerId: string;
  knowledge: boolean;
  agencyId: string;
  advertiserId?: string;
  existingFileId?: string;
}

export interface FileUploadResponse {
  fileId: string;
  knowledgeId?: string;
}

export type KnowledgeQueryRequest = KnowledgeType[];

export interface ScopeFilter {
  agencyIds?: string[];
  advertiserIds?: string[];
  userIds?: string[];
}

export interface KnowledgeItemResponse {
  id: string;
  knowledgeType: string;
  description?: string;
  payload: Record<string, any>;
  displayName: string;
  vectorize: boolean;
  updatedAt: string;
  ownerType: string;
  ownerId: string;
}

export interface KnowledgeItemRequest {
  knowledgeType: string;
  description?: string;
  payload: Record<string, any>;
  displayName: string;
  ownerType: string;
  ownerId: string;
}

export type KnowledgeQueryResponse = KnowledgeItemResponse[];

export interface BatchSetKnowledgeRequest {
  add: KnowledgeItemRequest[];
  remove: KnowledgeItemRequest[];
  update: KnowledgeItemRequest[];
}

export type GetAdvertisersResponse = Advertiser[];

export type GetAgenciesResponse = Agency[];

import { ReferenceableResourceType } from "~/types/attachment";

export interface ReferenceableResult {
  referenceableId: string;
  displayText: string;
  updatedAt: Date;
  ownerType: string;
  ownerId: string;
}

export interface GetReferenceablesResponse {
  [ReferenceableResourceType.JSON]?: ReferenceableResult[];
  [ReferenceableResourceType.FILE]?: ReferenceableResult[];
}

export interface ScopeFilterHeader {
  "Gigi-Scope-Filter": string;
}

export interface PagedResponse<T> {
  content: T[];
  nextToken: string;
}
