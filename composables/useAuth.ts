export const useAuth = () => {
  const session = useUserSession();

  async function loadUser() {}

  async function logOut() {
    await session.clear();
    navigateTo("/login");
  }

  const refreshToken = async () => {
    try {
      const response = await fetch("/api/auth/refresh", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to refresh token");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(error);
      await logOut();
    }
  };

  return {
    session,
    refreshToken,
  };
};
