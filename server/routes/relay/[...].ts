import { defineEventHandler, proxyRequest } from "h3";
import { serviceRegistry } from "~/server/utils/serviceRegistry";
import { getHeadersToForward } from "~/server/utils/headerForwarding";
import { isRelayAllowed } from "~/server/utils/relay-whitelist";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default defineEventHandler(async (event) => {
  const url = event.node.req.url || "";
  console.log(`Relay handler received request: ${event.node.req.method} ${url}`);

  // Pattern: /relay/{serviceId}/{pathAtService}
  const match = url.match(/^\/relay\/([^\/]+)(.*)/);

  if (!match) {
    console.log("Failed to match relay pattern:", url);
    return { statusCode: 400, body: "Invalid request" };
  }

  const [, serviceId, path] = match;
  console.log(`Parsed request - service: ${serviceId}, path: ${path}`);

  const serviceConfig =
    serviceRegistry[serviceId as keyof typeof serviceRegistry];

  if (!serviceConfig) {
    return { statusCode: 400, body: `Unknown service: ${serviceId}` };
  }

  const baseUrl = serviceConfig.url;
  const key = serviceConfig.key;

  const targetUrl = `${baseUrl}${path}`;

  if (
    !isRelayAllowed(
      serviceId as keyof typeof serviceRegistry,
      targetUrl,
      event.node.req.method
    )
  ) {
    return event.respondWith(new Response("Forbidden", { status: 403 }));
  }

  const session = await getUserSession(event);

  try {
    console.log(`Proxying ${event.node.req.method} to: ${targetUrl}`);
    console.log(`Using service key: ${key ? key.substring(0, 10) + '...' : 'undefined'}`);
    console.log(`Session token available: ${session.secure?.accessToken ? 'yes' : 'no'}`);

    const headersToForward = getHeadersToForward(event);
    const response = await proxyRequest(event, targetUrl, {
      headers: {
        "Gigi-AccessKey": key,
        Authorization: `Bearer ${session.secure?.accessToken}`,
        ...headersToForward,
      },
    });

    console.log(`Response status: ${response.status}`);
    return response;
  } catch (error: any) {
    console.error("Proxy request failed:", error);
    console.error("Error status:", error.status || error.statusCode);
    console.error("Error message:", error.message);

    event.respondWith(
      new Response(
        JSON.stringify({
          error: true,
          message: error.message || "Proxy request failed",
          service: serviceId,
          path: path,
        }),
        { status: error.statusCode || 500 }
      )
    );

    return;
  }
});
