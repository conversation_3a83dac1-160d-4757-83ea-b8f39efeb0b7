import { ScopeFilter } from "~/composables/clients/dtos";

export default defineEventHandler(async (event) => {
  const entityType = getRouterParam(event, 'entityType');
  const entityId = getRouterParam(event, 'entityId');
  
  if (!entityType || !entityId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Entity type and ID are required'
    });
  }

  // Validate entity type
  if (!['agency', 'advertiser'].includes(entityType)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid entity type. Must be "agency" or "advertiser"'
    });
  }
  
  const userSession = await getUserSession(event);
  const scopeFilter: ScopeFilter = {
    [`${entityType}Ids`]: [entityId],
  };
  const {url: baseUrl, key: accessKey} = serviceRegistry.rob;
  const url = `${baseUrl}/api/storage/latest/icon`;

  const response = await $fetch(url, {
      method: "GET",
      headers: {
        "Gigi-AccessKey": accessKey,
        "Gigi-Scope-Filter": JSON.stringify(scopeFilter),
        Authorization: `Bearer ${userSession.secure?.accessToken}`,
      },
    }
  );

  return response;
}); 