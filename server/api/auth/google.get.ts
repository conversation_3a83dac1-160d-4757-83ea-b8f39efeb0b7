import { buildSessionFromTokens } from "~/server/utils/authUtils";

interface GoogleAuthResponse {
  accessToken: string;
  refreshToken: string;
}

export default defineEventHandler(async (event) => {
  const requestUrl = getRequestURL(event);
  const redirectUri = `${requestUrl.origin}/confirm`;

  const authorizationCode = getQuery(event).code;

  const response = await $fetch<GoogleAuthResponse>(
    "/relay/mms/api/auth/google",
    {
      method: "POST",
      headers: {
        "Gigi-Platform": "NEO",
      },
      query: {
        redirectUri,
        authorizationCode,
      },
    }
  );

  const { accessToken, refreshToken } = response;
  const session = await buildSessionFromTokens(
    event,
    accessToken,
    refreshToken
  );
  
  await replaceUserSession(event, session);

  return { ok: true };
});
