import { debouncedRefreshAccessToken } from "~/server/utils/authUtils";
import { ApiError } from "~/types/api-error";

export default defineEventHandler(async (event) => {
  const session = await getUserSession(event);

  if (!session?.secure?.refreshToken) {
    const error = new ApiError("Unauthorized: missing refresh token", 401);
    event.respondWith(error.toResponse());
    return { ok: false };
  }

  const newUserSession = await debouncedRefreshAccessToken(
    event,
    session.secure.refreshToken
  );

  if (newUserSession) {
    await replaceUserSession(event, newUserSession);
  }
  return { ok: true };
});
