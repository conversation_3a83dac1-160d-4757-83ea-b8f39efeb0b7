import { aesDecrypt } from "~/server/utils/authUtils";
import { ApiError } from "~/types/api-error";

export default defineEventHandler(async (event) => {
  const token = getQuery(event).token;
  const decryptedToken = aesDecrypt(decodeURIComponent(token as string));

  let userSession;
  try {
    const res = await debouncedRefreshAccessToken(event, decryptedToken);
    userSession = res?.userSession;
  } catch (error) {
    console.error(error);
    throw new ApiError("Failed to refresh access token", 500).toResponse();
  }

  if (!userSession) {
    throw new ApiError("Unauthorized", 401).toResponse();
  }

  await replaceUserSession(event, userSession);

  return {
    ok: true,
  };
});
