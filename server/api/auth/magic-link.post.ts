import { sendEmail } from "~/server/utils/sendEmail";
import { ApiError } from "~/types/api-error";

export default defineEventHandler(async (event) => {
  const { email } = await readBody(event);
  if (!email) {
    throw new ApiError("Email is required", 400).toResponse();
  }

  try {
    const magicLink = generateMagicLinkToken(event, email);
    await sendEmail(email, "magic-link", {
      magic_link: magicLink,
    });
  } catch (error) {
    console.error(error);
    throw new ApiError("Failed to send magic link", 500).toResponse();
  }

  return {
    success: true,
  };
});
