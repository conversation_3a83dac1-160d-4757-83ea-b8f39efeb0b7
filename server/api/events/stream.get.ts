import { ApiError } from "~/types/api-error";
import { ServerEventsMediator } from "@/server/utils/sse/sse-mediator";
import { ClientEventEmitter } from "@/server/utils/sse/types";
import { startHeartbeat } from "~/server/utils/sse/heartbeat";
import { getSSETaskQueue } from "~/server/utils/sse/task-queue/index";

export default defineEventHandler(async (event) => {
  console.info("stream: New client connection request");
  const userSession = await getUserSession(event);

  if (!userSession.user) {
    console.error("stream: Unauthorized request");
    throw new ApiError("Unauthorized", 401);
  }

  const query = getQuery(event);
  const clientId = query.clientId;

  if (!clientId) {
    console.error("stream: Client ID is required");
    throw new ApiError("Client ID is required", 400);
  }

  if (typeof clientId !== "string" || !clientId.length) {
    console.error("stream: Invalid client ID", clientId);
    throw new ApiError("Invalid client ID", 400);
  }

  const { res } = event.node;

  res.setHeader("Content-Type", "text/event-stream");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");
  res.write("event: connect\n");

  const eventSender: ClientEventEmitter = (eventName: string, data: any) => {
    return new Promise<void>((resolve) => {
      const chunk = `event: ${eventName}\ndata: ${JSON.stringify(data)}\n\n`;
      const ok = res.write(chunk);
      if (ok) {
        return resolve();
      }
      // wait for drain before resolving (buffer is full)
      res.once("drain", resolve);
    });
  };

  console.info("stream: Handling new client connection");
  ServerEventsMediator.handleNewClient(clientId, eventSender);

  const stopHeartbeat = startHeartbeat(
    getSSETaskQueue(),
    eventSender,
    clientId
  );

  res.on("close", () => {
    console.info("stream: Handling client disconnect");

    stopHeartbeat();
    ServerEventsMediator.handleClientDisconnect(clientId, eventSender);
    res.end();
  });

  // Keep connection alive until the client disconnects
  return new Promise(() => {});
});
