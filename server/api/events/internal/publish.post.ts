import { getSSETaskQueue } from "@/server/utils/sse/task-queue/";
import { ServerEventsMediator } from "@/server/utils/sse/sse-mediator";
import { ApiError } from "@/types/api-error";
import { ServerEventRequestSchema } from "~/types/server-events";

export default defineEventHandler(async (event) => {
  const configRuntime = useRuntimeConfig();
  const accessToken = getRequestHeader(event, "Gigi-Access-Token");

  if (!accessToken || accessToken !== configRuntime.selfAccessToken) {
    console.error("Publish: Unauthorized request to publish updates");
    throw new ApiError("Unauthorized", 401);
  }

  const body = await readBody(event);
  const parsedBody = ServerEventRequestSchema.safeParse(body);
  if (!parsedBody.success) {
    console.error("Publish: Invalid request body", parsedBody.error);
    throw new ApiError("Invalid request body", 400);
  }

  console.info(
    `Publish: Received event: '${parsedBody.data.eventName}' to topic '${
      parsedBody.data.topic
    }' with data: ${JSON.stringify(parsedBody.data.data, null, 2)}`
  );

  try {
    const taskQueue = getSSETaskQueue();

    taskQueue.enqueue(async () => {
      await ServerEventsMediator.emitToTopic(
        parsedBody.data.topic.toLowerCase(),
        parsedBody.data.eventName,
        parsedBody.data.data
      );
    });

    console.info(
      `Publish: Event queued successfully: '${
        parsedBody.data.eventName
      }' to topic '${parsedBody.data.topic} with data: ${JSON.stringify(
        parsedBody.data.data,
        null,
        2
      )}`
    );
  } catch (error: any) {
    console.error("Publish: Failed to queue event:", error);
    throw new ApiError("Failed to queue event", 500);
  }

  event.respondWith(new Response(null, { status: 202 }));
});
