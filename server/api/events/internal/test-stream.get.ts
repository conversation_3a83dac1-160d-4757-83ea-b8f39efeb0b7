import { ApiError } from "~/types/api-error";
import { ServerEventsMediator } from "@/server/utils/sse/sse-mediator";
import { ClientEventEmitter } from "@/server/utils/sse/types";

export default defineEventHandler(async (event) => {
  console.info("test-stream: New test client connection");

  // Auth check using access token
  const configRuntime = useRuntimeConfig();
  const accessToken = getRequestHeader(event, "Gigi-Access-Token");

  if (!accessToken || accessToken !== configRuntime.selfAccessToken) {
    console.error("test-stream: Unauthorized request");
    throw new ApiError("Unauthorized", 401);
  }

  const query = getQuery(event);
  const topic = query.topic as string;
  const clientId = `test-client-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  if (!topic) {
    console.error("test-stream: Topic is required");
    throw new ApiError("Topic is required (?topic=your-topic)", 400);
  }

  console.info(
    `test-stream: Setting up SSE for topic "${topic}" with client ${clientId}`
  );

  const { res } = event.node;

  // Set SSE headers
  res.setHeader("Content-Type", "text/event-stream");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Headers", "Gigi-Access-Token");

  // Send initial connection event
  res.write(
    `event: connected\ndata: ${JSON.stringify({
      clientId,
      topic,
      timestamp: new Date().toISOString(),
      message: `Connected to topic: ${topic}`,
    })}\n\n`
  );

  // Create event sender with backpressure handling
  const eventSender: ClientEventEmitter = (eventName: string, data: any) => {
    return new Promise<void>((resolve) => {
      const chunk = `event: ${eventName}\ndata: ${JSON.stringify({
        ...data,
        timestamp: new Date().toISOString(),
        topic: topic,
      })}\n\n`;

      const ok = res.write(chunk);
      if (ok) {
        return resolve();
      }
      res.once("drain", resolve);
    });
  };

  // Register client and subscribe to topic
  ServerEventsMediator.handleNewClient(clientId, eventSender);
  ServerEventsMediator.subscribeToTopic(topic, clientId);

  // Send subscription confirmation
  await eventSender("subscribed", {
    topic,
    message: `Successfully subscribed to topic: ${topic}`,
  });

  // Handle connection close
  res.on("close", () => {
    console.info(
      `test-stream: Client ${clientId} disconnected from topic ${topic}`
    );
    ServerEventsMediator.handleClientDisconnect(clientId, eventSender);
    res.end();
  });

  // Keep connection alive with periodic heartbeat
  const heartbeat = setInterval(async () => {
    try {
      await eventSender("heartbeat", {
        message: "Connection alive",
        uptime: Date.now(),
      });
    } catch (error) {
      console.error("test-stream: Heartbeat failed, closing connection");
      clearInterval(heartbeat);
    }
  }, 30000); // Every 30 seconds

  res.on("close", () => {
    clearInterval(heartbeat);
  });
});
