import { TopicSubscriptionRequestSchema } from "~/types/server-events";

import { ServerEventsMediator } from "~/server/utils/sse/sse-mediator";
import { ApiError } from "~/types/api-error";

export default defineEventHandler(async (event) => {
  console.info("unsubscribe: New request");

  const userSession = await getUserSession(event);

  if (!userSession.user) {
    console.error("unsubscribe: Unauthorized");
    throw new ApiError("Unauthorized", 401);
  }

  const body = await readBody(event);
  const parsedBody = TopicSubscriptionRequestSchema.safeParse(body);

  if (!parsedBody.success) {
    console.error("unsubscribe: Invalid request body", parsedBody.error);
    throw new ApiError("Invalid request body", 400);
  }

  console.info("unsubscribe: Unsubscribing from topics", parsedBody.data);

  parsedBody.data.forEach((subscription) => {
    const topicName = `${subscription.topicLevel}:${subscription.topicIdentifier}`;
    ServerEventsMediator.unsubscribeFromTopic(topicName, subscription.clientId);
  });

  event.respondWith(new Response(null, { status: 204 }));
});
