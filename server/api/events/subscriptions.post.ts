import { UserSession } from "#auth-utils";
import { ServerEventsMediator } from "~/server/utils/sse/sse-mediator";
import { ApiError } from "~/types/api-error";
import {
  TopicSubscription,
  TopicSubscriptionRequestSchema,
} from "~/types/server-events";

function verifySubscriptionClaims(
  subscription: TopicSubscription,
  userSession: UserSession
) {
  if (!userSession.user) {
    return false;
  }

  if (userSession.user.isGigiAdmin) {
    return true;
  }

  switch (subscription.topicLevel) {
    case "USER":
      return true;
    case "ADVERTISER":
      return Object.values(userSession.user.roles?.ADVERTISER ?? [])
        .flat()
        .includes(subscription.topicIdentifier);
    case "AGENCY":
      return Object.values(userSession.user.roles?.AGENCY ?? [])
        .flat()
        .includes(subscription.topicIdentifier);
  }
}

export default defineEventHandler(async (event) => {
  console.info("subscribe: New request");

  const userSession = await getUserSession(event);

  if (!userSession.user) {
    console.error("subscribe: Unauthorized");
    throw new ApiError("Unauthorized", 401);
  }

  const body = await readBody(event);
  const parsedBody = TopicSubscriptionRequestSchema.safeParse(body);

  if (!parsedBody.success) {
    console.error("subscribe: Invalid request body", parsedBody.error);
    throw new ApiError("Invalid request body", 400);
  }

  console.info("subscribe: Verifying subscription claims");
  parsedBody.data.forEach((subscription) => {
    if (!verifySubscriptionClaims(subscription, userSession)) {
      console.error(
        "subscribe: Forbidden user does not have access to requested (some) of requested topics"
      );
      throw new ApiError("Forbidden", 403);
    }
  });

  console.info("subscribe: Subscribing to topics", parsedBody.data);

  parsedBody.data.forEach((subscription) => {
    let userIdPrefix = "";
    if (subscription.topicLevel === "USER") {
      userIdPrefix = `${userSession.user!.sub}:`;
    }
    const topicName = `${subscription.topicLevel.toLowerCase()}:${userIdPrefix}${
      subscription.topicIdentifier
    }`;
    ServerEventsMediator.subscribeToTopic(topicName, subscription.clientId);
  });

  event.respondWith(new Response(null, { status: 204 }));
});
