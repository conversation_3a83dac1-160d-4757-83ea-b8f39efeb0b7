{"rob": ["/api/referenceable/:referenceId/document", "/api/storage/upload", "/api/storage/file/:fileId", "/api/storage", "/api/knowledge/list", "/api/knowledge/batch-set", "/api/referenceable", "/api/feed", "/api/feed/:deliveryItemId/execute", "/api/feed/:deliveryItemId", "/api/metadata/campaigns", "/api/v2/chat/conversations", "/api/v2/chat/:conversationId", "/api/v2/chat/:conversationId/messages", "/api/v2/chat/:conversationId/messages/:messageId", "/api/v2/chat/:conversationId/documents", "/api/v2/chat/:conversationId/documents/:docVersion", "/api/v2/chat/:conversationId/documents/:docInstance/versions/:docVersion", "/api/v2/chat/create", "/api/adsp/v1/campaigns/:campaignId/flights/list", "/api/metadata/campaign-funnels", "/api/adsp/v1/adCreative/associations/adGroups/list", "/api/adsp/v1/adGroups/list", "/api/adsp/v1/adCreatives/list", "/api/adsp/v1/campaigns/list", "/api/metadata/creative-assets"], "linda": ["/stream_chat", "/workorder/markdown-cards", "/workorder/run", "/workorder/:id/status"], "mms": ["/api/resource/scoped/advertisers", "/api/resource/scoped/agencies", "/api/auth/public-key", "/api/auth/refresh/:token", "/api/auth/magic-link", "/api/auth/google"], "metrics": ["/metrics_from_group"]}