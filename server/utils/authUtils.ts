import { User } from "#auth-utils";
import { H3Event } from "h3";
import jwt from "jsonwebtoken";

export async function getMmsPublicKey(event: H3Event) {
  const response = await $fetch<string>("/relay/mms/api/auth/public-key");

  const publicKey =
    "-----BEGIN PUBLIC KEY-----\n" + response + "\n-----END PUBLIC KEY-----";
  return publicKey;
}

const existingRefreshTokenRequests = new Map<string, Promise<any>>();
export async function debouncedRefreshAccessToken(
  event: H3Event,
  refreshToken: string
) {
  if (existingRefreshTokenRequests.has(refreshToken)) {
    return existingRefreshTokenRequests.get(refreshToken);
  }

  // Create a new refresh request
  const requestPromise = refreshAccessToken(event, refreshToken)
    .then((result) => {
      // Keep the resolved promise for 5 seconds longer after the request completes
      // this way request spikes are handled more efficiently
      setTimeout(() => {
        existingRefreshTokenRequests.delete(refreshToken);
      }, 5000);

      return result;
    })
    .catch((error) => {
      // Remove immediately on failure
      existingRefreshTokenRequests.delete(refreshToken);
      throw error;
    });

  // Store the new promise immediately
  existingRefreshTokenRequests.set(refreshToken, requestPromise);

  return requestPromise;
}

async function refreshAccessToken(event: H3Event, refreshToken: string) {
  let publicKey;
  try {
    publicKey = await getMmsPublicKey(event);
  } catch (error) {
    console.error(error);
    return;
  }

  const url = `/relay/mms/api/auth/refresh/${refreshToken}`;
  const response = await $fetch<{ accessToken: string }>(url, {
    method: "POST",
    headers: {
      "Gigi-Platform": "NEO",
    },
  });

  const { accessToken } = response;
  const session = await buildSessionFromTokens(
    event,
    accessToken,
    refreshToken
  );
  return session;
}

export async function getUserFromAccessToken(
  event: H3Event,
  accessToken: string
): Promise<User> {
  const publicKey = await getMmsPublicKey(event);
  return jwt.verify(accessToken, publicKey, {
    algorithms: ["RS256"],
  }) as User;
}

export async function buildSessionFromTokens(
  event: H3Event,
  accessToken: string,
  refreshToken: string
) {
  const user = await getUserFromAccessToken(event, accessToken);

  const secure = {
    accessToken,
    refreshToken,
    accessTokenExpiresAt: user.exp,
  };

  return { user, secure };
}

export async function generateMagicLinkToken(event: H3Event, email: string) {
  const refreshToken = await $fetch("/relay/mms/api/auth/magic-link", {
    method: "POST",
    headers: {
      "Gigi-Platform": "NEO",
    },
    body: {
      email,
    },
  });

  const origin = getRequestURL(event).origin;
  const params = new URLSearchParams({
    token: aesEncrypt(refreshToken),
  });

  return `${origin}/otp?${params.toString()}`;
}

export function aesEncrypt(buffer: string) {
  const config = useRuntimeConfig();
  const key = CryptoJS.enc.Utf8.parse(config.aesKey as string);
  const iv = CryptoJS.enc.Utf8.parse(config.aesIv as string);
  return CryptoJS.AES.encrypt(buffer, key, { iv }).toString();
}

export function aesDecrypt(buffer: string) {
  const config = useRuntimeConfig();
  const key = CryptoJS.enc.Utf8.parse(config.aesKey as string);
  const iv = CryptoJS.enc.Utf8.parse(config.aesIv as string);
  return CryptoJS.AES.decrypt(buffer, key, { iv }).toString(CryptoJS.enc.Utf8);
}
