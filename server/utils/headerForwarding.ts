import { getHeaders } from "h3";

/**
 * Default headers to forward when no explicit list is provided
 */
const DEFAULT_FORWARD_HEADERS = [
  "accept",
  "content-type",
  "user-agent",
  "gigi-scope-filter",
];

/**
 * Parse the comma-separated list of headers from the control header
 */
function parseForwardHeadersList(forwardHeadersList: string): string[] {
  return forwardHeadersList
    .split(",")
    .map((header) => header.trim().toLowerCase())
    .filter((header) => header.length > 0);
}

/**
 * Get default headers to forward from the request
 */
function getDefaultHeaders(
  allHeaders: Record<string, string | undefined>
): Record<string, string> {
  const defaultHeaders: Record<string, string> = {};

  DEFAULT_FORWARD_HEADERS.forEach((headerName) => {
    const value = allHeaders[headerName];
    if (value) {
      // Convert to proper case for common headers
      const properCaseHeader = getProperHeaderCase(headerName);
      defaultHeaders[properCaseHeader] = value;
    }
  });

  return defaultHeaders;
}

/**
 * Convert header names to proper case for common headers
 */
function getProperHeaderCase(headerName: string): string {
  const properCaseMap: Record<string, string> = {
    accept: "Accept",
    "content-type": "Content-Type",
    "user-agent": "User-Agent",
    "accept-language": "Accept-Language",
    "accept-encoding": "Accept-Encoding",
    "cache-control": "Cache-Control",
    "x-requested-with": "X-Requested-With",
  };

  return properCaseMap[headerName.toLowerCase()] || headerName;
}

/**
 * Extract specific headers from the request based on the provided list
 */
function extractSpecificHeaders(
  allHeaders: Record<string, string | undefined>,
  headerNames: string[]
): Record<string, string> {
  const extractedHeaders: Record<string, string> = {};

  headerNames.forEach((headerName) => {
    const value = allHeaders[headerName.toLowerCase()];
    if (value) {
      const properCaseHeader = getProperHeaderCase(headerName);
      extractedHeaders[properCaseHeader] = value;
    }
  });

  return extractedHeaders;
}

/**
 * Main function to get headers to forward based on client request
 */
export function getHeadersToForward(event: any): Record<string, string> {
  const allHeaders = getHeaders(event);
  const forwardHeadersList = allHeaders["x-proxy-forward-headers"];

  // Always start with default headers as baseline
  const baseHeaders = getDefaultHeaders(allHeaders);

  // If no additional headers specified, return just the defaults
  if (!forwardHeadersList || forwardHeadersList.trim() === "") {
    return baseHeaders;
  }

  // Parse additional headers to forward
  const additionalHeaderNames = parseForwardHeadersList(forwardHeadersList);
  const additionalHeaders = extractSpecificHeaders(
    allHeaders,
    additionalHeaderNames
  );

  // Merge additional headers with defaults (additional headers take precedence if there's overlap)
  return {
    ...baseHeaders,
    ...additionalHeaders,
  };
}
