interface Service {
  url: string;
  key: string;
}

const configRuntime = useRuntimeConfig();
export const serviceRegistry: Record<string, Service> = {
  linda: {
    url: configRuntime.lindaApiUrl,
    key: configRuntime.lindaApiKey,
  },
  rob: {
    url: configRuntime.robApiUrl,
    key: configRuntime.robApiKey,
  },
  mms: {
    url: configRuntime.mmsApiUrl,
    key: configRuntime.mmsApiKey,
  },
  metrics: {
    url: configRuntime.metricsApiUrl,
    key: configRuntime.metricsApiKey,
  },
};
