import fs from "fs";
import path from "path";
import { match, MatchFunction } from "path-to-regexp";

import endpoints from "~/server/config/relay-endpoints.json" assert { type: "json" };

type ServiceKey = keyof typeof serviceRegistry;
interface Matcher {
  test: MatchFunction<object>;
  methods: Set<string>;
}

console.log(
  "Relay Whitelist Service: [INIT] loaded with",
  JSON.stringify(endpoints, null, 2)
);

const defaultMethods = ["GET", "POST", "PUT", "PATCH", "DELETE"] as const;

const matchers: Record<ServiceKey, Matcher[]> = Object.entries(
  endpoints
).reduce((acc, [service, paths]) => {
  acc[service as ServiceKey] = paths.map((p) => ({
    test: match(p, { decode: decodeURIComponent }),
    methods: new Set(defaultMethods),
  }));
  return acc;
}, {} as Record<ServiceKey, Matcher[]>);

export function isRelayAllowed(
  service: ServiceKey,
  path: string,
  method = "GET",
  requestId = crypto.randomUUID()
): boolean {
  if (!path) {
    console.warn(`Relay Whitelist Service: [${requestId}] empty path provided`);
    return false;
  }

  const pathname = new URL(path, 'http://localhost').pathname;

  console.log(
    `Relay Whitelist Service: [${requestId}] validating new request ${service} ${pathname} ${method}`
  );
  console.log(`Available matchers for ${service}:`, matchers[service]?.map(m => m.test.toString()));

  const hasMatch = matchers[service]?.some(({ test, methods }) => {
    const methodMatch = methods.has(method.toUpperCase());
    const pathMatch = Boolean(test(pathname));
    console.log(`Testing path ${pathname}: method=${methodMatch}, path=${pathMatch}`);
    return methodMatch && pathMatch;
  });

  if (!hasMatch) {
    console.warn(`Relay Whitelist Service: [${requestId}] rejected - no match found`);
  } else {
    console.log(`Relay Whitelist Service: [${requestId}] allowed`);
  }

  return hasMatch;
}
