import { ClientEventEmitter } from "@/server/utils/sse/types";
import { DefaultMap } from "~/types/default-map";

const emittersStore = new DefaultMap<string, Set<ClientEventEmitter>>(
  () => new Set()
);

export const ClientEmittersRegistry = {
  register: (clientId: string, emitter: ClientEventEmitter) => {
    emittersStore.get(clientId).add(emitter);
  },
  unregister: (clientId: string, emitter: ClientEventEmitter) => {
    emittersStore.get(clientId).delete(emitter);

    if (emittersStore.get(clientId).size === 0) {
      emittersStore.delete(clientId);
    }
  },
  emit: async (clientId: string, event: string, data: any) => {
    const emitters = emittersStore.get(clientId);
    const promises = Array.from(emitters).map((emitter) =>
      emitter(event, data)
    );
    await Promise.allSettled(promises);
  },
  getEmitters: () => {
    return emittersStore.values().flatMap((emitters) => Array.from(emitters));
  },
};
