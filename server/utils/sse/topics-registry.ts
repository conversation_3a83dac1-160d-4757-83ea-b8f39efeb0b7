import { DefaultMap } from "~/types/default-map";

const topicsToClients = new DefaultMap<string, Set<string>>(() => new Set());
const clientsToTopics = new DefaultMap<string, Set<string>>(() => new Set());

export const TopicsRegistry = {
  register: (topic: string, clientId: string) => {
    topicsToClients.get(topic).add(clientId);
    clientsToTopics.get(clientId).add(topic);
  },
  unregister: (topic: string, clientId: string) => {
    topicsToClients.get(topic).delete(clientId);
    clientsToTopics.get(clientId).delete(topic);
  },
  unregisterClient: (clientId: string) => {
    clientsToTopics.get(clientId).forEach((topic) => {
      topicsToClients.get(topic).delete(clientId);
    });
    clientsToTopics.delete(clientId);
  },
  getClientsForTopic: (topic: string) => {
    return topicsToClients.get(topic);
  },
};
