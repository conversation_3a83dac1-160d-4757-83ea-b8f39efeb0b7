type Task = () => Promise<void>;

interface TaskQueueStats {
  queueSize: number;
  running: number;
  totalProcessed: number;
}

export class TaskQueue {
  private concurrency: number;
  private running = 0;
  private queue: Task[] = [];
  private totalProcessed = 0;

  constructor(concurrency = 1) {
    this.concurrency = concurrency;
  }

  // Add a job (a function returning a Promise)
  enqueue(jobFn: Task): void {
    this.queue.push(jobFn);
    this._next();
  }

  // Get queue statistics
  getStats(): TaskQueueStats {
    return {
      queueSize: this.queue.length,
      running: this.running,
      totalProcessed: this.totalProcessed,
    };
  }

  // Try to start more jobs if we're under the limit
  private _next(): void {
    // Nothing to do if we're at max concurrency or queue is empty
    if (this.running >= this.concurrency) {
      console.info(
        `TaskQueue: concurrency limit reached, running: ${this.running}, concurrency: ${this.concurrency}, queue: ${this.queue.length}`
      );
      return;
    }

    if (this.queue.length === 0) {
      console.info("TaskQueue: queue is empty");
      return;
    }

    // Grab the next job
    const jobFn = this.queue.shift()!;
    this.running++;

    // Run it (and regardless of success/failure, decrement + try next)
    Promise.resolve()
      .then(() => jobFn())
      .then(() => {
        this.totalProcessed++;
      })
      .catch((err) => {
        console.error("Job error:", err);
      })
      .finally(() => {
        this.running--;
        this._next();
      });

    // Keep filling up to concurrency
    this._next();
  }
}
