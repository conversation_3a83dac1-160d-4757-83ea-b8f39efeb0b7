import { TaskQueue } from "../task-queue";

let sseTaskQueue: TaskQueue | null = null;

export function getSSETaskQueue(): TaskQueue {
  if (!sseTaskQueue) {
    const config = useRuntimeConfig();
    const concurrency = config.queueWorkerCount || 1;

    sseTaskQueue = new TaskQueue(concurrency);
    console.info(`SSE TaskQueue created with concurrency: ${concurrency}`);
  }

  return sseTaskQueue;
}

export async function shutdownSSETaskQueue(): Promise<void> {
  if (sseTaskQueue) {
    // No explicit shutdown needed - jobs finish naturally
    sseTaskQueue = null;
    console.info("SSE TaskQueue shutdown");
  }
}
