import type { ClientEventEmitter } from "./types";
import type { TaskQueue } from "./task-queue";

export function startHeartbeat(
  queue: TaskQueue,
  emit: ClientEventEmitter,
  clientId: string,
  intervalMs = 10_000
) {
  queue.enqueue(() => emit("heartbeat", {}));

  const timer = setInterval(() => {
    queue.enqueue(async () => {
      try {
        console.log(`[SSE] heartbeat ticked for client ${clientId}`);
        await emit("heartbeat", {});
        console.log(`[SSE] heartbeat emitted for client ${clientId}`);
      } catch (err) {
        console.error(`[SSE] heartbeat error for client ${clientId}:`, err);
      }
    });
  }, intervalMs);

  return () => clearInterval(timer);
}
