import { ClientEmittersRegistry } from "@/server/utils/sse/client-registry";
import { TopicsRegistry } from "@/server/utils/sse/topics-registry";
import { ClientEventEmitter } from "./types";

export const ServerEventsMediator = {
  handleNewClient: (clientId: string, emitter: ClientEventEmitter) => {
    console.info(`SSEMediator: Registering client ${clientId}`);
    ClientEmittersRegistry.register(clientId, emitter);
  },
  handleClientDisconnect: (clientId: string, emitter: ClientEventEmitter) => {
    console.info(`SSEMediator: Unregistering client ${clientId}`);
    ClientEmittersRegistry.unregister(clientId, emitter);
    TopicsRegistry.unregisterClient(clientId);
  },
  subscribeToTopic: (topic: string, clientId: string) => {
    console.info(
      `SSEMediator: Subscribing to topic ${topic} for client ${clientId}`
    );
    TopicsRegistry.register(topic, clientId);
  },
  unsubscribeFromTopic: (topic: string, clientId: string) => {
    console.info(
      `SSEMediator: Unsubscribing from topic ${topic} for client ${clientId}`
    );
    TopicsRegistry.unregister(topic, clientId);
  },
  emitToTopic: async (topic: string, eventName: string, data: any) => {
    console.info(
      `SSEMediator: Emitting event '${eventName}' to topic '${topic}'`
    );
    const clients = TopicsRegistry.getClientsForTopic(topic);
    const promises = Array.from(clients).map(async (clientId) => {
      console.info(
        `SSEMediator: Emitting event '${eventName}' to client '${clientId}'`
      );
      return ClientEmittersRegistry.emit(clientId, eventName, data);
    });
    await Promise.allSettled(promises);
  },
};
