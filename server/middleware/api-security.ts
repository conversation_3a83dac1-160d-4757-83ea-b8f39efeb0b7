import { UserSession } from "#auth-utils";
import { isGigiAdmin } from "~/utils/auth";
import { H3Event } from "h3";

type Endpoint = {
  path: string;
  method?: string;
};

export default defineEventHandler(async (event) => {
  let userSession: UserSession = await getUserSession(event);
  const isAdmin = isGigiAdmin(userSession?.user);

  if (isAdmin) return;

  const endpoint = event.path.split("?")[0];
  if (!endpoint.startsWith("/api")) return;

  const endpointClassification = classifyEndpoint(endpoint, event.method);

  switch (endpointClassification) {
    case "anonymous":
      return anonymousHandler(event);
    case "internal":
      return internalHandler(event);
    case "authenticatedPublic":
      return authenticatedPublicHandler(event);
    default:
      return forbiddenResponse(event);
  }
});

function anonymousHandler(event: H3Event) {
  return;
}

function internalHandler(event: H3Event) {
  const runtimeConfig = useRuntimeConfig();
  const hasValidHeaderToken =
    event.headers.get("Gigi-Access-Token") === runtimeConfig.selfAccessToken;

  if (hasValidHeaderToken) {
    return;
  }

  return forbiddenResponse(event);
}

async function authenticatedPublicHandler(event: H3Event) {
  const userSession = await getUserSession(event);
  if (userSession.user) return;

  return forbiddenResponse(event);
}

function classifyEndpoint(path: string, method: string) {
  if (isAnonymousEndpoint(path, method)) return "anonymous";
  if (isInternalEndpoint(path, method)) return "internal";
  if (isAuthenticatedPublicEndpoint(path, method)) return "authenticatedPublic";
  return;
}

function isAnonymousEndpoint(path: string, method: string) {
  if (!path || !method) return false;

  const anonymousEndpoints = [
    { path: "/api/_auth/session" },
    { path: "/api/healthcheck", method: "GET" },
    { path: "/api/auth/magic-link", method: "POST" },
    { path: "/api/auth/google", method: "GET" },
    { path: "/api/auth/refresh", method: "POST" },
    { path: "/api/auth/otp", method: "GET" },
  ];

  return validateEndpoint(path, method, anonymousEndpoints);
}

function isInternalEndpoint(path: string, method: string) {
  const internalEndpoints = [
    { path: "/api/events/internal/publish", method: "POST" },
    { path: "/api/events/internal/test-stream", method: "GET" },
  ];
  return validateEndpoint(path, method, internalEndpoints);
}

function isAuthenticatedPublicEndpoint(path: string, method: string) {
  const authenticatedPublicEndpoints = [
    { path: "/api/events/stream", method: "GET" },
    { path: "/api/events/subscriptions", method: "POST" },
    { path: "/api/events/subscriptions", method: "DELETE" },
  ];
  return validateEndpoint(path, method, authenticatedPublicEndpoints);
}

function validateEndpoint(
  path: string,
  method: string,
  endpointRegistry: Endpoint[]
) {
  return endpointRegistry.some(
    (endpoint) =>
      endpoint.path === path && (!endpoint.method || method === endpoint.method)
  );
}

function forbiddenResponse(event: H3Event) {
  event.respondWith(
    new Response(JSON.stringify({ error: "Forbidden" }), {
      status: 403,
      headers: { "Content-Type": "application/json" },
    })
  );

  return false;
}
