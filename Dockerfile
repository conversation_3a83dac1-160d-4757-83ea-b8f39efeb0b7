# Use an official Node.js image as the base image
FROM node:20-alpine AS build

# Set the working directory
WORKDIR /app

# Copy package.json and pnpm-lock.yaml to the working directory
COPY package.json pnpm-lock.yaml ./

# Enable corepack to ensure the correct version of pnpm specified in package.json is used
RUN corepack enable

# Install dependencies
RUN pnpm install  --frozen-lockfile

# Copy the rest of the application to the working directory
COPY . .

# Build the application
RUN pnpm run build

# Create a new stage for the final image to keep it lean
FROM node:20-alpine

# Set the working directory
WORKDIR /app

# Copy only the necessary files from the build stage
COPY --from=build /app/.output ./

# Expose the port the app runs on
EXPOSE 3000

# Command to run the app
CMD ["node", "server/index.mjs"]