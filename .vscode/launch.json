{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "client: chrome",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "node-terminal",
      "request": "launch",
      "name": "server: nuxt",
      "outputCapture": "std",
      "cwd": "${workspaceFolder}",
      "command": "pnpm run dev",
      "envFile": "${input:pickEnvFile}",
    }
  ],
  "compounds": [
    {
      "name": "fullstack: nuxt",
      "configurations": ["server: nuxt", "client: chrome"]
    }
  ],
  "inputs": [
    {
      "id": "pickEnvFile",
      "type": "pickString",
      "options": [
        {
          "label": ".env.local",
          "value": "${workspaceFolder}/.env.local"
        },
        {
          "label": ".env.dev",
          "value": "${workspaceFolder}/.env.dev"
        }
      ],
      "description": "Select an environment file",
      "default": "${workspaceFolder}/.env.local"
    }
  ]
}
