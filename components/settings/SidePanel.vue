<script setup lang="ts">
const pages = [
  // {
  //   groupLabel: "Account",
  //   items: [
  //     {
  //       label: "Profile",
  //       icon: "person",
  //       to: "/settings",
  //     },
  //     {
  //       label: "Integrations",
  //       icon: "share",
  //       to: "/settings/integrations",
  //     },
  //   ],
  // },
  {
    groupLabel: "Team Space",
    items: [
      {
        label: "Organization Practices",
        icon: "home_storage",
        to: "/settings/agency-practices",
      },
      {
        label: "Advertisers",
        icon: "corporate_fare",
        to: "/settings/advertisers",
      },
    ],
  },
  {
    groupLabel: "General Management",
    items: [
      {
        label: "Billing",
        icon: "description",
        to: "/settings/billing",
      },
      {
        label: "Export Data",
        icon: "upgrade",
        to: "/settings/export-data",
      },
    ],
  },
];

onMounted(() => navigateTo("/settings/agency-practices"));
</script>

<template>
  <div class="flex flex-col">
    <ul class="flex flex-col gap-6">
      <li
        v-for="page in pages"
        :key="page.groupLabel"
        class="flex flex-col gap-6"
      >
        <span class="label-3 text-disabled-text">
          {{ page.groupLabel }}
        </span>

        <ul class="flex flex-col">
          <NuxtLink
            v-for="item in page.items"
            :key="item.label"
            class="flex items-center gap-2 px-4 py-3 h-10 selected-link w-56 transition-all duration-100 body-1"
            :data-selected="item.to === decodeURIComponent($route.path)"
            :to="item.to"
          >
            <i class="material-symbols-outlined" style="font-size: 16px">{{
              item.icon
            }}</i>
            <span>{{ item.label }}</span>
          </NuxtLink>
        </ul>
      </li>
    </ul>
  </div>
</template>

<style scoped>
.selected-link[data-selected="true"] {
  @apply border rounded-lg border-neutral-300 bg-neutral-100 !label-3;
}
</style>
