<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

defineProps<
  KnowledgeItemComponentProps<KnowledgeType.CURRENT_OBJECTIVES_SETTINGS>
>();

const emit =
  defineEmits<
    KnowledgeItemComponentEmits<KnowledgeType.CURRENT_OBJECTIVES_SETTINGS>
  >();
</script>

<template>
  <EditableContainer
    #default="{ data }"
    :label="item.displayName"
    :description="item.description"
    :data="item"
    placement="top-right"
    @save="emit('save', $event)"
    @cancel="emit('cancel', $event)"
  >
    <Textarea
      v-model="data.payload.objectives"
      placeholder="Enter your objectives"
    />
  </EditableContainer>
</template>

<style scoped>
::v-deep(.p-textarea) {
  @apply h-28 resize-none;
}
</style>
