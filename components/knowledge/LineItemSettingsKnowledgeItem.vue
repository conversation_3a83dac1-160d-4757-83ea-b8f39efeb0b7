<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";
import SettingsEditableContainer from "~/components/common/SettingsEditableContainer.vue";

defineProps<KnowledgeItemComponentProps<KnowledgeType.LINE_ITEM_SETTINGS>>();
const emit =
  defineEmits<KnowledgeItemComponentEmits<KnowledgeType.LINE_ITEM_SETTINGS>>();

const defaultConfiguration =
  "I run 5 line items for display campaigns and each on is a different inventory.\nAmazon Desktop - which is Amazon Owned and Operated inventory on desktop devices.\nAmazon Mobile - which is Amazon Owned and Operated inventory on mobile devices.\nAmazon Mobile Apps - which is 3rd party inventory on mobile devices.\nAmazon Publisher Services - which are all the Amazon publisher services inventory.\n3P - any 3rd party inventory.\nFor OLV I only have one line item with the audience name configured and I use all 3p inventory.\nFor STV I use many line items depending on my audience strategy. If I have 5 audiences, I will have 5 line items, one for each audience.";

const textareaClasses = (isDefaultContent: boolean) => [
  "w-full h-full resize-none border-none bg-transparent pt-3 pr-4 pb-4 pl-4 focus:outline-none body-1",
  isDefaultContent ? "text-surface-700" : "text-surface-900",
];
</script>

<template>
  <SettingsEditableContainer
    #default="{ data, isDefaultContent, onInput }"
    :data="item"
    :height="200"
    :default-content="defaultConfiguration"
    field-path="payload.configuration"
    label="Tell us how you like your Line Items separated"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
  >
    <Textarea
      v-model="data.payload.configuration"
      :class="textareaClasses(isDefaultContent)"
      placeholder="Here's an example for what we do for STV ads: Prime Video STV - Frequency - TOF Audiences. We change out each part between the dashes depending on the KPI for the order and the part of the funnel that it's targeting."
      @input="onInput"
    />
  </SettingsEditableContainer>
</template>
