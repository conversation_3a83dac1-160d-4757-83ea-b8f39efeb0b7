<script setup lang="ts">
const isDrawerVisible = ref(false);
const searchQuery = ref("");
const knowledgeFiles = ref<KnowledgeItem<KnowledgeType.FILE>[]>([]);
const errorMessage = ref<string>();

const agencyStore = useAgencyStore();

const confirm = useConfirm();

const removeAllConfirmation = (event: Event) =>
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: "Are you sure you want to remove all knowledge files?",
    rejectProps: {
      label: "Cancel",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Delete",
      severity: "danger",
    },
    accept: async () => await removeAllKnowledgeFiles(),
    reject: () => {},
  });

const removeSingleConfirmation = (event: Event, knowledgeId?: string) =>
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: "Are you sure you want to remove this knowledge file?",
    rejectProps: {
      label: "Cancel",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Delete",
      severity: "danger",
    },
    accept: async () => await removeKnowledgeFile(knowledgeId),
    reject: () => {},
  });

const filteredKnowledgeFiles = computed(() => {
  return knowledgeFiles.value.filter((file) =>
    file.displayName.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

onMounted(async () => {
  await getKnowledgeFiles({
    agencyIds: [agencyStore.activeAgency?.id ?? ""],
  });
});

async function getKnowledgeFiles(scopeFilter?: ScopeFilter) {
  errorMessage.value = undefined;
  const [newKnowledgeFiles, error] = await useKnowledge().getKnowledgeFiles(
    scopeFilter
  );
  if (error) {
    errorMessage.value = error.message;
  } else {
    knowledgeFiles.value = newKnowledgeFiles;
  }
}

async function removeKnowledgeFile(fileId?: string) {
  if (!fileId) return;
  await useKnowledge().deleteKnowledgeFile(fileId);
  await getKnowledgeFiles();
}

async function removeAllKnowledgeFiles() {
  const deletePromises = knowledgeFiles.value.map(async (knowledgeFile) => {
    await useKnowledge().deleteKnowledgeFile(knowledgeFile.payload.fileId);
  });
  await Promise.allSettled(deletePromises);
  await getKnowledgeFiles();
}
</script>

<template>
  <button
    class="flex flex-row gap-2 items-center text-center rounded-md border border-default-border h-8 px-4 py-2 label-3"
    @click="isDrawerVisible = true"
  >
    <MaterialIcon icon="settings" size="1rem" />
    <span>Knowledge Base</span>
  </button>

  <Drawer
    v-model:visible="isDrawerVisible"
    modal
    position="right"
    header="Knowledge Base"
    style="width: 48rem"
  >
    <div class="flex flex-col gap-6">
      <div class="flex flex-row gap-4 items-center justify-between">
        <IconField>
          <InputIcon class="flex justify-center items-center">
            <i class="material-symbols-outlined">search</i>
          </InputIcon>
          <InputText v-model="searchQuery" placeholder="Search" size="small" />
        </IconField>

        <GFileUpload
          label="Upload"
          :metadata="{
            knowledge: true,
            agencyId: agencyStore.activeAgency!.id,
          }"
          @upload-done="getKnowledgeFiles"
        />
      </div>

      <div class="flex flex-col gap-2 justify-start">
        <Message
          v-if="errorMessage"
          severity="error"
          class="self-center w-full flex place-content-center"
        >
          <span class="inline-flex gap-4 place-content-center">
            <i class="material-symbols-outlined">error</i>
            {{ errorMessage }}
          </span>
        </Message>

        <Message
          v-else-if="filteredKnowledgeFiles.length === 0"
          severity="info"
          class="self-center w-full flex place-content-center"
        >
          <span class="inline-flex gap-4 place-content-center">
            <i class="material-symbols-outlined">info</i>
            No files found
          </span>
        </Message>

        <ul v-else class="flex flex-col justify-start">
          <li
            v-for="(file, index) in filteredKnowledgeFiles"
            :key="file.id"
            class="flex flex-col items-start justify-start"
          >
            <div
              class="flex flex-row w-full gap-2 items-center justify-between py-6"
            >
              <span class="flex flex-row gap-2 items-center justify-start">
                <i
                  :class="[
                    'material-symbols-outlined rounded select-none p-1',
                    getFileTypeByExtension(file.displayName).color,
                  ]"
                >
                  {{ getFileTypeByExtension(file.displayName).icon }}
                </i>
                <span class="text-sm text-neutral-900">
                  {{ file.displayName }}
                </span>
              </span>

              <MaterialIcon
                icon="delete"
                size="1rem"
                clickable
                class="hover:text-red-500 transition-all duration-200"
                @click="removeSingleConfirmation($event, file.payload.fileId)"
              />
            </div>

            <Divider
              v-if="index < filteredKnowledgeFiles.length - 1"
              class="!p-0 !m-0"
            />
          </li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="flex flex-row gap-2 items-center justify-between">
        <Button
          label="Remove All"
          severity="danger"
          outlined
          :disabled="filteredKnowledgeFiles.length === 0"
          @click="removeAllConfirmation($event)"
        >
          <template #icon>
            <MaterialIcon icon="delete" size="1.25rem" />
          </template>
        </Button>
        <Button label="Finish" @click="isDrawerVisible = false" />
      </div>
    </template>
  </Drawer>
</template>
