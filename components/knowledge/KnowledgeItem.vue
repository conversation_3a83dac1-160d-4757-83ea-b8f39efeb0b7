<script setup lang="ts">
import KpiSettingsKnowledgeItem from "~/components/knowledge/KpiSettingsKnowledgeItem.vue";
import NamingSettingsKnowledgeItem from "~/components/knowledge/NamingSettingsKnowledgeItem.vue";
import TargetingSettingsKnowledgeItem from "~/components/knowledge/TargetingSettingsKnowledgeItem.vue";
import LineItemSettingsKnowledgeItem from "~/components/knowledge/LineItemSettingsKnowledgeItem.vue";
import CurrentObjectivesKnowledgeItem from "~/components/knowledge/CurrentObjectivesKnowledgeItem.vue";
import DspStrategySettingsKnowledgeItem from "~/components/knowledge/DspStrategySettingsKnowledgeItem.vue";
import AsinsKnowledgeItem from "~/components/knowledge/AsinsKnowledgeItem.vue";
import CompetitorsKnowledgeItem from "~/components/knowledge/CompetitorsKnowledgeItem.vue";
import BrandSafetyKnowledgeItem from "~/components/knowledge/BrandSafetyKnowledgeItem.vue";
import AgencyAdditionalInfoKnowledgeItem from "~/components/knowledge/AgencyAdditionalInfoKnowledgeItem.vue";

const knowledgeItemComponentRegistry = {
  [KnowledgeType.KPI_SETTINGS]: KpiSettingsKnowledgeItem,
  [KnowledgeType.NAMING_SETTINGS]: NamingSettingsKnowledgeItem,
  [KnowledgeType.TARGETING_SETTINGS]: TargetingSettingsKnowledgeItem,
  [KnowledgeType.LINE_ITEM_SETTINGS]: LineItemSettingsKnowledgeItem,
  [KnowledgeType.CURRENT_OBJECTIVES_SETTINGS]: CurrentObjectivesKnowledgeItem,
  [KnowledgeType.DSP_STRATEGY_SETTINGS]: DspStrategySettingsKnowledgeItem,
  [KnowledgeType.ASIN_GROUPING_SETTINGS]: AsinsKnowledgeItem,
  [KnowledgeType.COMPETITORS_ASINS_SETTINGS]: CompetitorsKnowledgeItem,
  [KnowledgeType.BRAND_SAFETY_SETTINGS]: BrandSafetyKnowledgeItem,
  [KnowledgeType.AGENCY_ADDITIONAL_INFO]: AgencyAdditionalInfoKnowledgeItem,
  [KnowledgeType.FILE]: undefined,
};

const props = defineProps<{
  title?: string;
  description?: string;
  item: KnowledgeItem<KnowledgeType>;
  direction?: "row" | "column";
  disabled?: boolean;
  titleDescriptionGap?: "normal" | "large";
}>();

const emit = defineEmits<{
  (e: "saved", item: KnowledgeItem<KnowledgeType>): void;
}>();

const toast = useToast();

async function saveChanges(knowledgeItem: KnowledgeItem<KnowledgeType>) {
  try {
    if (knowledgeItem.id) {
      await useKnowledge().batchSetKnowledge({
        update: [knowledgeItem],
      });
    } else {
      await useKnowledge().batchSetKnowledge({
        add: [knowledgeItem],
      });
    }
    emit("saved", knowledgeItem);
    toast.add({
      severity: "success",
      summary: "Success",
      detail: "Changes saved",
      life: 3000,
    });
  } catch (error) {
    console.error(error);
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "Failed to save changes",
      life: 3000,
    });
  }
}
</script>
<template>
  <div
    class="flex w-full flex-row items-start justify-between gap-6 flex-grow-1 flex-shrink-0 flex-basis-0"
    :class="{ 'flex-col': direction === 'column' }"
  >
    <div
      class="flex flex-col flex-1"
      :class="{
        'gap-4': props.titleDescriptionGap === 'large',
        'gap-2': props.titleDescriptionGap === 'normal',
      }"
    >
      <span class="flex flex-row items-center justify-between gap-2">
        <h3 v-if="title" class="label-4 text-primary-text">
          {{ title }}
        </h3>
      </span>
      <p v-if="description" class="body-1 text-disabled-text">
        {{ description }}
      </p>
    </div>

    <div class="w-[30.5rem]" :class="{ 'w-full': direction === 'column' }">
      <component
        :is="knowledgeItemComponentRegistry[item.knowledgeType]"
        :item="item as any"
        :disabled="disabled"
        @save="saveChanges"
      />
    </div>
  </div>
</template>
