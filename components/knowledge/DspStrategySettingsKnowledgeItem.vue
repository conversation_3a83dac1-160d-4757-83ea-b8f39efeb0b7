<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

defineProps<KnowledgeItemComponentProps<KnowledgeType.DSP_STRATEGY_SETTINGS>>();
defineEmits<KnowledgeItemComponentEmits<KnowledgeType.DSP_STRATEGY_SETTINGS>>();
</script>

<template>
  <EditableContainer
    #default="{ data }"
    :label="item.displayName"
    :description="item.description"
    :data="item"
    placement="top-right"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
  >
    <Textarea
      v-model="data.payload.strategy"
      class="w-full"
      placeholder="Enter your strategy"
    />
  </EditableContainer>
</template>

<style scoped>
::v-deep(.p-textarea) {
  @apply h-28 resize-none;
}
</style>
