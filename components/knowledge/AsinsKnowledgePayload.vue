<script
  setup
  lang="ts"
  generic="T extends KnowledgeType.ASIN_GROUPING_SETTINGS | KnowledgeType.COMPETITORS_ASINS_SETTINGS"
>
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

const props = defineProps<KnowledgeItemComponentProps<T>>();
const emit = defineEmits<KnowledgeItemComponentEmits<T>>();

const editedGroupIndex = ref<number>();
const editableItem = ref<KnowledgeItem<T>>();

editableItem.value = JSON.parse(JSON.stringify(props.item));

watch(props.item, (newVal) => {
  editableItem.value = JSON.parse(JSON.stringify(newVal));
});

function onSave(data: AsinGroup, index: number) {
  updateEditableItem(data, index);
  emit("save", editableItem.value!);
}

function onCancel(data: AsinGroup, index: number) {
  updateEditableItem(data, index);
  emit("cancel", editableItem.value!);
}

function updateEditableItem(data: AsinGroup, index: number) {
  editableItem.value!.payload.groups[index] = data;
}

function addGroup() {
  editableItem.value?.payload.groups.push({
    name: "New Group",
    asins: [],
  });
}

function removeGroup(data: AsinGroup, index: number) {
  editableItem.value!.payload.groups.splice(index, 1);

  if (
    props.item.payload.groups.findIndex((group) => group.name === data.name) !==
    -1
  ) {
    emit("save", editableItem.value!);
  }
}
</script>

<template>
  <div class="flex flex-col gap-4">
    <Message
      v-if="editableItem?.payload.groups.length === 0"
      severity="info"
      :closable="false"
      pt:text:class="flex flex-row gap-2 items-center justify-start text-sm"
    >
      <MaterialIcon icon="info" size="1rem" />
      <span>
        No groups available at this time. Use the "Add Group" button to create a
        new group.
      </span>
    </Message>

    <div
      v-else
      class="flex h-full flex-col gap-6"
      @click="editedGroupIndex = undefined"
    >
      <EditableContainer
        v-for="(group, index) in editableItem?.payload.groups"
        :key="`${group.name}-${index}`"
        #default="{ data, hasChanges }"
        :data="group"
        placement="top-right"
        @save="(data) => onSave(data, index)"
        @cancel="(data) => onCancel(data, index)"
      >
        <div class="flex flex-row gap-2">
          <div
            v-if="editedGroupIndex !== index"
            class="flex w-full flex-row gap-2 items-center justify-between"
          >
            <label
              class="font-semibold font-literata text-base cursor-pointer"
              @click.stop="editedGroupIndex = index"
            >
              {{ data.name }}
            </label>

            <div
              class="opacity-100 transition-opacity"
              :class="{ 'opacity-0': hasChanges }"
            >
              <MaterialIcon
                icon="delete"
                size="1rem"
                clickable
                @click.stop="() => removeGroup(data, index)"
              />
            </div>
          </div>
          <InputText
            v-if="editedGroupIndex === index"
            v-model="data.name"
            size="small"
            @click.stop
            @blur="editedGroupIndex = undefined"
            @keydown.enter="editedGroupIndex = undefined"
          />
        </div>
        <Textarea
          :value="data.asins"
          class="w-full limited-textarea"
          :disabled="disabled"
          :rows="1"
          autoResize
          @input="
            data.asins = ($event.target as HTMLTextAreaElement).value.split(',')
          "
        />
      </EditableContainer>
    </div>

    <button
      class="flex flex-row gap-2 w-fit items-center text-center rounded-md border border-default-border h-8 px-4 py-2 label-3"
      @click="addGroup"
    >
      <MaterialIcon icon="add" size="1rem" />
      Add Group
    </button>
  </div>
</template>

<style>
.limited-textarea {
  max-height: calc(1.5em * 5 + 2 * 0.75rem) !important; /* 5 rows + padding */
  overflow-y: auto !important;
}
</style>
