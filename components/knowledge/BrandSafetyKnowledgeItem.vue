<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

defineProps<KnowledgeItemComponentProps<KnowledgeType.BRAND_SAFETY_SETTINGS>>();
defineEmits<KnowledgeItemComponentEmits<KnowledgeType.BRAND_SAFETY_SETTINGS>>();
</script>

<template>
  <EditableContainer
    #default="{ data }"
    :data="item"
    label="Domain Exclusions"
    placement="top-right"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
  >
    <Textarea
      :value="data.payload.excludedDomains"
      class="w-full limited-textarea"
      :rows="1"
      autoResize
      @input="
        data.payload.excludedDomains = (
          $event.target as HTMLTextAreaElement
        ).value.split(',')
      "
    />
  </EditableContainer>
</template>
