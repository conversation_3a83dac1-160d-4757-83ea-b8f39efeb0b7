<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";
import AsinsKnowledgePayload from "./AsinsKnowledgePayload.vue";

defineProps<
  KnowledgeItemComponentProps<KnowledgeType.ASIN_GROUPING_SETTINGS>
>();

defineEmits<
  KnowledgeItemComponentEmits<KnowledgeType.ASIN_GROUPING_SETTINGS>
>();
</script>

<template>
  <AsinsKnowledgePayload
    :item="item"
    :disabled="disabled"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
  />
</template>
