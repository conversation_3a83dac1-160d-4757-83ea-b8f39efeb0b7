<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";
import SettingsEditableContainer from "~/components/common/SettingsEditableContainer.vue";

defineProps<KnowledgeItemComponentProps<KnowledgeType.NAMING_SETTINGS>>();
defineEmits<KnowledgeItemComponentEmits<KnowledgeType.NAMING_SETTINGS>>();

const defaultOrderContent =
  "Here's an example for what we do for STV ads: Prime Video STV - Frequency - TOF Audiences.\nWe change out each part between the dashes depending on the KPI for the order and the part of the funnel that it's targeting.";
const defaultLineItemContent =
  "Here is an example: Multiple Products | AMZ Mobile Display | Retargeting | L90D STV Viewers\nWhich breaks down like this: Product Category | Inventory | Type of Campaign | Audience";

const textareaClasses = (isDefaultContent: boolean) => [
  "w-full h-full resize-none border-none bg-transparent pt-3 pr-4 pb-4 pl-4 focus:outline-none body-1",
  isDefaultContent ? "text-surface-700" : "text-surface-900",
];
</script>

<template>
  <div class="flex flex-col gap-6">
    <SettingsEditableContainer
      #default="{ data, isDefaultContent, onInput }"
      :data="item"
      :height="200"
      :default-content="defaultOrderContent"
      field-path="payload.orderNameTemplate"
      label="How do you name your orders? You can give a couple examples"
      title-description-gap="large"
      @save="$emit('save', $event)"
      @cancel="$emit('cancel', $event)"
    >
      <Textarea
        v-model="data.payload.orderNameTemplate"
        :class="textareaClasses(isDefaultContent)"
        placeholder="Here's an example for what we do for STV ads: Prime Video STV - Frequency - TOF Audiences. We change out each part between the dashes depending on the KPI for the order and the part of the funnel that it's targeting"
        @input="onInput"
      />
    </SettingsEditableContainer>

    <SettingsEditableContainer
      #default="{ data, isDefaultContent, onInput }"
      :data="item"
      :height="200"
      :default-content="defaultLineItemContent"
      field-path="payload.lineItemNameTemplate"
      label="What are your Line Item name conventions?"
      title-description-gap="large"
      @save="$emit('save', $event)"
      @cancel="$emit('cancel', $event)"
    >
      <Textarea
        v-model="data.payload.lineItemNameTemplate"
        :class="textareaClasses(isDefaultContent)"
        placeholder="Here is an example: Multiple Products | AMZ Mobile Display | Retargeting | L90D STV Viewers. Which breaks down like this: Product Category | Inventory | Type of Campaign | Audience"
        @input="onInput"
      />
    </SettingsEditableContainer>
  </div>
</template>
