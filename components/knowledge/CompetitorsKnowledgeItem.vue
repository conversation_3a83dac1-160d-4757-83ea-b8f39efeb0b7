<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";
import AsinsKnowledgePayload from "./AsinsKnowledgePayload.vue";

defineProps<
  KnowledgeItemComponentProps<KnowledgeType.COMPETITORS_ASINS_SETTINGS>
>();

defineEmits<
  KnowledgeItemComponentEmits<KnowledgeType.COMPETITORS_ASINS_SETTINGS>
>();
</script>

<template>
  <div class="flex flex-col gap-6">
    <EditableContainer
      #default="{ data }"
      label="Competitors Brands"
      :data="item"
      placement="top-right"
      @save="$emit('save', $event)"
      @cancel="$emit('cancel', $event)"
    >
      <Textarea
        :value="data.payload.competitors"
        class="w-full limited-textarea"
        :disabled="disabled"
        :rows="1"
        autoResize
        @input="
          data.payload.competitors = (
            $event.target as HTMLTextAreaElement
          ).value.split(',')
        "
      />
    </EditableContainer>

    <Divider />

    <AsinsKnowledgePayload
      :item="item"
      :disabled="disabled"
      @save="$emit('save', $event)"
      @cancel="$emit('cancel', $event)"
    />
  </div>
</template>
