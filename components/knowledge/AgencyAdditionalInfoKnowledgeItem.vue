<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";
import SettingsEditableContainer from "~/components/common/SettingsEditableContainer.vue";

defineProps<
  KnowledgeItemComponentProps<KnowledgeType.AGENCY_ADDITIONAL_INFO>
>();
defineEmits<
  KnowledgeItemComponentEmits<KnowledgeType.AGENCY_ADDITIONAL_INFO>
>();

const defaultInfo = "";

const textareaClasses = (isDefaultContent: boolean) => [
  "w-full h-full resize-none border-none bg-transparent pt-3 pr-4 pb-4 pl-4 focus:outline-none body-1",
  isDefaultContent ? "text-surface-700" : "text-surface-900",
];
</script>

<template>
  <SettingsEditableContainer
    #default="{ data, isDefaultContent, onInput }"
    :data="item"
    :height="120"
    :default-content="defaultInfo"
    field-path="payload.info"
    label="Anything else you'd like <PERSON><PERSON> to know?"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
  >
    <Textarea
      v-model="data.payload.info"
      :class="textareaClasses(isDefaultContent)"
      placeholder="We have meetings each week on Wednesdays..."
      @input="onInput"
    />
  </SettingsEditableContainer>
</template>
