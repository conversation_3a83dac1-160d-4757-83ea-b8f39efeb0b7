<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

const props =
  defineProps<KnowledgeItemComponentProps<KnowledgeType.KPI_SETTINGS>>();

defineEmits<KnowledgeItemComponentEmits<KnowledgeType.KPI_SETTINGS>>();

const editableItem = ref<KnowledgeItem<KnowledgeType.KPI_SETTINGS>>();
const funnels = computed(() =>
  editableItem.value?.payload.funnelKpis.map(
    (funnel: { funnelName: string }) => funnel.funnelName
  )
);

const selectedFunnel = ref(props.item.payload.funnelKpis[0].funnelName);
const selectedFunnelKpis = computed(
  () =>
    editableItem.value?.payload.funnelKpis.find(
      (funnel) => funnel.funnelName === selectedFunnel.value
    )?.kpis
);

function addKpi() {
  selectedFunnelKpis.value?.push({
    kpiName: "New KPI",
    importance: 0,
  });
}

function removeKpi(data: any) {
  selectedFunnelKpis.value?.splice(selectedFunnelKpis.value.indexOf(data), 1);
}

function onCellEditComplete(event: any) {
  let { data, newValue, field } = event;

  data[field] = newValue;
}
</script>
<template>
  <EditableContainer
    :label="item.displayName"
    :description="item.description"
    :data="item"
    placement="bottom-right"
    placement-class="!translate-y-0"
    @save="$emit('save', $event)"
    @cancel="$emit('cancel', $event)"
    @update:data="editableItem = $event"
  >
    <DataTable
      :value="selectedFunnelKpis"
      edit-mode="cell"
      pt:header:class="flex justify-between"
      pt:footer:class="p-0 pt-6 border-none"
      @cell-edit-complete="onCellEditComplete"
    >
      <template #header>
        <Tabs
          :value="selectedFunnel"
          style="
            --p-tabs-tab-active-background: linear-gradient(
              180deg,
              var(--p-neutral-100) 0%,
              var(--p-neutral-200) 100%
            );
            --p-tabs-active-bar-height: 0px;
          "
        >
          <TabList
            pt:tabList:class="rounded-full bg-neutral-50 border border-neutral-300 w-fit"
            pt:activeBar:class="hidden"
          >
            <Tab
              v-for="funnel in funnels"
              :key="funnel"
              :value="funnel"
              @click="selectedFunnel = funnel"
              pt:root:class="data-[p-active=true]:rounded-full border-none !text-sm !px-3 !py-2"
            >
              {{ funnel }}
            </Tab>
          </TabList>
        </Tabs>
      </template>

      <template #footer>
        <button
          class="flex flex-row gap-2 items-center text-center rounded-md border border-default-border h-8 px-4 py-2 label-3"
          @click="addKpi"
        >
          <MaterialIcon icon="add" size="1rem" />
          Add KPI
        </button>
      </template>
      <Column field="kpiName" header="Name" :style="{ width: '40%' }">
        <template #editor="{ data }">
          <InputText v-model="data.kpiName" size="small" />
        </template>
      </Column>
      <Column field="importance" header="Importance" :style="{ width: '40%' }">
        <template #body="{ data }">
          <span class="text-xs font-medium"> {{ data.importance }}% </span>
        </template>

        <template #editor="{ data }">
          <InputNumber v-model="data.importance" size="small" />
        </template>
      </Column>

      <Column :style="{ width: '10%' }">
        <template #body="{ data }">
          <MaterialIcon
            icon="delete"
            size="1rem"
            clickable
            @click="removeKpi(data)"
          />
        </template>
      </Column>
    </DataTable>
  </EditableContainer>
</template>
