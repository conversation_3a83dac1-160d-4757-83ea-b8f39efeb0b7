<script setup lang="ts">
import type {
  KnowledgeItemComponentProps,
  KnowledgeItemComponentEmits,
} from "~/types/knowledge";

const props = defineProps<KnowledgeItemComponentProps<KnowledgeType.TARGETING_SETTINGS>>();
const emit = defineEmits<KnowledgeItemComponentEmits<KnowledgeType.TARGETING_SETTINGS>>();
</script>

<template>
  <EditableContainer
      #default="{ data }"
      :description="item.description"
      :data="item"
      placement="top-right"
      @save="emit('save', $event)"
      @cancel="emit('cancel', $event)"
  >
    <div class="flex flex-col gap-6 w-full">
      <!-- Remarketing -->
      <div>
        <div class="mb-2 text-sm leading-[22px] font-semibold text-primary-text font-inter">
          How do you define Remarketing in terms of inclusions & exclusions?
        </div>
        <Textarea
            v-model="data.payload.remarketing"
            autoResize
            rows="3"
            class="textarea-standard"
            :disabled="props.disabled"
            placeholder="How do you define Remarketing in terms of inclusions & exclusions?"
        />
      </div>

      <!-- Retargeting -->
      <div>
        <div class="mb-2 text-sm leading-[22px] font-semibold text-primary-text font-inter">
          How do you define Retargeting in terms of inclusions & exclusions?
        </div>
        <Textarea
            v-model="data.payload.retargeting"
            autoResize
            rows="3"
            class="textarea-standard"
            :disabled="props.disabled"
            placeholder="How do you define Retargeting in terms of inclusions & exclusions?"
        />
      </div>

      <!-- NTB -->
      <div>
        <div class="mb-2 text-sm leading-[22px] font-semibold text-primary-text font-inter">
          What is your definition of NTB?
        </div>
        <Textarea
            v-model="data.payload.NTB"
            autoResize
            rows="3"
            class="textarea-standard"
            :disabled="props.disabled"
            placeholder="What is your definition of NTB?"
        />
      </div>
    </div>
  </EditableContainer>
</template>

<style scoped>
::v-deep(.textarea-standard) {
  @apply p-[12px_16px_16px_16px] rounded-lg border border-default-border bg-white text-sm text-primary resize-none w-full font-inter placeholder:text-text-disabled;
  line-height: 1.5;
}

::v-deep(.p-inputtextarea) {
  @apply w-full;
}

::v-deep(textarea) {
  @apply w-full;
  line-height: 1.5;
}

::v-deep(.p-textarea::placeholder) {
  @apply text-text-disabled;
  opacity: 1;
}
</style>

