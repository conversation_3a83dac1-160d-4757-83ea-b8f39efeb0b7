<script setup lang="ts">
const MAGIC_LINK_COOL_DOWN = 60;

const email = ref("");
const errorMessage = ref("");
const sent = ref(false);
const { countdown, isRunning, start, stop } = useCountdown();

async function sendMagicLink() {
  reset();

  const [ok, errMsg] = checkEmail();
  if (!ok) {
    errorMessage.value = errMsg;
    return;
  }

  start(MAGIC_LINK_COOL_DOWN);
  await doSendMagicLink();
}

function reset() {
  errorMessage.value = "";
  sent.value = false;
}

function checkEmail(): [boolean, string] {
  if (!email.value) return [false, "Email is required"];

  const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*\.\w+$/;
  if (!emailRegex.test(email.value)) {
    return [false, "Invalid email"];
  }

  return [true, ""];
}

async function doSendMagicLink() {
  try {
    await $fetch("/api/auth/magic-link", {
      method: "POST",
      body: { email: email.value },
    });
    sent.value = true;
  } catch (error) {
    errorMessage.value = "Failed to send magic link";
  }
}

onUnmounted(() => {
  stop();
});
</script>

<template>
  <div class="flex flex-col gap-2 w-full">
    <FloatLabel>
      <InputText
        pt:root:class="w-full"
        id="email"
        type="email"
        v-model="email"
      />
      <label for="email">Email</label>
    </FloatLabel>

    <span v-if="errorMessage" class="text-red-500 text-xs">{{
      errorMessage
    }}</span>
    <span v-if="sent" class="text-xs">
      Magic link sent, {{ countdown }} seconds left
    </span>
    <!-- TODO: enable button when we have a way to send the magic link -->
    <Button
      v-tooltip.top="{ value: 'Coming soon!', showDelay: 1000 }"
      :disabled="true || isRunning"
      @click="sendMagicLink"
    >
      Get Magic Link
    </Button>
  </div>
</template>
