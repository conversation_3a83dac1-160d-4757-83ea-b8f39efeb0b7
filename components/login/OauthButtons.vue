<script setup lang="ts">
const config = useRuntimeConfig();
const session = useUserSession();

async function loginWithGoogle() {
  session.clear();

  const redirectUrl = `${window.location.origin}/confirm`;
  const scope = "email profile";
  const responseType = "code";
  const clientId = config.public.googleClientId;

  const params = new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUrl,
    response_type: responseType,
    scope: scope,
  });
  const url = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;

  window.location.href = url;
}
</script>

<template>
  <button @click="loginWithGoogle" class="button-secondary">
    <img src="/public/images/google-logo.svg" alt="Google Logo" class="w-8" />
    Login with Google
  </button>
</template>

<style lang="css" scoped>
.button-secondary {
  @apply label-3;
  border-radius: 0.5rem;
  border: 1px solid var(--border-default, #d4d4d4);
  background: var(--white, #fff);
  display: flex;
  height: 3rem;
  padding: 0.5rem 1rem;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  align-self: stretch;
}
.button-secondary:hover {
  @apply bg-secondary-background;
  @apply border-default-border;
}
</style>
