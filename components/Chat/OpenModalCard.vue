<script setup lang="ts">
import { computed, watch, markRaw, inject } from "vue";
import MaterialIcon from "~/components/common/MaterialIcon.vue";

const props = defineProps<{
  title: string;
  type: string;
  status: string;
  canvasComponentType: Component;
  canvasProps: Record<string, any>;
  componentId: string;
}>();

const conversationId = inject<string>("conversationId") as string;

const { openWith, close, isOpenForId } = useChatModal(conversationId);

const isOpenForMe = computed(() => isOpenForId(props.componentId));

watch(
  () => props.canvasProps,
  (newProps, oldProps) => {
    if (
      isOpenForMe.value &&
      JSON.stringify(newProps) !== JSON.stringify(oldProps)
    ) {
      openWith(props.canvasComponentType, newProps, props.componentId);
    }
  },
  { deep: true }
);

function handleClick() {
  if (!isOpenForMe.value) {
    openWith(
      markRaw(props.canvasComponentType),
      props.canvasProps,
      props.componentId
    );
  } else {
    close();
  }
}
</script>

<template>
  <div
    class="order-form-card"
    :class="{ 'is-active': isOpenForMe }"
    @click="handleClick"
  >
    <div class="content">
      <p class="title">{{ title }}</p>
      <p class="type-status">{{ type }} • {{ status }}</p>
    </div>
    <div class="expand-icon-container">
      <MaterialIcon
        :icon="isOpenForMe ? 'collapse_content' : 'expand_content'"
        class="expand-icon"
        size="16px"
      />
    </div>
  </div>
</template>

<style scoped>
.order-form-card {
  @apply relative flex flex-row px-6 py-4 border border-default-border rounded-[16px] cursor-pointer bg-white shadow-sm hover:bg-surface-main transition-colors;
}

.order-form-card.is-active {
  @apply border border-focus-border;
}

.content {
  @apply flex-1 flex-col;
}

.title {
  @apply label-3 text-primary-text mb-1;
}

.type-status {
  @apply caption-2 text-secondary-text;
}

.expand-icon-container {
  @apply flex items-center;
}

.expand-icon {
  @apply text-secondary-text;
}
</style>
