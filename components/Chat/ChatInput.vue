<script setup lang="tsx">
import type { z } from "zod";
import { MessagePayloadSchema } from "~/types/chat/api-schemas";

type MessagePayload = z.infer<typeof MessagePayloadSchema>;

type ChatInputProps = {
  isBusy: boolean;
};

type ChatInputEmits = {
  (e: "sendClick"): void;
};

const props = defineProps<ChatInputProps>();
const emit = defineEmits<ChatInputEmits>();

const content = defineModel<MessagePayload>({ required: true });

const editorElement = ref();

onMounted(focusEditor);

function focusEditor() {
  nextTick(() => editorElement.value?.$el?.focus());
}

function handleFileUpload(attachment: ReferencedResource) {
  content.value.referencedResources = [
    ...(content.value.referencedResources || []),
    attachment,
  ];
}

function handleRemoveAttachment(attachment: ReferencedResource) {
  content.value.referencedResources =
    content.value.referencedResources?.filter(
      (resource) => resource.referenceId !== attachment.referenceId
    ) || [];
}

function onKeyDown(event: KeyboardEvent) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleSendClick();
  }
}

function handleSendClick() {
  if (!props.isBusy) emit("sendClick");
}
</script>

<template>
  <div
    class="flex flex-col w-full gap-4 justify-between items-center py-3 px-4 border border-neutral-300 bg-neutral-50 rounded-xl shadow transition-all duration-200"
  >
    <InputAttachments
      v-if="content.referencedResources?.length"
      :attachments="content.referencedResources"
      @remove-attachment="handleRemoveAttachment"
    />

    <Textarea
      ref="editorElement"
      v-model="content.message"
      class="input-textarea"
      :placeholder="isBusy ? 'Processing your request...' : 'Ask Gigi anything...'"
      :disabled="isBusy"
      :rows="1"
      autoResize
      @keydown="onKeyDown"
    />

    <div class="flex justify-between items-center w-full">
      <div class="flex justify-start items-center gap-2 flex-1 min-w-0">
        <AttachmentsMenu :disabled="isBusy" @file-uploaded="handleFileUpload" />
      </div>

      <div class="flex justify-end items-center gap-2">
        <button
          :disabled="isBusy"
          class="button send-button"
          @click="handleSendClick"
        >
          <MaterialIcon icon="arrow_upward" size="14px" />
        </button>
      </div>
    </div>
  </div>
</template>
<style scoped>
.button {
  @apply flex items-center justify-center rounded p-1 transition-colors duration-200;
}

.send-button {
  @apply text-white bg-gradient-to-b from-neutral-700 to-neutral-800;
}

.send-button:disabled {
  @apply !bg-neutral-500 !bg-none text-neutral-200 cursor-not-allowed;
}

.modal-button {
  @apply text-neutral-600 bg-neutral-200 hover:bg-neutral-300;
}

.modal-button:disabled {
  @apply !bg-neutral-100 !text-neutral-400 cursor-not-allowed;
}

.shadow {
  box-shadow: 0px 0px 10px -16px rgba(255, 255, 255, 0.1) inset,
    0px 4px 4px 2px rgba(229, 229, 229, 0.5);
}

.shadow:hover {
  box-shadow: 0px 0px 10px -16px rgba(255, 255, 255, 0.1) inset,
    0px 4px 4px 2px var(--tertiary-background, #e5e5e5);
}

.input-textarea {
  @apply w-full resize-none min-h-6 !overflow-y-auto p-0
    bg-transparent outline-none border-none drop-shadow-none shadow-none
    placeholder:text-neutral-500 body-1 placeholder:body-1;
  max-height: calc(1.25rem * 5) !important; /* 5 lines max height */
}
</style>
