<script setup lang="ts">
import Card from "primevue/card";
import { getFileTypeByExtension } from "~/components/Chat/file-type.config";
import type { ReferencedResource } from "~/types/attachment";
const props = defineProps<{
  attachment: ReferencedResource;
  downloadable?: boolean;
  removable?: boolean;
}>();

const emit = defineEmits<{
  (e: "click", attachment: ReferencedResource): void;
  (e: "remove", attachment: ReferencedResource): void;
  (e: "download", attachment: ReferencedResource): void;
}>();

const typeConfig = computed(() => {
  return getFileTypeByExtension(props.attachment.displayName);
});
</script>

<template>
  <Card
    pt:root:class="flex !flex-row items-center justify-start group/attachment !gap-2 !bg-neutral-50 !p-2 w-fit border border-neutral-300 max-w-64"
    pt:header:class="flex items-center justify-center !p-0 shrink-0"
    pt:body:class="flex flex-col items-start justify-start !p-0 !mr-4 min-w-0 flex-1 overflow-hidden"
    pt:caption:class="flex flex-col !gap-0 !p-0 w-full"
    pt:content:class="hidden"
    pt:title:class="inline-flex items-center justify-between !text-sm !font-semibold gap-2 w-full"
    pt:subtitle:class="!text-xs select-none"
    style="--p-card-shadow: none; --p-card-border-radius: 0.5rem"
    @click="emit('click', attachment)"
  >
    <template #header>
      <i
        :class="[
          'material-symbols-outlined rounded select-none p-2',
          typeConfig.color,
        ]"
      >
        {{ typeConfig.icon }}
      </i>
    </template>

    <template #title>
      <div class="min-w-0 flex-1 overflow-hidden">
        <span class="block truncate select-none">
          {{ attachment.displayName }}
        </span>
      </div>

      <div class="flex items-center justify-end shrink-0 gap-2">
        <button
          v-if="removable"
          class="material-symbols-outlined w-4 h-4 bg-neutral-400 group-hover/attachment:bg-red-400 text-white rounded-full text-center place-content-center select-none"
          style="font-size: 0.75rem; font-weight: 800"
          @click.stop="emit('remove', attachment)"
        >
          close
        </button>

        <button
          v-if="downloadable"
          class="material-symbols-outlined w-4 h-4 bg-neutral-400 group-hover/attachment:bg-blue-400 text-white rounded-full text-center place-content-center select-none"
          style="font-size: 0.75rem; font-weight: 800"
          @click.stop="emit('download', attachment)"
        >
          download
        </button>
      </div>
    </template>

    <template #subtitle> {{ typeConfig.label }} </template>
  </Card>
</template>
