<script setup lang="ts">
import type { ReferencedResource } from "~/types/attachment";

defineProps<{
  attachments: ReferencedResource[];
}>();

const emit = defineEmits<{
  (e: "downloadAttachment", attachment: ReferencedResource): void;
}>();

const handleClick = async (attachment: ReferencedResource) => {
  const { getReferenceableAsDocument } = useRob();
  const fileDownload = useFileDownload();

  const { blob, name } = await getReferenceableAsDocument(attachment.key);
  console.log(blob);

  fileDownload.fromBlob(blob, name ?? attachment.displayName);
};
</script>

<template>
  <WrappedCollection :items="attachments">
    <template #default="{ item }">
      <Attachment
        :attachment="item"
        downloadable
        @download="handleClick(item)"
      />
    </template>
  </WrappedCollection>
</template>
