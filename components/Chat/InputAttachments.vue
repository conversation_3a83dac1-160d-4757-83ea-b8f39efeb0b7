<script setup lang="ts">
import type { ReferencedResource } from "~/types/attachment";

defineProps<{
  attachments: ReferencedResource[];
}>();

const emit = defineEmits<{
  (e: "removeAttachment", attachment: ReferencedResource): void;
}>();
</script>

<template>
  <ScrollableCollection :items="attachments">
    <template #default="{ item }">
      <Attachment
        :attachment="item"
        removable
        @remove="emit('removeAttachment', item)"
      />
    </template>
  </ScrollableCollection>
</template>
<style scoped>
.attachment-icon {
  @apply opacity-0 group-hover:opacity-100 transition-opacity duration-200;
}
</style>
