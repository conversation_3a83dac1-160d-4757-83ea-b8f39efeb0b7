<script setup lang="ts">
import type { ReferencedResource } from "~/types/attachment";

defineProps<{
  disabled: boolean;
}>();

const emit = defineEmits<{
  (e: "fileUploaded", attachment: ReferencedResource): void;
}>();

const attachmentPopover = ref();
</script>

<template>
  <Popover ref="attachmentPopover">
    <div class="flex flex-col gap-2">
      <GFileUpload
        :disabled="disabled"
        @file-uploaded="emit('fileUploaded', $event)"
        @upload-done="attachmentPopover.hide()"
      />
    </div>
  </Popover>

  <button
    class="button attach-button"
    :disabled="disabled"
    @click="attachmentPopover.toggle($event)"
  >
    <MaterialIcon icon="add" size="14px" />
  </button>
</template>

<style scoped>
.button {
  @apply flex items-center justify-center rounded p-1 transition-colors duration-200;
}

.attach-button {
  @apply text-primary-text bg-white border border-default-border;
}

.attach-button:disabled {
  @apply !bg-neutral-200 text-neutral-400 cursor-not-allowed;
}
</style>
