<script setup lang="ts">
import type { z } from "zod";
import { MessagePayloadSchema } from "~/types/chat/api-schemas";

type MessagePayload = z.infer<typeof MessagePayloadSchema>;

const props = defineProps<{
  conversationId: string;
}>();

const { messages, conversation, isBusy, error, sendMessage } = useExistingChat(
  props.conversationId
);

const conversationsStore = useConversationStore();

const messagePayload = ref<MessagePayload>({
  message: "",
  referencedResources: [],
});

onMounted(async () => {
  const pendingMessage = conversation.value?.pendingMessage;
  if (pendingMessage) {
    console.log("Chat: Found pending message, sending automatically", {
      conversationId: props.conversationId,
      message: pendingMessage.message.slice(0, 50) + "...",
    });

    conversationsStore.updateConversation(props.conversationId, {
      pendingMessage: undefined,
    });

    await sendMessage(pendingMessage);
  }
});

async function handleSendMessage() {
  await sendMessage(messagePayload.value);
  messagePayload.value = {
    message: "",
    referencedResources: [],
  };
}

provide("conversationId", props.conversationId);
</script>

<template>
  <div class="root">
    <!-- No conversation state (loading/error) -->
    <div v-if="!conversation" class="no-conversation">
      <!-- Loading state -->
      <div v-if="isBusy" class="loading-state">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading conversation...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="error-state">
        <MaterialIcon icon="error" class="error-icon" size="32px" />
        <p class="error-text">Failed to load conversation</p>
        <p class="error-subtitle">
          Please try again later or contact support if the problem persists.
        </p>
      </div>

      <!-- Initial state (when conversationId is provided but no conversation yet) -->
      <div v-else-if="conversationId && !conversation" class="initial-state">
        <MaterialIcon icon="chat" class="initial-icon" size="32px" />
        <p class="initial-text">Preparing conversation...</p>
      </div>
    </div>

    <!-- Conversation exists - show messages -->
    <MessagesArea
      v-else
      class="messages-area"
      :messages="messages"
      :created-at="conversation?.createdAt"
      :is-busy="isBusy"
      :error="error?.message"
      :conversation="conversation"
    />

    <div class="chat-input-container">
      <ChatInput
        v-model="messagePayload"
        :is-busy="isBusy"
        @send-click="handleSendMessage"
      />
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col flex-1 relative h-full justify-end overflow-hidden;
}

.messages-area {
  @apply flex-1;
}

.chat-input-container {
  @apply w-full max-w-3xl mx-auto mb-6 px-4;
}

/* No conversation states */
.no-conversation {
  @apply flex-1 flex items-center justify-center;
}

.loading-state,
.error-state,
.initial-state {
  @apply flex flex-col items-center justify-center text-center p-8 max-w-md;
}

/* Loading state */
.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-200 border-t-neutral-500 rounded-full animate-spin mb-4;
}

.loading-text {
  @apply body-2 text-primary-text;
}

/* Error state */
.error-icon {
  @apply text-red-500;
}

.error-text {
  @apply body-2 text-red-500;
}

.error-subtitle {
  @apply body-1 text-secondary-text;
}

/* Initial state */
.initial-icon {
  @apply mb-4;
}

.initial-text {
  @apply body-2 text-primary-text;
}
</style>
