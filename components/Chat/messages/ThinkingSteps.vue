<script setup lang="tsx">
import type { StatefulAiMessage } from "@/types/chat/models";
import Markdown from "~/components/common/Markdown.vue";

type ThoughtSteps = StatefulAiMessage["thoughtSteps"];
type MessageState = StatefulAiMessage["state"];

export interface ThinkingStepsProps {
  steps: ThoughtSteps;
  messageState: MessageState;
  messageHasContent: boolean;
}

const TypewriterText = defineComponent({
  name: "InlineTypewriterText",
  props: { content: String, showFull: Boolean },
  setup(props) {
    const contentRef = toRef(props, "content") as Ref<string>;
    const display = props.showFull
      ? contentRef
      : useTypewriterText(contentRef, { splitType: "word", addSpeed: 20 });
    return () => <Markdown class="step-content" content={display.value} />;
  },
});

const props = defineProps<ThinkingStepsProps>();

const isExpanded = ref(false);
const contentRef = ref<HTMLElement | null>(null);

watch(
  () => props.messageHasContent,
  (newValue, oldValue) => {
    if (newValue && !oldValue) {
      isExpanded.value = false;
    }
  },
  { immediate: true }
);

function beforeEnter(el: Element) {
  (el as HTMLElement).style.height = "0";
}

function enter(el: Element) {
  requestAnimationFrame(() => {
    if ((el as HTMLElement).scrollHeight !== 0) {
      (el as HTMLElement).style.height =
        (el as HTMLElement).scrollHeight + "px";
    } else {
      (el as HTMLElement).style.height = "";
    }
  });
}

function afterEnter(el: Element) {
  (el as HTMLElement).style.height = "";
}

function leave(el: Element) {
  (el as HTMLElement).style.height = (el as HTMLElement).scrollHeight + "px";
  requestAnimationFrame(() => {
    (el as HTMLElement).style.height = "0";
  });
}

const displayedSteps = computed(() => {
  if (props.messageHasContent && !isExpanded.value) {
    return [];
  }

  if (isExpanded.value) {
    return props.steps;
  }

  if (!props.messageHasContent && props.messageState === "pending") {
    const lastStep = props.steps.at(-1);
    return lastStep ? [lastStep] : [];
  }

  return [];
});

const shouldShowContent = computed(() => {
  return displayedSteps.value.length > 0;
});
</script>

<template>
  <div
    v-if="steps.length > 0 || messageState === 'pending'"
    class="flex flex-col gap-3"
  >
    <button
      class="flex flex-row items-center gap-2"
      @click="() => (isExpanded = !isExpanded)"
    >
      <div
        v-if="messageState === 'pending' && !messageHasContent"
        class="flex flex-row items-center gap-2"
      >
        <ThinkingAnimation />
        <span class="body-1 text-secondary-text">Thinking</span>
      </div>

      <div v-else>
        <span class="body-1 text-secondary-text">Thoughts</span>
      </div>

      <MaterialIcon
        v-if="messageState !== 'pending' || steps.length > 1"
        icon="chevron_right"
        class="transition-transform duration-300 text-secondary-text"
        :class="{ 'rotate-90': isExpanded }"
        size="1rem"
      />
    </button>

    <transition
      name="expand"
      @before-enter="beforeEnter"
      @enter="enter"
      @after-enter="afterEnter"
      @leave="leave"
    >
      <div v-if="shouldShowContent" ref="contentRef" class="overflow-hidden">
        <transition-group name="step" tag="ul" class="steps-list">
          <li
            v-for="(step, index) in displayedSteps"
            :key="index"
            class="step-container"
          >
            <div class="flex mb-auto mt-1">
              <span
                v-if="
                  messageState !== 'pending' ||
                  index < displayedSteps.length - 1
                "
                class="step-bullet"
              >
                •
              </span>
              <span v-else class="spinner"></span>
            </div>

            <div class="flex flex-col gap-1">
              <span v-if="step.title" class="body-1 text-secondary-text">
                {{ step.title }}
              </span>

              <TypewriterText
                :content="step.content || ''"
                :show-full="
                  messageState !== 'pending' ||
                  index < displayedSteps.length - 1
                "
                :key="`${index}-${isExpanded}`"
              />
            </div>
          </li>
        </transition-group>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.step-enter-active,
.step-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.step-enter-from,
.step-leave-to {
  opacity: 0;
}

.steps-list {
  @apply flex flex-col gap-3;
}

.step-container {
  @apply flex flex-row gap-3 items-center;
}

.step-content {
  @apply text-disabled-text body-1;
}

.step-content p {
  @apply mb-0;
}

.step-bullet {
  @apply text-black body-1 w-3 h-3 flex items-center justify-center shrink-0;
}

.spinner {
  @apply w-3 h-3 border-2 border-default-border border-t-neutral-700 rounded-full animate-spin shrink-0;
}

.expand-enter-active,
.expand-leave-active {
  transition: height 0.2s ease;
}
</style>
