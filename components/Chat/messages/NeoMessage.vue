<script setup lang="ts">
import type { StatefulUserMessage } from "~/types/chat/models";

defineProps<{
  message: StatefulUserMessage;
}>();
</script>

<template>
  <div class="root">
    <div class="content-container">
      <span class="content">{{ message.content }}</span>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex justify-end relative;
}

.content-container {
  @apply w-fit max-w-xl p-3 gap-3 bg-secondary-background rounded-xl self-end;
}

.content {
  @apply body-1 text-primary-text;
}

.attachments-container {
  @apply w-full overflow-hidden;
}

.attachments-list {
  @apply flex flex-row gap-2 overflow-x-auto;
}
</style>
