<script setup lang="tsx">
import type { z } from "zod";
import { computed, inject, onMounted, ref } from "vue";
import { MessagePartSchema } from "~/types/chat/api-schemas";
import type { LocalConversation } from "~/types/chat/models";
import MarkdownCanvas from "~/components/Chat/canvas/MarkdownCanvas.vue";
import { useConversationStore } from "~/stores/conversation-store";
import { useRob } from "~/composables/clients/useRob";

type DocumentPart = Extract<
  z.infer<typeof MessagePartSchema>,
  { kind: "document" }
>;

type DocumentPartProps = {
  conversation: LocalConversation;
  part: DocumentPart;
  id: string;
};

const props = defineProps<DocumentPartProps>();

const conversationId = inject<string>("conversationId") as string;
const chatModal = useChatModal(conversationId);
const { sendMessage } = useExistingChat(conversationId);

const store = useConversationStore();
const { getChatDocument } = useChatService();
const isLoading = ref(false);

onMounted(async () => {
  if (!getDocument()) {
    isLoading.value = true;
    try {
      const fetchedDoc = await getChatDocument(
        conversationId,
        props.part.instance,
        props.part.version
      );
      const currentDocs = props.conversation.documents || [];
      store.updateConversation(conversationId, {
        documents: [...currentDocs, fetchedDoc],
      });
    } catch (error) {
      console.error("Failed to fetch document:", error);
    } finally {
      isLoading.value = false;
    }
  }
});

const canvasProps = computed(() => ({
  title: getDocumentTitle(),
  headerIcon: "article",
  sections: getDocumentSections(),
  primaryButtonText: "Accept and build",
  showPrimaryButton: true,
  onPrimaryClick: onFormSubmit,
  onClose: () => chatModal.close(),
}));

async function onFormSubmit() {
  await sendMessage({
    message: "Accept and build",
    referencedResources: [],
  });
}

function getDocumentSections() {
  if (isLoading.value) return {};
  const document = getDocument();

  const pages = document?.nodes?.filter((node) => node.kind === "page");

  const sections = Object.fromEntries(
    pages?.map((page) => {
      return [page.title, page.children?.[0]?.content];
    }) || []
  );

  return sections;
}

function getDocumentTitle() {
  if (isLoading.value) return "Loading Document...";
  const document = getDocument();

  return document?.title || "Unknown Document";
}

function getDocument() {
  return props.conversation.documents?.find(
    (doc) =>
      doc.instance === props.part.instance && doc.version === props.part.version
  );
}
</script>

<template>
  <div v-if="isLoading">Loading document...</div>
  <OpenModalCard
    v-else
    :title="getDocumentTitle()"
    type="Document"
    status="Waiting for user action"
    :canvas-component-type="MarkdownCanvas"
    :canvas-props="canvasProps"
    :component-id="`${props.id}--${props.part.instance}-${props.part.version}`"
  />
</template>

<style scoped></style>
