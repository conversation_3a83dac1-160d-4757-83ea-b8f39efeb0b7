<script setup lang="ts">
import type { LocalConversation, StatefulAiMessage } from "~/types/chat/models";
import Attachment from "~/components/Chat/Attachment.vue";
import { computed } from "vue";

const props = defineProps<{
  conversation: LocalConversation;
  message: StatefulAiMessage;
}>();

const messageHasContent = computed(() => {
  return props.message.parts.length > 0;
});

const isMessagePending = computed(() => {
  return props.message.state === "pending";
});
</script>

<template>
  <div class="root">
    <div class="content-container">
      <ThinkingSteps
        :steps="message.thoughtSteps"
        :message-state="message.state"
        :message-has-content="messageHasContent"
      />

      <transition-group name="fade" tag="div" class="flex flex-col gap-4">
        <div v-for="(part, index) in message.parts" :key="index">
          <TextPart
            v-if="part.kind === 'text'"
            :part="part"
            :is-pending="isMessagePending"
          />

          <FormPart
            v-else-if="part.kind === 'form'"
            :part="part"
            :id="`${props.message.id}--${index}`"
          />

          <DocumentPart
            v-else-if="part.kind === 'document'"
            :conversation="conversation"
            :part="part"
            :id="`${props.message.id}--${index}`"
          />
        </div>
      </transition-group>
    </div>

    <div
      v-if="message.parts.some((part) => part.kind === 'file')"
      class="attachments-container"
    >
      <ul class="attachments-list">
        <li
          v-for="(resource, index) in message.parts.filter(
            (part) => part.kind === 'file'
          )"
          :key="index"
        >
          <Attachment
            :attachment="{
              key: resource.fileName,
              displayName: resource.fileName,
              referenceId: resource.referenceableId,
            }"
            downloadable
          />
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col flex-1 relative
    p-3 gap-3;
}

.content-container {
  @apply w-full flex flex-col gap-4;
}

.content {
  @apply body-1 text-slate-800;
}

.attachments-container {
  @apply w-full overflow-hidden;
}

.attachments-list {
  @apply flex flex-row gap-2 overflow-x-auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
