<script setup lang="tsx">
import { z } from "zod";
import { markRaw, computed, inject } from "vue";

import type { MessagePartSchema } from "~/types/chat/api-schemas";
import FormCanvas from "~/components/Chat/canvas/FormCanvas.vue";
import { FormRegistry } from "~/utils/forms";
import { ChatParticipant } from "~/types/chat/common";
import type { StatefulAiMessage } from "~/types/chat/models";

type FormPart = Extract<z.infer<typeof MessagePartSchema>, { kind: "form" }>;

const props = defineProps<{
  part: FormPart;
  id: string;
}>();

const formEntry = FormRegistry.get(props.part.formConfig.formType);

if (!formEntry) {
  throw new Error(
    `Form entry not found for formId: ${props.part.formConfig.formId}`
  );
}

const canvasProps = computed(() => ({
  title: formEntry!.title,
  headerIcon: formEntry!.icon,
  primaryButtonText: formEntry!.submitMessage,
  initialData: formEntry!.toLocal(props.part.formConfig),
  loading: isBusy.value,
  disabled: isBusy.value || props.part.formConfig.state === "submitted",
  component: markRaw(formEntry!.component),
  onDataChange: updateFormData,
  onPrimaryClick: onFormSubmit,
  onSecondaryClick: onClose,
  onClose: onClose,
}));

const conversationId = inject<string>("conversationId") as string;
const { sendMessage, isBusy, messages } = useExistingChat(conversationId);
const chatModal = useChatModal(conversationId);
const { updateMessageParts } = useChatService();

function updateFormData(data: any) {
  const formData = formEntry!.fromLocal(props.part.formConfig, data);
  props.part.formConfig.formData = formData;
  updateMessageAndPersist(formData);
}

function updateMessageAndPersist(formData: any) {
  const currentMessage: StatefulAiMessage = messages.value.find(
    (message) =>
      message.id === props.id.split("--")[0] &&
      message.type === ChatParticipant.AI
  ) as StatefulAiMessage;

  updateMessageParts(
    conversationId,
    currentMessage.id,
    currentMessage,
    (parts) => {
      const updatedFormPart = parts.find(
        (part) => part.kind === "form"
      ) as FormPart;
      updatedFormPart.formConfig.formData = formData;
      return parts;
    }
  ).catch((error: any) => {
    console.error("Failed to persist form data to Rob:", error);
  });
}

async function onFormSubmit() {
  await sendMessage({
    message: formEntry!.submitMessage,
    referencedResources: [],
    additionalPayload: {
      formConfig: {
        fields: props.part.formConfig.formData,
      },
    },
  });
  props.part.formConfig.state = "submitted";
}

function onClose() {
  chatModal.close();
}
</script>

<template>
  <OpenModalCard
    type="Order form"
    :status="
      part.formConfig.state === 'pending'
        ? 'Waiting for user action'
        : 'Submitted'
    "
    title="Order Creation Form"
    :canvas-component-type="FormCanvas"
    :canvas-props="canvasProps"
    :component-id="`${props.id}--${props.part.formConfig.formId}`"
  />
</template>
