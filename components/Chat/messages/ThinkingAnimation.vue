<template>
  <div class="flex items-center gap-2 text-neutral-500">
    <div class="la-ball-grid-beat la-sm">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  </div>
</template>

<style scoped>
/* Load Awesome styles using CSS Grid */
.la-ball-grid-beat {
  display: grid;
  grid-template-columns: repeat(3, 0.25em);
  gap: 0.25em;
  justify-content: start;
  align-items: center;
  font-size: 0;
  color: inherit;
}

.la-ball-grid-beat.la-sm {
  font-size: 12px;
}

.la-ball-grid-beat > div {
  width: 0.25em;
  height: 0.25em;
  border-radius: 100%;
  background-color: currentColor;
  animation: ball-grid-beat 1s infinite linear;
}

.la-ball-grid-beat > div:nth-child(1) {
  animation-delay: 0.03s;
}
.la-ball-grid-beat > div:nth-child(2) {
  animation-delay: 0.17s;
}
.la-ball-grid-beat > div:nth-child(3) {
  animation-delay: 0.08s;
}
.la-ball-grid-beat > div:nth-child(4) {
  animation-delay: 0.21s;
}
.la-ball-grid-beat > div:nth-child(5) {
  animation-delay: 0.13s;
}
.la-ball-grid-beat > div:nth-child(6) {
  animation-delay: 0.29s;
}
.la-ball-grid-beat > div:nth-child(7) {
  animation-delay: 0.18s;
}
.la-ball-grid-beat > div:nth-child(8) {
  animation-delay: 0.05s;
}
.la-ball-grid-beat > div:nth-child(9) {
  animation-delay: 0.24s;
}

@keyframes ball-grid-beat {
  50% {
    opacity: 0.2;
    transform: scale(0.75);
  }
}

.font-inter {
  font-family: "Inter", sans-serif;
}
</style>
