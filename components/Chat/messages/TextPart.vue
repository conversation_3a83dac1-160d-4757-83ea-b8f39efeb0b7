<script setup lang="ts">
import { z } from "zod";
import { MessagePartSchema } from "@/types/chat/api-schemas";

type MessagePart = z.infer<typeof MessagePartSchema>;
type TextPart = Extract<MessagePart, { kind: "text" }>;

export interface TextPartProps {
  part: TextPart;
  speed?: number;
  isPending?: boolean;
}

const props = defineProps<TextPartProps>();

const sourceText = computed(() => props.part.content);

const animatedText = useTypewriterText(sourceText, {
  addSpeed: props.speed,
  splitType: "word",
});

const displayText = computed(() => {
  return props.isPending ? animatedText.value : sourceText.value;
});
</script>

<template>
  <Markdown :content="displayText" />
</template>
