<script setup lang="ts">
import { format } from "date-fns";
import type { LocalConversation, StatefulMessage } from "~/types/chat/models";
import { ChatParticipant } from "~/types/chat/common";

export type MessagesAreaProps = {
  conversation: LocalConversation;
  messages: StatefulMessage[];
  createdAt: Date;
  isBusy?: boolean;
  error?: string;
};

const props = defineProps<MessagesAreaProps>();

const viewportRef = ref<HTMLElement>();

const scrollToBottom = () => {
  nextTick(() => {
    if (viewportRef.value) {
      viewportRef.value.scrollTop = viewportRef.value.scrollHeight;
    }
  });
};

watch(
  () => [props.messages.length, props.error, props.isBusy],
  () => {
    scrollToBottom();
  },
  { immediate: true }
);

onMounted(() => {
  scrollToBottom();
});
</script>

<template>
  <div class="root">
    <div class="edge-shadow-container">
      <div class="edge-shadow" />
    </div>
    <div ref="viewportRef" class="viewport">
      <div class="messages-container">
        <div class="datetime-container">
          <span class="datetime-text">
            {{ format(createdAt, "MMM d, yyyy HH:mm a") }}
          </span>
        </div>

        <ul class="messages-list">
          <li v-for="message in messages" :key="message.id">
            <NeoMessage
              v-if="message.type === ChatParticipant.USER"
              :message="message as any"
            />
            <TrinityMessage
              v-else
              :message="message as any"
              :conversation="conversation"
            />
          </li>
        </ul>

        <div v-if="error" class="error-state">
          <div class="error-card">
            <MaterialIcon icon="error" class="error-icon" size="24px" />
            <div class="error-content">
              <p class="error-title">Something went wrong</p>
              <p class="error-message">{{ error }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col flex-1 relative min-h-0;
}

.viewport {
  @apply w-full h-full flex-1 overflow-y-auto mx-auto;
}

.edge-shadow-container {
  @apply absolute z-10 inset-0 mx-auto max-w-3xl px-4;
  pointer-events: none;
}

.edge-shadow {
  @apply w-full h-full z-10;
  box-shadow: inset 0 -10px 10px 5px rgba(255, 255, 255, 0.85);
  pointer-events: none;
}

.messages-container {
  @apply w-full flex flex-col max-w-3xl mx-auto pt-6 pb-4 px-4 relative;
}

.datetime-container {
  @apply w-full flex justify-center items-center mb-4;
}

.datetime-text {
  @apply label-3 text-disabled-text;
}

.messages-list {
  @apply flex flex-col gap-4;
}

.error-state {
  @apply mt-4 pb-4 flex justify-center;
}

.error-card {
  @apply flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg max-w-md;
}

.error-icon {
  @apply text-red-500 flex-shrink-0 mt-0.5;
}

.error-content {
  @apply flex flex-col gap-1;
}

.error-title {
  @apply body-2 font-medium text-red-700;
}

.error-message {
  @apply body-1 text-red-600;
}

.busy-state {
  @apply mt-4 pb-4;
}
</style>
