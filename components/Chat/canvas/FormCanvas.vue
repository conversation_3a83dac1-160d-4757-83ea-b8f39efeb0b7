<script setup lang="ts">
import type { Component } from "vue";
import type { BaseCanvasEmits, BaseCanvasProps } from "./BaseCanvas.vue";
import type {
  FormComponentEmits,
  FormComponentProps,
} from "~/types/form-component";

export type FormCanvasProps = BaseCanvasProps &
  FormComponentProps & {
    component: Component;
  };
export type FormCanvasEmits = BaseCanvasEmits & FormComponentEmits;

defineProps<FormCanvasProps>();
defineEmits<FormCanvasEmits>();
</script>

<template>
  <BaseCanvas
    :title="title"
    :header-icon="headerIcon"
    :primary-button-text="primaryButtonText"
    secondary-button-text="Cancel"
    :loading="loading"
    :disabled="disabled"
    :show-primary-button="!disabled"
    :show-secondary-button="!disabled"
    @primary-click="$emit('primaryClick')"
    @secondary-click="$emit('secondaryClick')"
    @close="$emit('close')"
  >
    <component
      :is="component"
      :initial-data="initialData"
      :disabled="disabled"
      @data-change="$emit('dataChange', $event)"
    />
  </BaseCanvas>
</template>
