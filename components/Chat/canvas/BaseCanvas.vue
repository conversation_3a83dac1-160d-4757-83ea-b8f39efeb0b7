<script setup lang="ts">
export interface BaseCanvasProps {
  title: string;
  headerIcon?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  showPrimaryButton?: boolean;
  showSecondaryButton?: boolean;
  loading?: boolean;
  disabled?: boolean;
}

export interface BaseCanvasEmits {
  (e: "primaryClick"): void;
  (e: "secondaryClick"): void;
  (e: "close"): void;
}

const props = withDefaults(defineProps<BaseCanvasProps>(), {
  headerIcon: "dashboard",
  primaryButtonText: "Submit",
  secondaryButtonText: "Cancel",
  showPrimaryButton: false,
  showSecondaryButton: false,
  loading: false,
  disabled: false,
});

const emit = defineEmits<BaseCanvasEmits>();

const handlePrimaryClick = () => {
  if (!props.disabled && !props.loading) {
    emit("primaryClick");
  }
};

const handleSecondaryClick = () => {
  if (!props.disabled && !props.loading) {
    emit("secondaryClick");
  }
};

const handleClose = () => {
  emit("close");
};
</script>

<template>
  <div class="base-canvas">
    <!-- Header -->
    <div class="canvas-header">
      <div class="header-content">
        <div class="header-left">
          <MaterialIcon
            v-if="headerIcon"
            :icon="headerIcon"
            class="header-icon"
            size="16px"
          />
          <div class="header-text">
            <h2 class="canvas-title">{{ title }}</h2>
          </div>
        </div>

        <div class="header-right">
          <slot name="header-actions" />

          <div class="header-buttons">
            <button
              v-if="showSecondaryButton"
              type="button"
              class="button-base button-secondary"
              :disabled="disabled || loading"
              @click="handleSecondaryClick"
            >
              <slot name="secondary-button-content">
                {{ secondaryButtonText }}
              </slot>
            </button>

            <button
              v-if="showPrimaryButton"
              type="button"
              class="button-base button-primary"
              :disabled="disabled || loading"
              @click="handlePrimaryClick"
            >
              <div v-if="loading" class="loading-spinner"></div>
              <slot name="primary-button-content">
                {{ primaryButtonText }}
              </slot>
            </button>
          </div>

          <!-- Close button -->
          <button type="button" class="close-button" @click="handleClose">
            <MaterialIcon icon="close" size="20px" />
          </button>
        </div>
      </div>
    </div>

    <!-- Content Area -->
    <div class="canvas-content">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.base-canvas {
  @apply flex flex-col min-h-0 h-full bg-white;
}

.canvas-header {
  @apply px-6 py-4 h-16;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-left {
  @apply flex items-center gap-x-2;
}

.header-right {
  @apply flex items-center gap-x-4;
}

.header-buttons {
  @apply flex items-center gap-x-3;
}

.header-icon {
  @apply text-black flex-shrink-0;
}

.header-text {
  @apply flex flex-col gap-1;
}

.canvas-title {
  @apply label-3 text-primary-text line-clamp-1;
}

.canvas-content {
  @apply flex-1 overflow-auto min-h-0;
}

.loading-spinner {
  @apply w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin;
}

.button-base {
  @apply px-3 py-1.5 rounded-md label-3 transition-all duration-200 flex items-center gap-2;
}

.button-primary {
  @apply bg-neutral-700 text-white hover:bg-neutral-800 shadow-sm;
}

.button-secondary {
  @apply bg-white text-neutral-700 hover:bg-neutral-300 border border-neutral-300;
}

.close-button {
  @apply p-2 rounded-full text-neutral-800 bg-secondary-background hover:bg-neutral-100 hover:shadow-sm transition-all duration-200 w-8 h-8 flex items-center justify-center;
}

.close-button:hover {
  transform: scale(1.05);
}
</style>
