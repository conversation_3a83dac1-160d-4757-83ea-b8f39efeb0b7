<script setup lang="ts">
import type {
  BaseCanvasProps,
  BaseCanvasEmits,
} from "~/components/Chat/canvas/BaseCanvas.vue";
import Tabs from "primevue/tabs";
import TabList from "primevue/tablist";
import Tab from "primevue/tab";
import TabPanels from "primevue/tabpanels";
import TabPanel from "primevue/tabpanel";

type MarkdownCanvasProps = BaseCanvasProps & {
  sections: Record<string, string>;
};

type MarkdownCanvasEmits = BaseCanvasEmits;

const props = defineProps<MarkdownCanvasProps>();
defineEmits<MarkdownCanvasEmits>();

const activeTab = ref("");
watch(
  () => props.sections,
  (newSections) => {
    const sectionKeys = Object.keys(newSections);
    if (sectionKeys.length > 0 && !activeTab.value) {
      activeTab.value = sectionKeys[0];
    }
  },
  { immediate: true }
);

const sectionKeys = computed(() => Object.keys(props.sections));

const onTabChange = (event: any) => {
  activeTab.value = event.value;
};
</script>

<template>
  <BaseCanvas
    :title="title"
    :header-icon="headerIcon"
    :primary-button-text="primaryButtonText"
    :secondary-button-text="secondaryButtonText"
    :show-primary-button="showPrimaryButton"
    :show-secondary-button="showSecondaryButton"
    @primary-click="$emit('primaryClick')"
    @secondary-click="$emit('secondaryClick')"
    @close="$emit('close')"
  >
    <!-- Main Content -->
    <div class="markdown-content">
      <!-- Tabs Navigation -->
      <Tabs
        v-if="sectionKeys.length > 0"
        :value="activeTab"
        @tab-change="onTabChange"
        class="markdown-tabview"
      >
        <TabList>
          <Tab
            v-for="sectionKey in sectionKeys"
            :key="sectionKey"
            :value="sectionKey"
          >
            {{ sectionKey }}
          </Tab>
        </TabList>

        <TabPanels
          :pt="{
            root: 'p-0',
          }"
        >
          <TabPanel
            v-for="sectionKey in sectionKeys"
            :key="sectionKey"
            :value="sectionKey"
          >
            <div class="document-content">
              <Markdown :content="props.sections[sectionKey]" />
            </div>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>
  </BaseCanvas>
</template>

<style scoped>
.header-actions {
  @apply flex items-center gap-3;
}

.markdown-tabview {
  @apply mb-6;
}

.document-content {
  @apply px-10 pt-[2.75rem];
}
</style>
