<script setup lang="ts">
import PageHeader from "~/components/layout/PageHeader.vue";
import MaterialIcon from "~/components/common/MaterialIcon.vue";

const conversationId = inject<string>("conversationId") as string;

const { conversation } = useExistingChat(conversationId);

const { isOpen, activeCanvasComponentType, activeCanvasComponentProps, close } =
  useChatModal(conversationId);

function handleEscKey(event: KeyboardEvent) {
  if (event.key === "Escape" && activeCanvasComponentType.value) {
    close();
  }
}

onMounted(() => {
  document.addEventListener("keydown", handleEscKey);
});

onBeforeUnmount(() => {
  document.removeEventListener("keydown", handleEscKey);
});
</script>

<template>
  <ClientOnly>
    <Teleport to="#main-content">
      <Transition name="modal" appear>
        <div v-if="isOpen" class="modal-overlay">
          <Splitter class="flex-1 h-full min-h-0 override-splitter">
            <SplitterPanel class="chat-section" :size="40" :min-size="25">
              <PageHeader :title="conversation?.title || 'New Chat'" />
              <ChatArea :conversation-id="conversationId" />
            </SplitterPanel>

            <SplitterPanel class="canvas-section" :size="60" :min-size="50">
              <component
                v-if="activeCanvasComponentType"
                :is="activeCanvasComponentType"
                v-bind="activeCanvasComponentProps"
              />

              <div v-else class="empty-canvas">
                <div class="empty-canvas-content">
                  <MaterialIcon
                    icon="dashboard"
                    class="empty-icon"
                    size="48px"
                  />
                  <p class="empty-text">No canvas component loaded</p>
                </div>
              </div>
            </SplitterPanel>
          </Splitter>
        </div>
      </Transition>
    </Teleport>
  </ClientOnly>
</template>

<style scoped>
.modal-overlay {
  @apply absolute inset-0 z-50 bg-black bg-opacity-50;
}

.modal-container {
  @apply bg-white w-full h-full flex overflow-hidden relative;
}

/* Chat Section (1/3) */
.chat-section {
  @apply w-1/3 flex flex-col border-neutral-200 bg-white;
}

/* Canvas Section (2/3) */
.canvas-section {
  @apply w-2/3 flex flex-col min-h-0;
  box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.05);
}

.canvas-content {
  @apply flex-1 min-h-0 overflow-hidden;
}

.empty-canvas {
  @apply flex-1 flex items-center justify-center;
}

.empty-canvas-content {
  @apply flex flex-col items-center justify-center text-center p-8;
}

.empty-icon {
  @apply text-neutral-400 mb-4;
}

.empty-text {
  @apply body-2 text-neutral-500;
}

/* Modal Animation */
.modal-enter-active {
  transition: all 0.2s ease-out;
}

.modal-leave-active {
  transition: all 0.2s ease-in;
}

.modal-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-to {
  opacity: 1;
  transform: scale(1);
}

.override-splitter ::v-deep(.p-splitter-gutter) {
  @apply bg-white;
  position: relative;
  cursor: col-resize;
}

.override-splitter ::v-deep(.p-splitter-gutter)::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.override-splitter ::v-deep(.p-splitter-gutter)::after {
  content: "⋮";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 28px;
  color: #6b7280;
  line-height: 1;
  letter-spacing: 4px;
}
</style>
