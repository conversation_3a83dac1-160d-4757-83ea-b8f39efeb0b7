// Create a fast lookup map for extensions
const extensionMap: Record<string, keyof typeof fileTypeConfig> = {
  // Documents
  pdf: "pdf",
  doc: "document",
  docx: "document",
  odt: "document",
  rtf: "document",
  txt: "document",
  // Spreadsheets
  xls: "spreadsheet",
  xlsx: "spreadsheet",
  csv: "spreadsheet",
  ods: "spreadsheet",
  numbers: "spreadsheet",
  // Presentations
  ppt: "presentation",
  pptx: "presentation",
  key: "presentation",
  odp: "presentation",
  // Images
  jpg: "image",
  jpeg: "image",
  png: "image",
  gif: "image",
  bmp: "image",
  webp: "image",
  svg: "image",
  heic: "image",
  // Videos
  mp4: "video",
  mov: "video",
  avi: "video",
  wmv: "video",
  flv: "video",
  webm: "video",
  mkv: "video",
  // Audio
  mp3: "audio",
  wav: "audio",
  ogg: "audio",
  m4a: "audio",
  flac: "audio",
  aac: "audio",
  // Archives
  zip: "archive",
  rar: "archive",
  "7z": "archive",
  tar: "archive",
  gz: "archive",
  bz2: "archive",
  // Code
  js: "code",
  ts: "code",
  py: "code",
  java: "code",
  cpp: "code",
  html: "code",
  css: "code",
  php: "code",
  rb: "code",
  swift: "code",
  // Database
  sql: "database",
  db: "database",
  sqlite: "database",
  mdb: "database",
  // Fonts
  ttf: "font",
  otf: "font",
  woff: "font",
  woff2: "font",
  eot: "font",
};

type FileTypeConfig = {
  [key: string]: {
    icon: string;
    label: string;
    color: string;
    extensions: string[];
  };
};

export const fileTypeConfig: FileTypeConfig = {
  pdf: {
    icon: "description",
    label: "PDF Document",
    color: "bg-blue-200",
    extensions: ["pdf"],
  },
  document: {
    icon: "article",
    label: "Document",
    color: "bg-indigo-200",
    extensions: ["doc", "docx", "odt", "rtf", "txt"],
  },
  spreadsheet: {
    icon: "table_view",
    label: "Spreadsheet",
    color: "bg-emerald-200",
    extensions: ["xls", "xlsx", "csv", "ods", "numbers"],
  },
  presentation: {
    icon: "slideshow",
    label: "Presentation",
    color: "bg-orange-200",
    extensions: ["ppt", "pptx", "key", "odp"],
  },
  image: {
    icon: "image",
    label: "Image",
    color: "bg-purple-200",
    extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "heic"],
  },
  video: {
    icon: "movie",
    label: "Video",
    color: "bg-pink-200",
    extensions: ["mp4", "mov", "avi", "wmv", "flv", "webm", "mkv"],
  },
  audio: {
    icon: "volume_up",
    label: "Audio",
    color: "bg-amber-200",
    extensions: ["mp3", "wav", "ogg", "m4a", "flac", "aac"],
  },
  archive: {
    icon: "folder_zip",
    label: "Archive",
    color: "bg-gray-200",
    extensions: ["zip", "rar", "7z", "tar", "gz", "bz2"],
  },
  code: {
    icon: "code",
    label: "Code",
    color: "bg-emerald-200",
    extensions: [
      "js",
      "ts",
      "py",
      "java",
      "cpp",
      "html",
      "css",
      "php",
      "rb",
      "swift",
    ],
  },
  database: {
    icon: "database",
    label: "Database",
    color: "bg-teal-200",
    extensions: ["sql", "db", "sqlite", "mdb"],
  },
  font: {
    icon: "text_fields",
    label: "Font",
    color: "bg-red-200",
    extensions: ["ttf", "otf", "woff", "woff2", "eot"],
  },
  default: {
    icon: "draft",
    label: "File",
    color: "bg-gray-200",
    extensions: [],
  },
};

// Optimized helper function using direct lookup
export const getFileTypeByExtension = (
  filename: string
): (typeof fileTypeConfig)[keyof typeof fileTypeConfig] => {
  const extension = filename?.split(".").pop()?.toLowerCase() || "";
  const type = extensionMap[extension];
  return type ? fileTypeConfig[type] : fileTypeConfig.default;
};

// Optimized mime type lookup using direct object access
const mimeMap: Record<string, keyof typeof fileTypeConfig> = {
  "application/pdf": "pdf",
  "application/msword": "document",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "document",
  "application/vnd.ms-excel": "spreadsheet",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
    "spreadsheet",
  // Add more mime types as needed
};

export const getFileTypeByMimeType = (
  mimeType: string
): (typeof fileTypeConfig)[keyof typeof fileTypeConfig] => {
  const type = mimeMap[mimeType];
  return type ? fileTypeConfig[type] : fileTypeConfig.default;
};
