<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import {
  format,
  formatDuration,
  getYear,
  intervalToDuration,
  min,
  startOfWeek,
  startOfDay,
  endOfDay,
  type Duration,
  subWeeks,
} from "date-fns";
import { type Flight } from "~/types/amazon";
import { DataRequirement } from "~/utils/advertiser-datasets";
import {
  type CampaignSpendData,
  type CampaignSummaryMetrics,
} from "~/utils/advertiser-datasets/metrics/types";

export type CampaignProgressProps = {
  advertiserId: string;
  campaignId: string;
  currentFlight?: Flight;
};

const props = defineProps<CampaignProgressProps>();

const advertiserDatasetStore = useAdvertiserDatasetStore();

const campaignMetricsForFlight = computedAsync(async () => {
  if (!props.currentFlight) return;

  const { startDate, endDate } = getSundaysRange();

  const requirement = new DataRequirement<CampaignSpendData>({
    datasets: "campaign-spend",
    args: {
      advertiserId: props.advertiserId,
      campaignId: props.campaignId,
      startDate,
      endDate,
    },
  });

  return await fulfillRequirement(requirement);
});

const campaignMetricsSummary = computedAsync(() => {
  if (!props.currentFlight) return null;

  const { startDate, endDate } = getSundaysRange();

  const requirement = new DataRequirement<CampaignSummaryMetrics>({
    datasets: ["campaign-summary-metrics"],
    args: {
      advertiserId: props.advertiserId,
      campaignId: props.campaignId,
      startDate,
      endDate,
    },
  });

  return fulfillRequirement(requirement);
});

const currencyCode = computed(
  () => props.currentFlight?.currencyCode || undefined
);

const flightBudget = computed(() => props.currentFlight?.budgetAmount || 0);
const flightSpend = computed(
  () =>
    campaignMetricsForFlight.value?.[campaignMetricsForFlight.value.length - 1]
      ?.totalCost || 0
);

const flightBudgetRemaining = computed(
  () => flightBudget.value - flightSpend.value
);
const flightSpendPercentage = computed(
  () => (flightSpend.value / flightBudget.value) * 100
);

const totalDays = computed(() =>
  props.currentFlight
    ? intervalToDuration({
        start: props.currentFlight.startDateTime!,
        end: props.currentFlight.endDateTime!,
      })
    : ({ days: 0 } as Duration)
);
const daysElapsed = computed(() =>
  props.currentFlight
    ? intervalToDuration({
        start: props.currentFlight.startDateTime!,
        end: min([props.currentFlight.endDateTime!, new Date()]),
      })
    : ({ days: 0 } as Duration)
);
const daysRemaining = computed(() =>
  props.currentFlight
    ? intervalToDuration({
        start: new Date(),
        end: props.currentFlight.endDateTime!,
      })
    : ({ days: 0 } as Duration)
);

const daysElapsedPercentage = computed(() => {
  const totalInDays =
    (totalDays.value.years || 0) * 365 +
    (totalDays.value.months || 0) * 30 +
    (totalDays.value.days || 0);
  const daysElapsedInDays =
    (daysElapsed.value.years || 0) * 365 +
    (daysElapsed.value.months || 0) * 30 +
    (daysElapsed.value.days || 0);

  return (daysElapsedInDays / totalInDays) * 100;
});

const flightDateRangeText = computed(() => {
  if (!props.currentFlight) return "N/A";

  const startYear = getYear(props.currentFlight.startDateTime!);
  const endYear = getYear(props.currentFlight.endDateTime!);

  const startText = format(
    props.currentFlight.startDateTime!,
    startYear === endYear ? "MMM d" : "MMM d, yyyy"
  );
  const endText = format(props.currentFlight.endDateTime!, "MMM d, yyyy");

  return `${startText} - ${endText}`;
});

function fulfillRequirement<T>(requirement: DataRequirement<T>) {
  try {
    return advertiserDatasetStore.fulfillRequirement<T>(requirement);
  } catch (error) {
    console.error(error);
    return null;
  }
}

function getSundaysRange() {
  const lastSundayDate = startOfWeek(new Date());

  return {
    startDate: subWeeks(startOfDay(lastSundayDate), 1),
    endDate: endOfDay(lastSundayDate),
  };
}

function getSummaryMetric(metricName: keyof CampaignSummaryMetrics[number]) {
  return (
    campaignMetricsSummary.value?.[campaignMetricsSummary.value.length - 1]?.[
      metricName
    ]?.toFixed(0) || "N/A"
  );
}
</script>

<template>
  <div class="root">
    <div class="content">
      <!-- Date and Budget -->
      <div class="text-row">
        <span class="body-2-alt">
          Current flight: {{ flightDateRangeText }}
        </span>
        <span class="body-1">
          Budget: {{ formatCurrency(flightBudget, currencyCode) }}
        </span>
      </div>

      <!-- Spend Container -->
      <div class="section-container">
        <ProgressBar
          :value="flightSpendPercentage"
          :showValue="false"
          class="progress-bar spend-progress-bar"
        />

        <div class="text-row body-1">
          <span>Spent:</span>
          <span>{{ formatCurrency(flightSpend, currencyCode) }}</span>
        </div>

        <div class="text-row body-1">
          <span>Budget remaining:</span>
          <span>{{ formatCurrency(flightBudgetRemaining, currencyCode) }}</span>
        </div>
      </div>

      <!-- Days Elapsed Container -->
      <div class="section-container">
        <ProgressBar
          :value="daysElapsedPercentage"
          :showValue="false"
          class="progress-bar days-elapsed-progress-bar"
        />

        <div class="text-row body-1">
          <span>Days elapsed:</span>
          <span>{{
            formatDuration(daysElapsed, { format: ["years", "months", "days"] })
          }}</span>
        </div>

        <div class="text-row body-1">
          <span>Days remaining:</span>
          <span>{{
            formatDuration(daysRemaining, {
              format: ["years", "months", "days"],
            })
          }}</span>
        </div>
      </div>

      <Divider />

      <!-- Frequency Metrics Container -->
      <div class="frequency-metrics-container">
        <span class="label-2">Since Campaign Start</span>

        <div class="text-row">
          <div class="metric-item">
            <span class="metric-item-value">{{
              getSummaryMetric("averageImpressions")
            }}</span>
            <span class="metric-item-label">Average frequency</span>
          </div>
          <div class="metric-item">
            <span class="metric-item-value">{{
              getSummaryMetric("medianImpressions")
            }}</span>
            <span class="metric-item-label">Median frequency</span>
          </div>
          <div class="metric-item">
            <span class="metric-item-value">{{
              getSummaryMetric("avgTime")
            }}</span>
            <span class="metric-item-label">Time to convert (Days)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col;
}

.content {
  @apply flex flex-col gap-4;
}

.text-row {
  @apply flex flex-row flex-1 items-center justify-between gap-4;
}

.section-container {
  @apply flex flex-col gap-2;
}

.progress-bar {
  @apply rounded-full border-none h-3;
}

.spend-progress-bar {
  @apply bg-red-100;
}

:deep(.spend-progress-bar .p-progressbar-value) {
  @apply bg-red-600;
}

.days-elapsed-progress-bar {
  @apply bg-indigo-50;
}

:deep(.days-elapsed-progress-bar .p-progressbar-value) {
  @apply bg-indigo-500;
}

.metric-item {
  @apply flex flex-col gap-2;
}

.metric-item-label {
  @apply caption-2 text-disabled-text;
}

.metric-item-value {
  @apply text-primary-text font-medium text-3xl leading-none;
}
</style>
