<script setup lang="ts">
import type { Asset } from "~/types/amazon";

export type CreativesCarouselProps = {
  advertiserId: string;
  campaignId: string;
};

const props = defineProps<CreativesCarouselProps>();

const status = ref<"loading" | "error" | "success">("loading");
const assets = ref<Asset[]>([]);

watch(
  () => [props.advertiserId, props.campaignId],
  async ([advertiserId, campaignId]) => {
    if (!advertiserId || !campaignId) {
      return;
    }
    assets.value = await fetchCreativeAssets();
  },
  { immediate: true }
);

async function fetchCreativeAssets() {
  try {
    status.value = "loading";
    const response = await $fetch<Asset[]>(
      "/relay/rob/api/metadata/creative-assets",
      {
        query: {
          advertiserId: props.advertiserId,
          campaignId: props.campaignId,
        },
      }
    );

    status.value = "success";
    return response;
  } catch (error) {
    console.error("fetchCreativeAssets error:", error);
    status.value = "error";
    return [];
  }
}
</script>

<template>
  <GCarousel
    :items="assets"
    :state="status"
    empty-message="No creatives found"
    empty-icon="image"
    error-message="Failed to fetch creatives"
  >
    <template #default="{ item }">
      <img
        v-if="item.assetGlobal?.assetType === 'IMAGE'"
        class="creative-media"
        :src="item.assetVersionList?.[0]?.storageLocationUrls?.defaultUrl"
        :alt="item.assetVersionList?.[0]?.name"
      />
      <video
        v-else-if="item.assetGlobal?.assetType === 'VIDEO'"
        class="creative-media"
        :src="item.assetVersionList?.[0]?.storageLocationUrls?.defaultUrl"
        controls
        muted
      >
        Your browser does not support the video tag.
      </video>
    </template>
  </GCarousel>
</template>

<style scoped>
.creative-media {
  @apply w-full h-full object-contain;
}
</style>
