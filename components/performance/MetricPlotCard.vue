<script setup lang="ts">
import { ADSP_CAMPAIGN_METRIC_DEFINITIONS } from "~/utils/advertiser-datasets/metrics/constants";
import Skeleton from "primevue/skeleton";

export type MetricPlotCardProps = {
  metricName: string;
  value: string | undefined;
  activeColor?: string;
  loading?: boolean;
};

const props = defineProps<MetricPlotCardProps>();

defineEmits<{
  (e: "click", metricName: string): void;
  (e: "remove", metricName: string): void;
}>();

const metricDefinition = computed(() => {
  const metricDefinition =
    ADSP_CAMPAIGN_METRIC_DEFINITIONS[
      props.metricName as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS
    ];

  if (!metricDefinition) {
    console.warn(`Metric definition not found for ${props.metricName}`);
  }

  return metricDefinition;
});

const label = computed(() => {
  return metricDefinition.value?.label || "Unknown Metric";
});

const infoTooltip = computed(() => {
  return metricDefinition.value?.description || "No description available";
});
</script>

<template>
  <button
    class="root"
    :style="activeColor ? { '--border-color': activeColor } : {}"
    :data-active="!!activeColor"
    @click="$emit('click', metricName)"
  >
    <div class="content">
      <div class="header">
        <span class="label">{{ label }}</span>

        <div class="actions">
          <MaterialIcon v-tooltip="infoTooltip" icon="info" />

          <MaterialIcon
            icon="close"
            clickable
            @click="$emit('remove', metricName)"
          />
        </div>
      </div>

      <div class="valueContainer">
        <Skeleton v-if="loading" width="33%" class="h-8" />
        <span v-else class="value">{{ value }}</span>
      </div>
    </div>
  </button>
</template>

<style scoped>
.root {
  --border-color: #d4d4d4;
  @apply flex flex-1 p-6 h-32 rounded-xl
    border border-default-border;

  border-top: 0.375rem solid var(--border-color);
}

.root[data-active] {
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
}

.content {
  @apply flex flex-col flex-1 gap-3;
}

.header {
  @apply flex flex-row justify-between items-center text-disabled-text gap-4;
}

.label {
  @apply body-1 text-start line-clamp-2;
}

.actions {
  @apply flex flex-row gap-2 items-center;
}

.valueContainer {
  @apply flex flex-row justify-start items-center flex-1 overflow-hidden;
}

.value {
  @apply text-primary text-xl leading-8 font-medium font-inter truncate tracking-[-0.01875rem] whitespace-nowrap;
}
</style>
