<script setup lang="ts">
import type { ViewType } from "~/composables/usePerformanceTable";

type Props = {
  isAllExpanded: boolean;
  viewTypeOptions: Array<{ label: string; value: ViewType }>;
  disabled?: boolean;
  hideExpandCollapseButton?: boolean;
};

type Emits = {
  "expand-collapse-all": [];
};

defineProps<Props>();
defineEmits<Emits>();

const searchQuery = defineModel<string>("searchQuery", { required: true });
const viewType = defineModel<ViewType>("viewType", { required: true });
const selectedMetricColumns = defineModel<string[]>("selectedMetricColumns", {
  required: true,
});
</script>

<template>
  <div class="flex flex-wrap items-center gap-4 justify-between">
    <div class="flex flex-wrap items-center gap-4">
      <div class="search-input-wrapper">
        <MaterialIcon icon="search" class="search-icon" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search"
          class="search-input"
          :disabled="disabled"
        />
      </div>

      <div class="flex items-center gap-4">
        <SelectButton
          v-model="viewType"
          :options="viewTypeOptions"
          option-label="label"
          option-value="value"
          class="h-8"
          :disabled="disabled"
          :pt="{
            pcToggleButton: {
              root: { class: 'rounded-lg custom-toggle-button shrink-0' },
            },
          }"
        />

        <button
          v-if="viewType === 'both' && !hideExpandCollapseButton"
          class="button"
          :disabled="disabled"
          @click="$emit('expand-collapse-all')"
        >
          <MaterialIcon :icon="isAllExpanded ? 'remove' : 'add'" size="1rem" />
          <span class="label-3">
            {{ isAllExpanded ? "Collapse All" : "Expand All" }}
          </span>
        </button>
      </div>
    </div>

    <MetricsPoolSelector
      v-model="selectedMetricColumns"
      label="Columns"
      prefix-icon="view_column"
      suffix-icon="arrow_drop_down"
      :max-selection="20"
      :disabled="disabled"
    />
  </div>
</template>

<style scoped>
.button {
  @apply h-8 flex flex-row items-center justify-center gap-2 px-4 py-2 bg-secondary-background rounded-lg select-none shrink-0;
}

.button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.search-input-wrapper {
  @apply relative flex items-center w-[13.75rem];
}

:deep(.custom-toggle-button.p-togglebutton::before) {
  @apply top-0 left-0 w-full h-full bg-white rounded-none first:rounded-l-md last:rounded-r-md;
}

:deep(.custom-toggle-button.p-togglebutton-checked::before) {
  @apply bg-tertiary-background;
}

.search-input {
  @apply h-8 w-64 pl-10 pr-3 py-2 border border-default-border rounded-lg text-sm focus:outline-default-border;
}

.search-input:disabled {
  @apply opacity-50 cursor-not-allowed bg-gray-100;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none;
  font-size: 1rem;
}
</style>
