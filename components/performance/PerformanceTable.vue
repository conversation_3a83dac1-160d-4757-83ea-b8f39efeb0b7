<script setup lang="ts">
import { usePerformanceTable } from "~/composables/usePerformanceTable";

export type PerformanceTableProps = {
  advertiserId: string;
  campaignId: string;
  funnel: string | null;
  startDate: Date;
  endDate: Date;
  viewType: "day" | "week";
};

const props = defineProps<PerformanceTableProps>();

const {
  // Data
  selectedMetricColumns,
  filteredTableData,

  // UI State
  searchQuery,
  viewType,
  expandedRows,
  tableKey,
  isAllExpanded,

  // Actions
  expandCollapseAll,

  // Constants
  viewTypeOptions,

  // Status
  isLoading,
  hasError,
  errorMessage,
  status,
} = usePerformanceTable(props);

// Computed properties for controlling header state
const isHeaderDisabled = computed(() => {
  return status.value === "idle" || isLoading.value;
});

const shouldHideExpandCollapseButton = computed(() => {
  return (
    status.value === "idle" ||
    isLoading.value ||
    filteredTableData.value.length === 0
  );
});
</script>

<template>
  <div class="flex flex-col flex-1 gap-8">
    <PerformanceTableHeader
      v-model:search-query="searchQuery"
      v-model:view-type="viewType"
      v-model:selected-metric-columns="selectedMetricColumns"
      :is-all-expanded="isAllExpanded"
      :view-type-options="viewTypeOptions"
      :disabled="isHeaderDisabled"
      :hide-expand-collapse-button="shouldHideExpandCollapseButton"
      @expand-collapse-all="expandCollapseAll"
    />

    <!-- Error Message -->
    <div v-if="hasError" class="text-red-600 text-sm p-2">
      {{ errorMessage }}
    </div>

    <!-- Idle State (No Metrics Selected) -->
    <div
      v-if="status === 'idle'"
      class="flex flex-col items-center justify-center h-96 gap-4 p-8 border border-neutral-200 rounded-lg bg-neutral-50"
    >
      <MaterialIcon icon="analytics" class="text-neutral-400 text-5xl" />
      <div class="text-center">
        <h3 class="text-lg font-semibold text-neutral-700 mb-2">
          Select Metrics to View Performance Data
        </h3>
        <p class="text-neutral-600 text-sm max-w-md">
          Choose metrics from the "Columns" dropdown to start analyzing your
          campaign performance.
        </p>
      </div>
    </div>

    <!-- Table -->
    <PerformanceTreeTable
      v-else
      v-model:expanded-rows="expandedRows"
      :table-data="hasError ? [] : filteredTableData"
      :table-key="tableKey"
      :selected-metric-columns="selectedMetricColumns"
      :view-type="viewType"
      :loading="isLoading"
    />
  </div>
</template>
