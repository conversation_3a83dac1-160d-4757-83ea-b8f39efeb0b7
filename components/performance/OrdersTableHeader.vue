<script setup lang="ts">
import { CAMPAIGN_STATUS_OPTIONS, FUNNEL_TACTIC_OPTIONS } from "~/constants/campaignConstants";

type Props = {
};

type Emits = {
  "clear-selection": [];
  "clear-filters": [];
};

defineProps<Props>();
defineEmits<Emits>();

const searchQuery = defineModel<string>("searchQuery", { required: true });
const selectedStatuses = defineModel<string[]>("selectedStatuses", { required: true });
const selectedFunnelTactics = defineModel<string[]>("selectedFunnelTactics", { required: true });

// Use shared constants
const statusOptions = CAMPAIGN_STATUS_OPTIONS;
const funnelTacticOptions = FUNNEL_TACTIC_OPTIONS;

// Function to generate filter labels with selection count
const generateFilterLabel = (baseLabel: string, selectedValues: string[], options: Array<{label: string, value: string}>) => {
  if (selectedValues.length === 0) {
    return baseLabel;
  }
  
  const firstSelectedOption = options.find(option => option.value === selectedValues[0]);
  let label = `${baseLabel}: ${firstSelectedOption?.label || selectedValues[0]}`;
  
  if (selectedValues.length > 1) {
    label += ` +${selectedValues.length - 1}`;
  }
  
  return label;
};

// Computed for clear filters button state
const hasActiveFilters = computed(() => 
  selectedStatuses.value.length > 0 || selectedFunnelTactics.value.length > 0
);

const statusFilterLabel = computed(() => 
  generateFilterLabel('Status', selectedStatuses.value, statusOptions)
);

const funnelTacticFilterLabel = computed(() => 
  generateFilterLabel('Funnel Tactic', selectedFunnelTactics.value, funnelTacticOptions)
);


</script>

<template>
  <div class="flex flex-row gap-2 items-center">
    <div class="search-input-wrapper">
        <MaterialIcon icon="search" class="search-icon" />
        <input
        v-model="searchQuery"
        type="text"
        placeholder="Search"
        class="search-input"/>
    </div>

    <!-- Filter Dropdowns -->
    <GMultiSelect 
        v-model="selectedStatuses"
        :options="statusOptions"
        :label="statusFilterLabel"
        suffix-icon="arrow_drop_down"
        :maxSelection="10"
        :showSearch="false"
        clearable/>

     <GMultiSelect 
        v-model="selectedFunnelTactics"
        :options="funnelTacticOptions"
        :label="funnelTacticFilterLabel"
        suffix-icon="arrow_drop_down"
        :maxSelection="10"
        :showSearch="false"/>

    <!-- Clear Filters Button -->
    <button
        class="clear-filters-button"
        :class="{ 'has-filters': hasActiveFilters, 'no-filters': !hasActiveFilters }"
        :disabled="!hasActiveFilters"
        @click="$emit('clear-filters')">
        Clear Filters
    </button>
  </div>
</template>

<style scoped>
.button {
  @apply h-8 flex flex-row items-center justify-center gap-2 px-4 py-2 bg-secondary-background rounded-lg select-none shrink-0;
}

.search-input-wrapper {
  @apply relative flex items-center w-[13.75rem];
}

.search-input {
  @apply h-8 w-64 pl-10 pr-3 py-2 border border-default-border rounded-lg text-sm focus:outline-default-border;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none;
  font-size: 1rem;
}

.clear-filters-button {
  @apply h-8 px-4 py-2 label-3 flex items-center justify-center rounded-lg transition-all duration-200;
}

.clear-filters-button.has-filters {
  @apply text-primary-text hover:bg-tertiary-background;
}

.clear-filters-button.no-filters {
  @apply bg-transparent border-transparent text-gray-400 cursor-not-allowed;
}


</style> 