<script setup lang="ts">
import { toRef } from "vue";
import Column from "primevue/column";
import { formatCurrency } from "~/utils/currencyUtils";
import { dateText } from "~/utils/dateTextUtils";
import { getStatusText } from "~/constants/campaignConstants";
import FallbackAvatar from "~/components/common/FallbackAvatar.vue";
import FunnelPill from "~/components/common/funnel/FunnelPill.vue";
import OrdersTableHeader from "~/components/performance/OrdersTableHeader.vue";

const advertiserStore = useAdvertiserStore();

const props = defineProps<{
  advertiserIds: string[];
}>();

const {
  campaigns,
  loading,
  error,
  currentPage,
  pageSize,
  totalElements,
  searchQuery,
  selectedStatuses,
  selectedFunnelTactics,
  onPageChange,
  clearFilters,
  createSortableHeader,
} = useCampaigns(toRef(props, "advertiserIds"), "orders-table-state");

// Set default status filter for Orders table (only if no persisted state)
if (selectedStatuses.value.length === 0) {
  selectedStatuses.value = ["DELIVERING"];
}

const formatDate = (dateString: string) => {
  if (!dateString) return "—";
  return dateText(new Date(dateString));
};

const formatBudget = (budget?: number, advertiserId?: string) => {
  if (budget === undefined || budget === null) return "—";
  const advertiser = advertiserStore.getAdvertiserById(advertiserId);
  return formatCurrency(budget, advertiser?.currencyCode ?? "USD");
};

const formatPacing = (pacing?: number) => {
  if (pacing === undefined || pacing === null) return "—";
  return `${Math.round(pacing * 100)}%`;
};

const nameHeader = createSortableHeader("Name", "campaignName");
const startDateHeader = createSortableHeader("Start Date", "startDate");
const endDateHeader = createSortableHeader("End Date", "endDate");
const budgetHeader = createSortableHeader("Flight Budget", "budget");
const pacingHeader = createSortableHeader("Pacing", "pacingPercentage");
</script>

<template>
  <div class="flex flex-col flex-1 gap-4 overflow-hidden">
    <OrdersTableHeader
      v-model:searchQuery="searchQuery"
      v-model:selectedStatuses="selectedStatuses"
      v-model:selectedFunnelTactics="selectedFunnelTactics"
      @clear-filters="clearFilters"
    />

    <GTable
      :data="error ? [] : campaigns"
      data-key="campaignId"
      class="flex-1 min-h-0 table-fixed"
      :loading="loading"
      empty-message="No campaigns found."
      :paginator="true"
      :lazy="true"
      :totalRecords="totalElements"
      :first="currentPage * pageSize"
      :rows="pageSize"
      :rowsPerPageOptions="[10, 20, 50, 100]"
      @page="onPageChange"
    >
      <Column class="w-[35%] min-w-60">
        <template #header>
          <div
            class="flex items-center gap-2 cursor-pointer"
            @click="nameHeader.onClick"
          >
            <span class="label-3">{{ nameHeader.label }}</span>
            <MaterialIcon
              :icon="nameHeader.icon.value"
              size="16px"
              class="text-gray-500"
            />
          </div>
        </template>
        <template #body="slotProps">
          <div class="flex items-center gap-3">
            <FunnelPill
              :funnel="slotProps.data.funnelStage"
              size="sm"
              class="flex-shrink-0"
            />
            <div class="flex flex-col gap-1 min-w-0 flex-1">
              <a
                :href="`/orders/${slotProps.data.advertiserId}/${slotProps.data.campaignId}`"
                class="body-1 truncate text-left hover:text-brand-primary focus:text-brand-primary transition-colors cursor-pointer underline decoration-dotted underline-offset-2 hover:decoration-solid focus:decoration-solid block"
              >
                {{ slotProps.data.campaignName }}
              </a>
            </div>
          </div>
        </template>
      </Column>

      <Column field="advertiserName" class="w-[20%] min-w-48">
        <template #header>
          <span class="label-3">Advertiser</span>
        </template>
        <template #body="slotProps">
          <div class="flex items-center gap-2">
            <FallbackAvatar
              :image="`/images/advertiser/${slotProps.data.advertiserId}`"
              :label="slotProps.data.advertiserName"
              shape="rounded"
              size="normal"
            />
            <span class="truncate body-1">{{
              slotProps.data.advertiserName
            }}</span>
          </div>
        </template>
      </Column>

      <Column class="w-[12%] min-w-40">
        <template #header>
          <div
            class="flex items-center gap-2 cursor-pointer"
            @click="startDateHeader.onClick"
          >
            <span class="label-3">{{ startDateHeader.label }}</span>
            <MaterialIcon
              :icon="startDateHeader.icon.value"
              size="16px"
              class="text-gray-500"
            />
          </div>
        </template>
        <template #body="slotProps">
          <span class="body-1">{{ formatDate(slotProps.data.startDate) }}</span>
        </template>
      </Column>

      <Column class="w-[12%] min-w-40">
        <template #header>
          <div
            class="flex items-center gap-2 cursor-pointer"
            @click="endDateHeader.onClick"
          >
            <span class="label-3">{{ endDateHeader.label }}</span>
            <MaterialIcon
              :icon="endDateHeader.icon.value"
              size="16px"
              class="text-gray-500"
            />
          </div>
        </template>
        <template #body="slotProps">
          <span class="body-1">{{ formatDate(slotProps.data.endDate) }}</span>
        </template>
      </Column>

      <Column class="w-[12%] min-w-28">
        <template #header>
          <div
            class="flex items-center gap-2 cursor-pointer"
            @click="budgetHeader.onClick"
          >
            <span class="label-3">{{ budgetHeader.label }}</span>
            <MaterialIcon
              :icon="budgetHeader.icon.value"
              size="16px"
              class="text-gray-500"
            />
          </div>
        </template>
        <template #body="slotProps">
          <span class="body-1">{{
            formatBudget(slotProps.data.budget, slotProps.data.advertiserId)
          }}</span>
        </template>
      </Column>

      <Column class="w-[7%] min-w-24">
        <template #header>
          <div
            class="flex items-center gap-2 cursor-pointer"
            @click="pacingHeader.onClick"
          >
            <span class="label-3">{{ pacingHeader.label }}</span>
            <MaterialIcon
              :icon="pacingHeader.icon.value"
              size="16px"
              class="text-gray-500"
            />
          </div>
        </template>
        <template #body="slotProps">
          <span class="body-1">{{
            formatPacing(slotProps.data.pacingPercentage)
          }}</span>
        </template>
      </Column>

      <Column field="status" class="w-[8%] min-w-32">
        <template #header>
          <span class="label-3">Status</span>
        </template>
        <template #body="slotProps">
          <span class="body-1 text-gray-900">
            {{ getStatusText(slotProps.data.status) }}
          </span>
        </template>
      </Column>
    </GTable>
  </div>
</template>
