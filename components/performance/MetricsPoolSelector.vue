<script setup lang="ts">
import { onUnmounted } from "vue";
import { ADSP_CAMPAIGN_METRIC_DEFINITIONS } from "~/utils/advertiser-datasets/metrics/constants";

defineProps<{
  label: string;
  prefixIcon?: string;
  suffixIcon?: string;
  maxSelection: number;
}>();

const selectRef = ref();
const searchInputRef = ref();

const modelValue = defineModel<string[]>({ default: [] });

const options = ref(getOptions());
const internalValue = ref([...modelValue.value]);

const filterValue = ref("");
const filteredOptions = computed(() => {
  const labelMatches = options.value.filter((option) =>
    option.label.toLowerCase().includes(filterValue.value.toLowerCase())
  );

  const labelMatchesSet = new Set(labelMatches.map((option) => option.value));
  const descriptionMatches = options.value
    .filter((option) => !labelMatchesSet.has(option.value))
    .filter((option) =>
      option.description.toLowerCase().includes(filterValue.value.toLowerCase())
    );

  return [...labelMatches, ...descriptionMatches];
});

watch(
  modelValue,
  (newValue) => {
    internalValue.value = [...newValue];
  },
  { immediate: true }
);

watch(
  internalValue,
  () => {
    options.value = getOptions();
  },
  { deep: true }
);

const hasChanged = computed(() => {
  return (
    JSON.stringify(internalValue.value.sort()) !==
    JSON.stringify(modelValue.value.sort())
  );
});

let blurHandler: (() => void) | null = null;

function getOptions() {
  return Object.keys(ADSP_CAMPAIGN_METRIC_DEFINITIONS)
    .sort((k1, k2) => (modelValue.value.includes(k1) ? -1 : 1))
    .map((k) => ({
      label: getMetricLabel(k),
      description: getMetricDescription(k),
      value: k,
    }));
}

function getMetricLabel(metric: string) {
  return ADSP_CAMPAIGN_METRIC_DEFINITIONS[
    metric as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS
  ].label;
}

function getMetricDescription(metric: string) {
  return ADSP_CAMPAIGN_METRIC_DEFINITIONS[
    metric as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS
  ].description;
}

function applySelection() {
  modelValue.value = [...internalValue.value];
  selectRef.value.hide();
}

function onShow() {
  focusSearchInput();
  searchInputRef.value.addEventListener("blur", focusSearchInput);
}

function onHide() {
  if (searchInputRef.value) {
    searchInputRef.value.removeEventListener("blur", focusSearchInput);
  }

  filterValue.value = "";
}

function focusSearchInput() {
  if (searchInputRef.value) {
    searchInputRef.value.focus();
  }
}

onUnmounted(() => {
  if (blurHandler && searchInputRef.value) {
    searchInputRef.value.removeEventListener("blur", blurHandler);
  }
});
</script>

<template>
  <MultiSelect
    ref="selectRef"
    :options="filteredOptions"
    v-model="internalValue"
    :selection-limit="maxSelection"
    option-label="label"
    option-value="value"
    :pt="{
      labelContainer: { class: '!hidden' },
      root: { class: 'outline-none border-none w-40' },
      dropdown: { class: 'flex-1' },
      overlay: {
        class:
          'mt-2 w-[19.5rem] pt-2 rounded-xl bg-white border border-default-border shadow-lg',
      },
      listContainer: { class: '!max-h-[400px] p-0' },
      option: { class: 'px-4 py-2' },
    }"
    @show="onShow()"
    @hide="onHide()"
  >
    <template #dropdownicon>
      <div class="button text-primary">
        <MaterialIcon v-if="prefixIcon" :icon="prefixIcon" size="20px" />
        <span class="label-3">{{ label }}</span>
        <MaterialIcon v-if="suffixIcon" :icon="suffixIcon" size="16px" />
      </div>
    </template>

    <template #header>
      <div class="search-container">
        <MaterialIcon icon="search" class="search-icon" size="16px" />
        <input
          ref="searchInputRef"
          id="metrics-pool-selector-search"
          v-model="filterValue"
          type="text"
          class="search-input"
          placeholder="Search metrics..."
        />
      </div>
    </template>

    <template #option="{ option }">
      <div class="option-container">
        <span class="option">{{ option.label }}</span>
        <MaterialIcon
          v-tooltip="option.description"
          icon="info"
          class="text-disabled-text"
          size="16px"
        />
      </div>
    </template>

    <template #footer>
      <div class="footer-container">
        <span
          v-if="internalValue.length === maxSelection"
          class="selection-limit-text"
        >
          Maximum amount of metrics selected
        </span>

        <div class="footer-actions">
          <button class="button text-primary label-3" @click="selectRef.hide()">
            Cancel
          </button>
          <button
            class="button bg-neutral-900 disabled:bg-neutral-500 disabled:cursor-not-allowed text-white label-3"
            :disabled="!hasChanged"
            @click="applySelection()"
          >
            Apply
          </button>
        </div>
      </div>
    </template>
  </MultiSelect>
</template>

<style scoped>
.button {
  @apply h-8 flex-1 flex flex-row items-center justify-center gap-2 px-4 py-2 border border-default-border rounded-lg select-none;
}

.option-container {
  @apply flex-1 flex flex-row items-center gap-2 justify-between;
}

.option {
  @apply flex-1 body-1 text-wrap line-clamp-2;
}

.search-input {
  @apply w-full h-full border border-default-border rounded-lg focus:border-primary outline-none pl-8 pr-3 py-1;
}

.search-icon {
  @apply absolute top-1/2 left-2 -translate-y-1/2 text-disabled-text;
}

.search-container {
  @apply flex-1 relative flex flex-row items-center gap-2 justify-between mx-4 my-2;
}

.footer-container {
  @apply flex-1 flex flex-col items-center gap-2 justify-between p-4;
}

.footer-actions {
  @apply flex-1 flex flex-row items-center gap-2 justify-between w-full;
}

.selection-limit-text {
  @apply caption-2 text-orange-600;
}
</style>
