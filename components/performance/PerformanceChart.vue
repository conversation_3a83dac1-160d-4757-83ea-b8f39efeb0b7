<script setup lang="ts">
import {
  useLocalStorage,
  StorageSerializers,
  computedAsync,
} from "@vueuse/core";
import { format } from "date-fns";
import {
  DataRequirement,
  RequirementsFactory,
} from "~/utils/advertiser-datasets";
import {
  ADSP_CAMPAIGN_METRIC_DEFINITIONS,
  DEFAULT_METRICS_PER_FUNNEL_TYPE,
} from "~/utils/advertiser-datasets/metrics/constants";
import type { PerDateMetrics } from "~/utils/advertiser-datasets/metrics/types";
import { MetricFormatter } from "~/utils/performance/formatters";

export type PerformanceChartProps = {
  advertiserId: string;
  campaignId: string;
  funnel: string | null;
  startDate: Date;
  endDate: Date;
  viewType: "day" | "week";
};

const MAX_SELECTED_METRICS = 4;

const { user } = useUserSession();
const advertiserStore = useAdvertiserStore();
const props = defineProps<PerformanceChartProps>();

const advertiser = computed(() =>
  advertiserStore.getAdvertiserById(props.advertiserId)
);

// Get default metrics based on funnel
const defaultMetricsForFunnel = computed(() => {
  if (!props.funnel) return [];

  const funnelConfig = DEFAULT_METRICS_PER_FUNNEL_TYPE.find((config) =>
    config.funnels.has(props.funnel!)
  );

  return funnelConfig?.metrics || [];
});

const colors = useLocalStorage<string[]>(
  await hashKey(
    `${user.value?.sub}:${props.campaignId}:performance-chart:colors`
  ),
  [
    "#DD4227", // brand primary
    "#736CFF", // indigo
    "#10b981", // emerald
    "#7F1D1D", // dark red
  ],
  { serializer: StorageSerializers.object, initOnMounted: true }
);

const metricsInPool = useLocalStorage<string[]>(
  await hashKey(
    `${user.value?.sub}:${props.campaignId}:performance-chart:metrics-in-pool`
  ),
  [],
  { serializer: StorageSerializers.object, initOnMounted: true }
);

const selectedMetrics = useLocalStorage<string[]>(
  await hashKey(
    `${user.value?.sub}:${props.campaignId}:performance-chart:selected-metrics`
  ),
  [],
  { serializer: StorageSerializers.object, initOnMounted: true }
);

const advertiserDatasetStore = useAdvertiserDatasetStore();

const lineItemsFilter = ref<string[]>([]);
const creativesFilter = ref<string[]>([]);

const dataRequirementsMap = computed(() =>
  Object.fromEntries(
    metricsInPool.value.map((metric) => [
      metric,
      RequirementsFactory.createRequirement("per-date-metrics", {
        advertiserId: props.advertiserId,
        campaignId: props.campaignId,
        startDate: props.startDate,
        endDate: props.endDate,
        metricName: metric,
        lineItemIds: lineItemsFilter.value,
        creativeIds: creativesFilter.value,
        viewType: props.viewType,
      }),
    ])
  )
);

const metricsData = computedAsync<Record<string, PerDateMetrics | null>>(
  async () => await getMetricsData(),
  {}
);

const metricTotals = computed(() =>
  Object.fromEntries(
    Object.entries(metricsData.value).map(([metric, data]) => {
      if (!data || !data.totalForPeriod[metric]) return [metric, "N/A"];

      return [
        metric,
        MetricFormatter.formatValue(
          metric,
          data.totalForPeriod[metric],
          advertiser.value?.currencyCode
        ),
      ];
    })
  )
);

const chartLabels = computed(() => {
  if (!selectedMetrics.value.length || !metricsData.value) return [];

  const firstMetric = selectedMetrics.value[0];
  const timeSeries = metricsData.value[firstMetric]?.timeSeries;
  return timeSeries?.map((t) => format(t.date, "yyyy-MM-dd")) ?? [];
});

const chartDatasets = computed(() =>
  selectedMetrics.value.map((metric, index) => ({
    label:
      ADSP_CAMPAIGN_METRIC_DEFINITIONS[
        metric as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS
      ].label,
    metricId: metric, // Add the original metric ID here
    borderColor: colors.value[index],
    data:
      metricsData.value[metric]?.timeSeries.map((t) => t[metric] ?? null) ?? [],
  }))
);

// Initialize metrics based on funnel when it becomes available
watch(
  [() => props.funnel, defaultMetricsForFunnel],
  ([funnel, defaultMetrics]) => {
    // If funnel is available and no metrics are set yet, initialize with defaults
    if (
      funnel &&
      defaultMetrics.length > 0 &&
      metricsInPool.value.length === 0
    ) {
      metricsInPool.value = [...defaultMetrics];
      // Select first 4 metrics by default
      selectedMetrics.value = defaultMetrics.slice(0, MAX_SELECTED_METRICS);
    }
  },
  { immediate: true }
);

watch(metricsInPool, (newMetrics) => {
  handleMetricsPoolUpdate(newMetrics);
});

function handleMetricsPoolUpdate(newMetrics: string[]) {
  selectedMetrics.value
    .filter((m) => !newMetrics.includes(m))
    .forEach(handleMetricRemove);
}

function handleSelectionChange(metric: string) {
  const selectedIndex = selectedMetrics.value.indexOf(metric);

  if (selectedIndex !== -1) {
    selectedMetrics.value.splice(selectedIndex, 1);
    rotateColorsOnIndex(selectedIndex);
    return;
  }

  if (selectedMetrics.value.length < MAX_SELECTED_METRICS) {
    selectedMetrics.value.push(metric);
  }
}

function handleMetricRemove(metric: string) {
  if (selectedMetrics.value.includes(metric)) {
    handleSelectionChange(metric);
  }

  metricsInPool.value = metricsInPool.value.filter((m) => m !== metric);
}

function rotateColorsOnIndex(index: number) {
  const color = colors.value.splice(index, 1)[0];
  colors.value.push(color);
}

async function getMetricsData() {
  if (Object.values(dataRequirementsMap.value).some((r) => !r.isValid()))
    return;

  return Object.fromEntries(
    await Promise.all(
      Object.entries(dataRequirementsMap.value).map(
        async ([metric, requirement]) => {
          const data = await fulfillRequirement(metric, requirement);
          return [metric, data];
        }
      )
    )
  );
}

function fulfillRequirement(metric: string, requirement: DataRequirement) {
  try {
    return advertiserDatasetStore.fulfillRequirement(requirement);
  } catch (error) {
    console.error(`Error fulfilling requirement for metric ${metric}:`, error);
    return null;
  }
}
</script>

<template>
  <div class="root">
    <div class="header">
      <CampaignDataFilter
        v-model="lineItemsFilter"
        item-name-descriptor="line item"
        :advertiser-id="advertiserId"
        :campaign-id="campaignId"
        dataset-id="line-item-names"
      />

      <CampaignDataFilter
        v-model="creativesFilter"
        item-name-descriptor="creative"
        :advertiser-id="advertiserId"
        :campaign-id="campaignId"
        dataset-id="creative-names"
      />

      <MetricsPoolSelector
        class="ml-auto"
        v-model="metricsInPool"
        label="Add metric"
        prefix-icon="add"
        :max-selection="8"
      />
    </div>

    <MetricsPlotSelector
      :options="metricsInPool"
      :value="selectedMetrics"
      :metrics-data="metricTotals"
      :colors="colors"
      @selection-change="handleSelectionChange"
      @metric-remove="handleMetricRemove"
    />

    <MultiAxisLineChart
      :labels="chartLabels"
      :datasets="chartDatasets"
      :height="470"
      :currency-code="advertiser?.currencyCode"
    />
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col gap-4;
}

.header {
  @apply flex flex-row items-center gap-4;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-gray-200 border-t-brand-primary rounded-full animate-spin mx-auto;
}

.loading-state,
.empty-state {
  @apply bg-gray-50 rounded-lg border border-gray-200;
}
</style>
