<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import { getCampaignFlights, getCurrentFlight } from "~/utils/campaignFlights";

export type CampaignSpendWidgetProps = {
  advertiserId: string;
  campaignId: string;
};

const props = defineProps<CampaignSpendWidgetProps>();

const flights = computedAsync(
  async () => getCampaignFlights(props.campaignId, props.advertiserId),
  []
);

const currentFlight = computed(() => {
  const flight = getCurrentFlight(flights.value);
  return flight;
});
</script>

<template>
  <div class="root">
    <div class="content">
      <CreativesCarousel
        class="w-[30rem] max-w-[30rem] aspect-[3/2]"
        :advertiser-id="advertiserId"
        :campaign-id="campaignId"
      />

      <CampaignProgress
        :advertiser-id="advertiserId"
        :campaign-id="campaignId"
        :current-flight="currentFlight"
      />
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-1 w-full;
}

.content {
  @apply flex flex-1 flex-col lg:flex-row gap-8 items-center lg:items-start;
}
</style>
