<script setup lang="ts">
import type { TreeNode } from "primevue/treenode";
import type { ViewType } from "~/composables/usePerformanceTable";
import { ADSP_CAMPAIGN_METRIC_DEFINITIONS } from "~/utils/advertiser-datasets/metrics/constants";
import { defaultPt } from "../common/g-table/constants";

type Props = {
  tableData: TreeNode[];
  tableKey: string;
  selectedMetricColumns: string[];
  viewType: ViewType;
  loading?: boolean;
};

const props = defineProps<Props>();

const hasData = computed(() => props.tableData.length > 0);

const expandedRows = defineModel<Record<string, boolean>>("expandedRows", {
  required: true,
});

function toggleRowExpansion(node: TreeNode) {
  if (!node.children || node.children.length === 0) return;

  const newExpandedRows = { ...expandedRows.value };
  if (newExpandedRows[node.key as string]) {
    delete newExpandedRows[node.key as string];
  } else {
    newExpandedRows[node.key as string] = true;
  }
  expandedRows.value = newExpandedRows;
}
</script>

<template>
  <div class="h-[49.125rem] overflow-hidden">
    <TreeTable
      v-if="hasData"
      v-model:expanded-keys="expandedRows"
      :value="tableData"
      :key="tableKey"
      :loading="loading"
      data-key="key"
      paginator
      scroll-height="flex"
      scrollable
      :pt="{
        ...defaultPt,
      }"
    >
      <Column
        field="name"
        header="Name"
        :expander="viewType === 'both'"
        frozen
        style="min-width: 22.5rem; width: 22.5rem"
        body-style="outline: 1px solid #e4e4e4"
      >
        <template #rowtoggleicon="{ expanded }: any">
          <MaterialIcon :icon="expanded ? 'arrow_drop_down' : 'arrow_right'" />
        </template>

        <template #body="{ node }: { node: TreeNode }">
          <div
            class="flex items-center gap-2 w-full select-none"
            :class="{
              'cursor-pointer': node.children && node.children.length > 0,
            }"
            @click="toggleRowExpansion(node)"
          >
            <span>{{ node.data.name }}</span>
          </div>
        </template>
      </Column>

      <Column
        v-for="metric in selectedMetricColumns"
        :key="metric"
        :field="`${metric}`"
        :header="ADSP_CAMPAIGN_METRIC_DEFINITIONS[metric as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS].label"
        class="text-right"
        style="min-width: 172px !important"
      />

      <template #paginatorcontainer="slotProps: any">
        <GTablePaginator v-bind="slotProps" />
      </template>
    </TreeTable>
    
    <div
      v-else
      class="flex flex-col items-center justify-center h-full gap-2 p-2 border border-default-border rounded-lg"
    >
      <template v-if="loading">
        <ProgressSpinner />
      </template>
      <template v-else>
        <MaterialIcon
          icon="table_chart"
          class="bg-primary-background rounded-full p-4"
        />
        <span class="label-3 text-neutral-700">No data available</span>
      </template>
    </div>
  </div>
</template>

<style scoped>
:deep(.text-right .p-treetable-column-header-content) {
  @apply justify-end;
}

:deep(.p-treetable-header-cell:not(:has(.p-frozen-column))) {
  @apply bg-primary-background;
  box-shadow: inset 0 -1px 0 0 #e4e4e4;
}

:deep(.text-right .p-treetable-body-cell-content) {
  @apply justify-self-end body-1;
}

/* Hide the default PrimeVue toggle icon */
:deep(.p-treetable-node-toggle-button .p-treetable-node-toggle-icon) {
  @apply hidden;
}

/* Ensure the toggle button container is properly sized for our custom icon */
:deep(.p-treetable-node-toggle-button) {
  @apply flex items-center justify-center w-7 h-7 rounded-full shrink-0;
}
</style>
