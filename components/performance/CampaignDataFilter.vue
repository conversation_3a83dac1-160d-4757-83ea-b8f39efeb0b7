<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import type { MultiSelectProps } from "primevue/multiselect";
import { DataRequirement } from "~/utils/advertiser-datasets";
import type { DatasetId } from "~/utils/advertiser-datasets/datasets";

export type BaseFilterProps = Omit<
  MultiSelectProps,
  "pt" | "optionLabel" | "optionValue" | "emptyMessage" | "options"
> & {
  itemNameDescriptor: string;
  advertiserId: string;
  campaignId: string;
  datasetId: DatasetId;
};

const props = defineProps<BaseFilterProps>();

const advertiserDatasetStore = useAdvertiserDatasetStore();
const dataRequirement = computed(
  () =>
    new DataRequirement({
      datasets: [props.datasetId],
      args: {
        advertiserId: props.advertiserId,
        campaignId: props.campaignId,
      },
    })
);

const evaluating = shallowRef(false);

const options = computedAsync(
  async () => await fulfillRequirement(dataRequirement.value),
  [],
  { evaluating }
);

const modelValue = defineModel<string[]>({ default: [] });

const labelText = computed(() => {
  if (options.value?.length === 0) return `No ${props.itemNameDescriptor}s`;
  if (modelValue.value.length === 0) return `All ${props.itemNameDescriptor}s`;
  if (modelValue.value.length === options.value?.length)
    return `All ${props.itemNameDescriptor}s`;
  return `${modelValue.value.length} ${props.itemNameDescriptor}${
    modelValue.value.length === 1 ? "" : "s"
  } selected`;
});

async function fulfillRequirement(requirement: DataRequirement) {
  if (!requirement.isValid()) return;

  let data: Record<string, string> = {};

  try {
    data = await advertiserDatasetStore.fulfillRequirement<
      Record<string, string>
    >(requirement);
  } catch (error) {
    console.error(error);
  }

  return Object.entries(data).map(([key, value]) => ({
    label: value,
    value: key,
  }));
}
</script>

<template>
  <MultiSelect
    v-bind="{ ...$props, ...$attrs }"
    v-model="modelValue"
    :options="options"
    option-label="label"
    option-value="value"
    empty-message="No data available"
    :pt="{
      labelContainer: { class: '!hidden' },
      root: { class: 'outline-none border-none w-fit' },
      dropdown: { class: 'flex-1 w-full' },
      overlay: { class: 'mt-2' },
      option: { class: 'option' },
    }"
  >
    <template #dropdownicon>
      <div class="button text-primary">
        <span class="label-3">{{ evaluating ? "loading..." : labelText }}</span>
        <MaterialIcon icon="arrow_drop_down" />
      </div>
    </template>
  </MultiSelect>
</template>

<style scoped>
.button {
  @apply h-8 flex-1 flex flex-row items-center justify-center gap-2 px-4 py-2 border border-default-border rounded-lg select-none;
}

.option {
  @apply flex-1 body-1 text-wrap line-clamp-2 max-w-32;
}
</style>
