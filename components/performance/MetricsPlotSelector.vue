<script setup lang="ts">
export type MetricsPlotSelectorProps = {
  options: string[];
  value: string[];
  metricsData: Record<string, string | undefined>;
  colors: string[];
  maxItems?: number;
};

const props = withDefaults(defineProps<MetricsPlotSelectorProps>(), {
  maxItems: 8,
});

defineEmits<{
  (e: "selectionChange", metric: string): void;
  (e: "metricRemove", metric: string): void;
}>();

function getMetricColor(metric: string): string | undefined {
  if (!props.value.includes(metric)) return undefined;

  const selectedIndex = props.value.indexOf(metric);
  return props.colors[selectedIndex] || undefined;
}
</script>

<template>
  <div class="root">
    <div class="content">
      <MetricPlotCard
        v-for="metric in options"
        :key="metric"
        :metric-name="metric"
        :value="metricsData[metric]"
        :active-color="getMetricColor(metric)"
        :loading="metricsData[metric] === undefined"
        @click="$emit('selectionChange', metric)"
        @remove="$emit('metricRemove', metric)"
      />

      <div v-for="i in Math.max(0, maxItems - options.length)" :key="i">
        <div class="slot-placeholder"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-1;
}

.content {
  @apply flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
}

.slot-placeholder {
  @apply h-32 rounded-xl border border-default-border border-dashed;
}
</style>
