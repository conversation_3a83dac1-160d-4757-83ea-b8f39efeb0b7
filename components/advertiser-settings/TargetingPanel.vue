<script setup lang="ts">
import { computed } from "vue";
import { KnowledgeType, type AdvertiserKnowledge } from "~/types/knowledge";

const props = defineProps<{
  advertiserKnowledge?: AdvertiserKnowledge[];
}>();

const targetingKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.TARGETING_SETTINGS
  )
);
</script>

<template>
  <div class="flex flex-col gap-6 flex-1">
    <div class="text-black font-inter text-sm font-normal leading-[22px]">
      Using your definitions below, <PERSON><PERSON> will be tailored to your organizations in all aspect throughout the app, from campaign creation to deck generation
    </div>
    <KnowledgeItem
        v-if="targetingKnowledgeItem"
        :item="targetingKnowledgeItem"
        direction="column"
    />
  </div>
</template>