<script setup lang="ts">
const props = defineProps<{
  advertiserKnowledge?: AdvertiserKnowledge[];
}>();

const competitorsAsinsKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.COMPETITORS_ASINS_SETTINGS
  )
);
</script>

<template>
  <KnowledgeItem
    v-if="competitorsAsinsKnowledgeItem"
    :item="competitorsAsinsKnowledgeItem"
    direction="column"
  />
</template>
