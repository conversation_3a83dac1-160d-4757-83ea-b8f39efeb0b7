<script setup lang="ts">
const props = defineProps<{
  advertiser: Advertiser;
}>();

function openWebsite() {
  if (!props.advertiser.shopUrl) return;

  window.open(props.advertiser.shopUrl, "_blank");
}
</script>

<template>
  <div class="flex flex-row items-center justify-start gap-6">
    <FallbackAvatar
      :image="`/images/advertiser/${advertiser.advertiserId}`"
      :label="advertiser.amazonDspAdvertiserName"
      size="xlarge"
      class="rounded-xl"
    />

    <div class="flex flex-1 flex-col gap-2">
      <h2 class="font-medium">{{ advertiser.amazonDspAdvertiserName }}</h2>
      <p class="text-sm text-gray-500">{{ advertiser.advertiserId }}</p>
    </div>

    <div v-if="advertiser.shopUrl" class="flex flex-row items-center gap-4">
      <p class="overflow-hidden text-ellipsis whitespace-nowrap max-w-48">
        {{ advertiser.shopUrl }}{{ advertiser.shopUrl }}{{ advertiser.shopUrl
        }}{{ advertiser.shopUrl }}
      </p>
      <MaterialIcon icon="open_in_new" clickable @click="openWebsite" />
    </div>
  </div>
</template>
