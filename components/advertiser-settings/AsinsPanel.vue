<script setup lang="ts">
const props = defineProps<{
  advertiserKnowledge?: AdvertiserKnowledge[];
}>();

const asinGroupingKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.ASIN_GROUPING_SETTINGS
  )
);
</script>

<template>
  <KnowledgeItem
    v-if="asinGroupingKnowledgeItem"
    :item="asinGroupingKnowledgeItem"
    direction="column"
  />
</template>
