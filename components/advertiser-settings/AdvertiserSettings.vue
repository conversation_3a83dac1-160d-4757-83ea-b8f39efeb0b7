<script setup lang="ts">
import AsinsPanel from "~/components/advertiser-settings/AsinsPanel.vue";
import ObjectivesPanel from "~/components/advertiser-settings/ObjectivesPanel.vue";
import CompetitorsPanel from "~/components/advertiser-settings/CompetitorsPanel.vue";
import BrandSafetyPanel from "~/components/advertiser-settings/BrandSafetyPanel.vue";
import TargetingPanel from "~/components/advertiser-settings/TargetingPanel.vue";

const toast = useToast();

const props = defineProps<{
  advertiser?: Advertiser;
}>();

const settingsTabs = [
  {
    label: "Objectives",
    component: ObjectivesPanel,
  },
  {
    label: "Targeting",
    component: TargetingPanel,
  },
  {
    label: "ASINs",
    component: AsinsPanel,
  },
  {
    label: "Competitors",
    component: CompetitorsPanel,
  },
  {
    label: "Brand Safety",
    component: BrandSafetyPanel,
  },
];

const advertiserKnowledge = ref<AdvertiserKnowledge[]>([]);

onMounted(async () => {
  await fetchAdvertiserKnowledge();
});

async function fetchAdvertiserKnowledge() {
  if (!props.advertiser) return;

  const [newAdvertiserKnowledge, error] =
    await useKnowledge().getAdvertiserKnowledge(props.advertiser?.advertiserId);
  if (error) {
    console.error(error);
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "Failed to fetch advertiser knowledge",
      life: 3000,
    });
  } else {
    advertiserKnowledge.value = newAdvertiserKnowledge;
  }
}
</script>

<template>
  <div v-if="advertiser" class="flex flex-1 flex-col gap-6">
    <AdvertiserSettingsHeader :advertiser="advertiser" />

    <Tabs :value="settingsTabs[0].label">
      <TabList>
        <Tab v-for="tab in settingsTabs" :key="tab.label" :value="tab.label">
          {{ tab.label }}
        </Tab>
      </TabList>
      <TabPanels>
        <TabPanel
          v-for="tab in settingsTabs"
          :key="tab.label"
          :value="tab.label"
        >
          <component
            :is="tab.component"
            :advertiser-knowledge="advertiserKnowledge"
          />
        </TabPanel>
      </TabPanels>
    </Tabs>
  </div>
  <div v-else class="flex flex-1 flex-col gap-6">
    <Message
      severity="warn"
      :closable="false"
      pt:text:class="flex flex-row gap-2 items-center justify-start"
    >
      <MaterialIcon icon="warning" size="1rem" />
      <span>Please select an advertiser to view its settings.</span>
    </Message>
  </div>
</template>
