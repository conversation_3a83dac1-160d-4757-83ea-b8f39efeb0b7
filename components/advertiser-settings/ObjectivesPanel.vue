<script setup lang="ts">
const props = defineProps<{
  advertiserKnowledge?: AdvertiserKnowledge[];
}>();

const currentObjectivesKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.CURRENT_OBJECTIVES_SETTINGS
  )
);

const kpiSettingsKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.KPI_SETTINGS
  )
);

const dspStrategyKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.DSP_STRATEGY_SETTINGS
  )
);


</script>
<template>
  <div class="flex w-full flex-col gap-6">
    <KnowledgeItem
      v-if="currentObjectivesKnowledgeItem"
      :item="currentObjectivesKnowledgeItem"
      direction="column"
    />

    <KnowledgeItem
      v-if="dspStrategyKnowledgeItem"
      :item="dspStrategyKnowledgeItem"
      direction="column"
    />

    <KnowledgeItem
      v-if="kpiSettingsKnowledgeItem"
      :item="kpiSettingsKnowledgeItem"
      direction="column"
    />
  </div>
</template>
