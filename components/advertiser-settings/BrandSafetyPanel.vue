<script setup lang="ts">
import KnowledgeItem from "~/components/knowledge/KnowledgeItem.vue";

const props = defineProps<{
  advertiserKnowledge?: AdvertiserKnowledge[];
}>();

const brandSafetyKnowledgeItem = computed(() =>
  props.advertiserKnowledge?.find(
    (item) => item.knowledgeType === KnowledgeType.BRAND_SAFETY_SETTINGS
  )
);
</script>

<template>
  <div class="flex w-full flex-col gap-4">
    <KnowledgeItem
      v-if="brandSafetyKnowledgeItem"
      :item="brandSafetyKnowledgeItem"
      direction="column"
    />
  </div>
</template>
