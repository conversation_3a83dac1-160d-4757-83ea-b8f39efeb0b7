<script setup lang="ts">
const { user, loggedIn } = useUserSession();
const popover = ref();

const agencyStore = useAgencyStore();

const initials = computed(() => {
  return user.value?.fullName[0];
});

async function logout() {
  await useServerEventsStore().unsubscribeFromAllTopics();
  await useUserSession().clear();
  navigateTo("/login");
}
</script>

<template>
  <Button unstyled @click="popover.toggle($event)" :disabled="!loggedIn">
    <Avatar
      shape="circle"
      :label="initials"
      class="bg-indigo-100 border border-neutral-300"
    >
      <template #icon>
        <MaterialIcon icon="person" size="1.25rem" />
      </template>
    </Avatar>
  </Button>

  <Popover ref="popover" class="w-[20rem] max-w-[88vw]">
    <div class="flex flex-col gap-1 px-3 py-3">
      <div class="flex items-center gap-4">
        <Avatar
          shape="circle"
          size="large"
          class="shrink-0 bg-neutral-100 border border-neutral-300"
        >
          <template #icon>
            <MaterialIcon icon="person" size="100%" />
          </template>
        </Avatar>

        <!-- 💡 This wrapper gets width & truncation styles -->
        <div class="flex flex-col min-w-0">
          <span class="label-4 truncate">{{ user?.fullName }}</span>
          <span class="text-sm text-neutral-500 truncate">
            {{ user?.email }}
          </span>
          <span class="text-sm text-neutral-500 truncate">
            {{ agencyStore.activeAgency?.name }}
          </span>
        </div>
      </div>

      <Divider />

      <!-- <span class="text-sm font-bold">Switch Agency</span> * this is to be replaced with a search bar once we have more of agencies * -->
      <ScrollPanel class="max-h-64 overflow-auto">
        <ul class="flex flex-col gap-1">
          <li v-for="agency in agencyStore.data" :key="agency.id">
            <button
              class="inline-flex items-center px-2 py-1 gap-6 rounded-lg w-full text-left hover:bg-neutral-50"
              :class="{
                'bg-neutral-50 border-neutral-400':
                  agencyStore.activeAgency?.id === agency.id,
              }"
              @click="agencyStore.setActiveAgencyById(agency.id)"
            >
              <FallbackAvatar
                :image="`/images/agency/${agency.id}`"
                :label="agency.name.slice(0, 2)"
                shape="rounded"
                size="normal"
                class="scale-75"
              />
              {{ agency.name }}
              <MaterialIcon
                v-if="agencyStore.activeAgency?.id === agency.id"
                icon="check"
                class="text-primary-50 ml-auto"
              />
            </button>
          </li>
        </ul>
      </ScrollPanel>

      <Divider />

      <Button
        label="Profile"
        outlined
        size="small"
        @click="navigateTo('/settings')"
      >
        <i class="material-symbols-outlined">settings</i>
        Settings
      </Button>
      <Button label="Logout" outlined size="small" @click="logout">
        <i class="material-symbols-outlined">logout</i>
        Logout
      </Button>
    </div>
  </Popover>
</template>
