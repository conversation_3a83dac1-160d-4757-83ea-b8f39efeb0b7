<script setup lang="ts">
defineProps<{
  icon: string;
  label: string;
  path: string;
  active?: boolean;
}>();
</script>

<template>
  <NuxtLink :to="path">
    <div class="flex flex-col items-center justify-center group">
      <i
        class="material-symbols-outlined p-2 !font-light group-hover:bg-tertiary-background rounded-lg"
        :class="{ 'bg-tertiary-background text-brand-primary': active }"
        style="font-size: 24px"
        >{{ icon }}</i
      >
      <span v-if="label" class="label-1 text-primary-text">{{ label }}</span>
    </div>
  </NuxtLink>
</template>
