<script setup lang="ts">
const route = useRoute();

const actions = [
  {
    icon: "edit_square",
    label: "",
    path: "/chat",
  },
];

const navigation = [
  {
    icon: "home",
    label: "Home",
    path: "/",
  },
  {
    icon: "leaderboard",
    label: "Measure",
    path: "/measure",
  },
  {
    icon: "slide_library",
    label: "Decks",
    path: "/decks",
  },
  {
    icon: "content_paste",
    label: "Orders",
    path: "/orders",
  },
];

function isActivePath(path: string): boolean {
  if (path === "/") {
    return route.path === "/";
  }
  return route.path.startsWith(path);
}
</script>

<template>
  <div
    class="flex flex-shrink-0 flex-col gap-8 w-18 h-screen items-center p-4 border-r border-neutral-300 bg-neutral-50"
  >
    <!-- Logo -->
    <img src="/images/gigi-avatar.svg" />

    <div class="flex h-full flex-col gap-4 items-center justify-start px-4">
      <!-- Page Navigation -->
      <NavbarItem
        v-for="navigation in navigation"
        :key="navigation.path"
        :icon="navigation.icon"
        :label="navigation.label"
        :path="navigation.path"
        :active="isActivePath(navigation.path)"
      />

      <span class="w-full h-px bg-primary-border"></span>

      <!-- Actions -->
      <NavbarItem
        v-for="action in actions"
        :key="action.path"
        :icon="action.icon"
        :label="action.label"
        :path="action.path"
        :active="isActivePath(action.path)"
      />
    </div>

    <!-- User -->
    <div class="flex flex-col gap-4 items-center justify-start p-4">
      <Avatar shape="circle" class="bg-transparent border border-neutral-300">
        <template #icon>
          <i class="material-symbols-outlined">help_outline</i>
        </template>
      </Avatar>
      <UserAvatar />
    </div>
  </div>
</template>
