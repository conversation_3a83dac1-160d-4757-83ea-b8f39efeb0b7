<script setup lang="ts">
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import type { ChartOptions, ChartData } from "chart.js";
import { format, parse } from "date-fns";
import twColors from "tailwindcss/colors";
import { MetricFormatter } from "~/utils/performance/formatters"; // Import MetricFormatter
import { ADSP_CAMPAIGN_METRIC_DEFINITIONS } from "~/utils/advertiser-datasets/metrics/constants"; // Import ADSP_CAMPAIGN_METRIC_DEFINITIONS

// Import compact formatter for tick labels
const compactFmt = new Intl.NumberFormat(undefined, { notation: "compact" });

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// Custom plugin for vertical crosshair line
const crosshairPlugin = {
  id: "crosshair",
  beforeDatasetsDraw: (chart: any) => {
    const { ctx, chartArea } = chart;

    if (chart.tooltip?._active?.length) {
      const activePoint = chart.tooltip._active[0];
      const x = activePoint.element.x;
      ctx.save();
      ctx.strokeStyle = "#D4D4D4";
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, chartArea.top);
      ctx.lineTo(x, chartArea.bottom);
      ctx.stroke();
      ctx.restore();
    }
  },
};

const chartAreaBorder = {
  id: "chartAreaBorder",
  beforeDraw(chart: any, args: any, options: any) {
    const {
      ctx,
      chartArea: { left, top, width, height },
    } = chart;
    ctx.save();
    ctx.strokeStyle = twColors.neutral[100];
    ctx.lineWidth = options.borderWidth;
    ctx.setLineDash(options.borderDash || []);
    ctx.lineDashOffset = options.borderDashOffset;
    ctx.strokeRect(left, top, width, height);
    ctx.restore();
  },
};

// Register the crosshair plugin
ChartJS.register(crosshairPlugin, chartAreaBorder);

interface Props {
  labels?: string[];
  datasets?: {
    label: string;
    metricId: string;
    data: (number | null)[];
    borderColor: string;
    backgroundColor?: string;
    yAxisID?: string;
  }[];
  height?: number;
  currencyCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  labels: () => [],
  datasets: () => [],
  currencyCode: undefined,
});

// Create a mapping from yAxisID to metricId
const yAxisMetricMap = computed(() => {
  const map: Record<string, string> = {};
  props.datasets?.forEach((dataset) => {
    // We assign yAxisIDs in generateAxisConfig, so we need to ensure this map is created after or alongside that logic
    // For now, assume the yAxisID is set on the dataset by chartData computed property correctly
    if (dataset.yAxisID && dataset.metricId) {
      map[dataset.yAxisID] = dataset.metricId;
    }
  });
  return map;
});

// Helper function to format dates for tooltip and X-axis
const formatDate = (
  dateString: string,
  mode: "tooltip" | "xAxis" = "tooltip"
) => {
  try {
    const date = parse(dateString, "yyyy-MM-dd", new Date());
    const year = format(date, "yyyy");
    if (mode === "tooltip") {
      const monthDay = format(date, "MMM do");
      return `${monthDay}, ${year}`;
    } else {
      if (date.getFullYear() === new Date().getFullYear()) {
        return format(date, "MMM, dd");
      } else {
        return [format(date, "MMM dd"), year];
      }
    }
  } catch {
    return dateString;
  }
};

// Helper function to calculate axis bounds with padding
const calculateAxisBounds = (
  datasets: Props["datasets"],
  yAxisId: string,
  assignedAxisIds: string[]
) => {
  if (!datasets) return { min: undefined, max: undefined };

  const relevantDatasets = datasets.filter(
    (_, index) => assignedAxisIds[index] === yAxisId
  );

  if (relevantDatasets.length === 0) return { min: undefined, max: undefined };

  let allValues: number[] = [];
  relevantDatasets.forEach((dataset) => {
    dataset.data.forEach((value) => {
      if (value !== undefined && value !== null) {
        allValues.push(value);
      }
    });
  });

  if (allValues.length === 0) return { min: undefined, max: undefined };

  const min = Math.min(...allValues);
  const max = Math.max(...allValues);
  const range = max - min;
  const padding = range * 0.15; // 15% padding above and below

  return {
    min: Math.max(0, min - padding), // Don't go below 0 for positive metrics
    max: max + padding,
  };
};

// Generate axis configuration based on number of datasets
const generateAxisConfig = (
  datasetCount: number,
  datasets: Props["datasets"],
  // Pass the axisIdToMetricIdMap here so it's available in the callback
  axisIdToMetricIdMap: Record<string, string>
) => {
  const yAxisIds = assignYAxisIds(datasetCount);

  // Configure X-axis
  const scales: any = {
    x: {
      type: "category",
      display: true,
      ticks: {
        display: true,
        font: {
          size: 11,
        },
        padding: 8,
        callback: function (
          value: any,
          index: number,
          ticks: any
        ): string | string[] {
          const chartInstance = (this as any).chart;
          const labels = chartInstance?.data?.labels || [];
          const label = labels[index] as string;
          return formatDate(label, "xAxis");
        },
      },
      grid: {
        color: twColors.neutral[100],
      },
    },
  };

  // Helper function to get the primary color for an axis based on its datasets
  const getAxisColor = (axisId: string) => {
    const datasetIndex = yAxisIds.findIndex((id) => id === axisId);
    return datasets?.[datasetIndex]?.borderColor || "#000000";
  };

  // Base Y axis configuration (used as a base, then overridden per axis)
  const baseYAxisConfig = {
    type: "linear",
    display: true,
    beginAtZero: false,
    title: {
      display: false,
    },
    border: {
      display: false,
    },
  };

  // Create Y axes dynamically based on dataset count
  for (let i = 0; i < datasetCount; i++) {
    const axisId = `y${i}`;
    const bounds = calculateAxisBounds(datasets, axisId, yAxisIds);
    const color = getAxisColor(axisId);
    const isRightAxis = i % 2;
    const position = isRightAxis ? "right" : "left";
    const gridConfig = isRightAxis
      ? { drawOnChartArea: false }
      : i === 0
      ? { display: false }
      : {};

    scales[axisId] = {
      ...baseYAxisConfig,
      position,
      min: bounds.min,
      max: bounds.max,
      ticks: {
        font: {
          size: 11,
        },
        padding: 8,
        callback: function (value: any) {
          // Use the passed map to get the metricId for this axis
          const metricId = axisIdToMetricIdMap[axisId];
          if (metricId) {
            return MetricFormatter.formatValue(
              metricId,
              value,
              props.currencyCode,
              true
            );
          }
          return compactFmt.format(value);
        },
        color,
      },
      grid: {
        ...gridConfig,
        tickLength: 0,
        color: twColors.neutral[100],
      },
    };
  }

  return scales;
};

// Assign y-axis IDs to datasets based on count
const assignYAxisIds = (datasetCount: number) => {
  return Array.from({ length: datasetCount }, (_, i) => `y${i}`);
};

const computeMetricsDataLength = (datasets: Props["datasets"]) => {
  if (!datasets || !datasets.length) {
    return 0;
  }

  let minLength = Infinity;
  for (const dataset of datasets) {
    if (dataset.data.length > 0) {
      minLength = Math.min(minLength, dataset.data.length);
    }
  }
  return minLength === Infinity ? 0 : minLength;
};

let metricsDataLength = computed(() =>
  computeMetricsDataLength(props.datasets)
);
console.log("metricsDataLength", metricsDataLength);

const chartData = computed((): ChartData<"line"> => {
  if (metricsDataLength.value <= 1) {
    return { labels: [], datasets: [] };
  }

  const yAxisIds = assignYAxisIds(props.datasets?.length || 0);
  const chartDatasets =
    props.datasets?.map((dataset, index) => {
      return {
        label: dataset.label,
        data: dataset.data,
        borderColor: dataset.borderColor,
        backgroundColor: dataset.backgroundColor || dataset.borderColor,
        pointBackgroundColor: dataset.borderColor,
        pointBorderColor: dataset.borderColor,
        yAxisID: dataset.yAxisID || yAxisIds[index],
        metricId: dataset.metricId, // Ensure metricId is passed through to Chart.js dataset
      };
    }) || [];

  return {
    labels: props.labels || [],
    datasets: chartDatasets,
  };
});

const chartOptions = computed((): ChartOptions<"line"> => {
  // Create the map here, accessible to both generateAxisConfig and tooltip
  const axisIdToMetricIdMap: Record<string, string> = {};
  chartData.value.datasets.forEach((dataset) => {
    if (dataset.yAxisID && dataset.metricId) {
      axisIdToMetricIdMap[dataset.yAxisID] = dataset.metricId;
    }
  });

  return {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: "index",
      intersect: false,
    },
    plugins: {
      title: {
        display: false,
      },
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        external: (context: any) => {
          let tooltipEl = document.getElementById("chartjs-tooltip");
          if (!tooltipEl) {
            tooltipEl = document.createElement("div");
            tooltipEl.id = "chartjs-tooltip";
            tooltipEl.className = "custom-tooltip";

            const canvas = context.chart.canvas;
            const chartContainer =
              canvas.closest(".root") || canvas.parentElement;

            if (chartContainer) {
              chartContainer.appendChild(tooltipEl);
            } else {
              document.body.appendChild(tooltipEl);
            }

            (tooltipEl as any)._chartContext = context;
            (tooltipEl as any)._positionFunction = null;

            const updateTooltipPosition = () => {
              if (tooltipEl && (tooltipEl as any)._positionFunction) {
                (tooltipEl as any)._positionFunction();
              }
            };

            window.addEventListener("scroll", updateTooltipPosition, {
              passive: true,
            });
            document.addEventListener("scroll", updateTooltipPosition, {
              passive: true,
              capture: true,
            });

            (tooltipEl as any)._scrollCleanup = () => {
              window.removeEventListener("scroll", updateTooltipPosition);
              document.removeEventListener("scroll", updateTooltipPosition);
            };
          }

          const tooltipModel = context.tooltip;
          if (tooltipModel.opacity === 0) {
            tooltipEl.style.opacity = "0";
            return;
          }

          if (tooltipModel.body) {
            const titleLines = tooltipModel.title || [];
            const bodyLines = tooltipModel.body.map((b: any) => b.lines);

            let innerHtml = '<div class="tooltip-header">';
            titleLines.forEach((title: string) => {
              innerHtml += `<div class="tooltip-title-date">${formatDate(
                title,
                "tooltip"
              )}</div>`;
            });
            innerHtml += "</div>";

            innerHtml += '<div class="tooltip-body">';
            bodyLines.forEach((body: string[], i: number) => {
              const colors = tooltipModel.labelColors[i];
              const parts = body[0].split(": ");
              const metricLabel = parts[0];
              const rawMetricValue = parts[1] || "";

              // Get yAxisID from the data point, then lookup metricId from the map
              const datasetFromTooltip = tooltipModel.dataPoints[i]?.dataset;
              const yAxisIdFromTooltip = datasetFromTooltip?.yAxisID;
              const metricId = axisIdToMetricIdMap[yAxisIdFromTooltip];

              let formattedMetricValue = rawMetricValue;
              if (metricId) {
                const numericValue = parseFloat(
                  rawMetricValue.replace(/[^0-9.-]/g, "")
                );
                if (!isNaN(numericValue)) {
                  formattedMetricValue = MetricFormatter.formatValue(
                    metricId,
                    numericValue,
                    props.currencyCode
                  );
                }
              }

              innerHtml += `<div class="tooltip-item">
              <div class="tooltip-color" style="background-color: ${colors.backgroundColor}"></div>
              <div class="tooltip-metric-container">
                <span class="tooltip-metric-name">${metricLabel}</span>
                <span class="tooltip-metric-value">${formattedMetricValue}</span>
              </div>
            </div>`;
            });
            innerHtml += "</div>";

            tooltipEl.innerHTML = innerHtml;
          }

          // Create reusable positioning function
          const positionTooltip = () => {
            const { chartArea } = context.chart;
            const canvas = context.chart.canvas;
            const chartContainer =
              canvas.closest(".root") || canvas.parentElement;
            const containerRect = chartContainer
              ? chartContainer.getBoundingClientRect()
              : { left: 0, top: 0 };
            const canvasRect = canvas.getBoundingClientRect();

            const tooltipWidth = tooltipEl.offsetWidth;
            const tooltipHeight = tooltipEl.offsetHeight;

            // Calculate cursor position relative to chart area
            const relativeX = tooltipModel.caretX - chartArea.left;
            const chartAreaWidth = chartArea.right - chartArea.left;
            const chartAreaHeight = chartArea.bottom - chartArea.top;

            // Calculate position relative to the container
            // X follows the data point, Y is centered on the canvas
            let left =
              canvasRect.left - containerRect.left + chartArea.left + relativeX;
            let top =
              canvasRect.top -
              containerRect.top +
              chartArea.top +
              chartAreaHeight / 2;

            const tooltipHalfWidth = tooltipWidth / 2;
            const horizontalMargin = 20;

            // Adjust horizontal positioning to prevent clipping
            if (
              relativeX + tooltipHalfWidth >
              chartAreaWidth - horizontalMargin
            ) {
              // Too close to right edge - position to the left of cursor
              left = left - tooltipWidth - 10;
              tooltipEl.style.transform = "translateY(-50%)";
            } else if (relativeX - tooltipHalfWidth < horizontalMargin) {
              // Too close to left edge - position to the right of cursor
              left = left + 10;
              tooltipEl.style.transform = "translateY(-50%)";
            } else {
              // Centered positioning (default)
              tooltipEl.style.transform = "translateX(-50%) translateY(-50%)";
            }

            tooltipEl.style.opacity = "1";
            tooltipEl.style.position = "absolute";
            tooltipEl.style.left = left + "px";
            tooltipEl.style.top = top + "px";
            tooltipEl.style.pointerEvents = "none";
          };

          // Store the positioning function for scroll event updates
          (tooltipEl as any)._positionFunction = positionTooltip;

          // Position tooltip with smart boundary detection
          positionTooltip();
        },
      },
    },
    scales: generateAxisConfig(
      props.datasets?.length || 0,
      props.datasets,
      axisIdToMetricIdMap // Pass the map to generateAxisConfig
    ),
    elements: {
      line: {
        tension: 0.4, // Cubic interpolation
        fill: false,
        borderWidth: 2,
      },
      point: {
        radius: 3,
        hoverRadius: 5,
        borderWidth: 2,
      },
    },
  };
});

// Cleanup tooltip and event listeners when component is unmounted
onUnmounted(() => {
  const tooltipEl = document.getElementById("chartjs-tooltip");
  if (tooltipEl) {
    // Remove scroll event listeners
    const cleanup = (tooltipEl as any)._scrollCleanup;
    if (cleanup) cleanup();

    // Remove tooltip element
    tooltipEl.remove();
  }
});
</script>

<template>
  <div class="root" :style="{ height: `${height}px` }">
    <Line
      v-if="metricsDataLength.valueOf() > 1"
      :data="chartData"
      :options="chartOptions"
      class="chart-container"
    />
    <div v-else class="no-data-container">
      <div class="icon">
        <MaterialIcon icon="stacked_line_chart" />
      </div>
      <div
        v-if="metricsDataLength.valueOf() === 1"
        class="text-sm font-medium text-gray-700"
      >
        Not enough data points
      </div>
      <div v-else class="label-3 text-gray-700">No data available</div>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply rounded-xl border border-default-border w-full relative;
  z-index: 1;
}

.chart-container,
.no-data-container {
  @apply w-full h-full relative pt-6 pb-6 pr-4 pl-4;
}

.no-data-container {
  @apply flex flex-col items-center justify-center space-y-3;
}

.icon {
  @apply flex flex-row w-12 h-12 bg-primary-background rounded-full items-center justify-center mx-auto;
}
</style>

<style>
.custom-tooltip {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: opacity 0.2s ease;
  min-width: 180px;
}

.tooltip-header {
  margin-bottom: 12px;
}

.tooltip-title-date {
  @apply label-3 text-primary-text;
}

.tooltip-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tooltip-item {
  @apply flex flex-row justify-start gap-2;
}

.tooltip-color {
  @apply w-3 h-3 rounded-[0.25rem] mt-0.5;
}

.tooltip-metric-container {
  @apply flex flex-col;
}

.tooltip-metric-name {
  @apply caption-2 text-disabled-text;
}

.tooltip-metric-value {
  @apply label-3 text-primary-text;
  margin-left: 0;
}
</style>
