<script setup lang="ts" generic="T extends DeliveryItem">
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import type { DeliveryConfig } from "~/types/delivery-item/config";
import { approvedAcceptedText } from "~/utils/deliveryItemUtils";

const props = defineProps<{
  item: T;
  typeConfig: DeliveryConfig["types"][T["type"]];
  actioned: boolean;
  primaryCount?: number;
  secondaryCount?: number;
  showActions?: boolean;
  isDrawer?: boolean;
}>();

defineEmits<{
  (e: "expand"): void;
}>();

const confirm = useConfirm();
const toast = useToast();

const { user } = useUserSession();
const isLoading = ref(false);

onMounted(() => {
  validateItemActionSuggestions(props.item, props.typeConfig);
});

const primaryActionConfig = computed(() => {
  return props.typeConfig.actionSuggestions[0];
});

const secondaryActionConfig = computed(() => {
  return props.typeConfig.actionSuggestions[1];
});

const primaryAction = computed(() => {
  return getActionByActionType(
    props.item,
    primaryActionConfig.value?.actionType
  );
});

const secondaryAction = computed(() => {
  return getActionByActionType(
    props.item,
    secondaryActionConfig.value?.actionType
  );
});

const acceptedText = computed(() => {
  const actionBy = props.item.actionBy;
  const actionAt = props.item.actionAt;

  if (!actionBy || !actionAt) return "";

  return (
    props.typeConfig.acceptedTextCreator?.(actionBy, actionAt) ||
    approvedAcceptedText(actionBy, actionAt)
  );
});

function executeAction(
  event: Event,
  actionConfig: typeof primaryActionConfig.value
) {
  const cb = async () => {
    if (!user.value) return;

    isLoading.value = true;

    const actionBy = user.value.fullName.split(" ")[0] || "unknown";
    const actionAt = new Date();

    try {
      await actionConfig.onClick?.(props.item);
      await useRob().putDeliveryItem({
        ...props.item,
        actionBy,
        actionAt,
      });
    } catch (error) {
      console.error(error);
      return;
    } finally {
      isLoading.value = false;
    }

    props.item.hasExecutionError = true;
    props.item.actionBy = actionBy;
    props.item.actionAt = actionAt;
    props.item.updatedAt = actionAt;
  };

  confirm.require({
    target: event.currentTarget as HTMLElement,
    header: "Confirm Action",
    message: "Are you sure you want to perform this action?",
    acceptLabel: "Execute",
    rejectLabel: "Cancel",
    accept: tryWithToast(cb, undefined, "error", "Error", toast).execute,
  });
}
</script>

<template>
  <div v-if="actioned" class="flex items-center gap-4">
    <MaterialIcon
      :icon="item.hasExecutionError ? 'report' : 'check'"
      :class="item.hasExecutionError ? 'text-red-500' : 'text-green-500'"
    />
    <span class="accepted-text">
      {{ acceptedText }}
    </span>
  </div>

  <div v-else-if="!item.isRelevant">
    <span class="flex items-center gap-4 text-sm">
      <MaterialIcon icon="block" size="1rem" class="text-disabled-text" />
      <span class="accepted-text"> Data is no longer relevant </span>
    </span>
  </div>

  <div v-else class="flex items-center gap-2">
    <button
      v-if="primaryActionConfig && primaryAction"
      class="footer-action-button footer-action-button-primary"
      :disabled="
        !primaryActionConfig.onClick || isLoading || primaryCount === 0
      "
      @click="executeAction($event, primaryActionConfig)"
    >
      <MaterialIcon
        :icon="isLoading ? 'refresh' : primaryActionConfig.icon"
        size="1rem"
        :class="{ 'animate-spin': isLoading }"
      />
      <span class="font-inter text-sm font-semibold leading-[22px] text-primary-text text-center">
        {{ primaryActionConfig.label }}
      </span>
      <span
        v-if="typeof primaryCount === 'number'"
        class="footer-action-button-counter"
      >
        {{ primaryCount }}
      </span>
    </button>

    <button
      v-if="secondaryActionConfig && secondaryAction"
      class="footer-action-button footer-action-button-secondary"
      :disabled="
        !secondaryActionConfig.onClick || isLoading || secondaryCount === 0
      "
      @click="executeAction($event, secondaryActionConfig)"
    >
      <MaterialIcon
        :icon="isLoading ? 'refresh' : secondaryActionConfig.icon"
        size="1rem"
        :class="{ 'animate-spin': isLoading }"
      />
      <span class="font-inter text-sm font-semibold leading-[22px] text-primary-text text-center">
        {{ secondaryActionConfig.label }}
      </span>
      <span v-if="secondaryCount" class="footer-action-button-counter">
        {{ secondaryCount }}
      </span>
    </button>
  </div>

  <div v-if="showActions" class="flex items-center gap-2">
    <MaterialIcon
      v-if="typeConfig.allowReschedule && !item.isRelevant && !actioned"
      icon="watch_later"
      class="footer-icon-button"
    />
    <MaterialIcon
      v-if="typeConfig.allowExpand"
      icon="zoom_out_map"
      class="footer-icon-button"
      clickable
      @click="$emit('expand')"
    />
  </div>
</template>

<style scoped>
.footer-icon-button {
  @apply p-2 bg-neutral-50 rounded-lg;
}

.footer-action-button {
  @apply flex justify-center items-center h-8 px-4 py-2 gap-2 rounded-lg border border-default-border bg-white transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed;
}
.footer-action-button-primary {
  @apply border border-default-border bg-white text-primary-text;
}
.footer-action-button-secondary {
  @apply bg-neutral-100 text-brand-primary border border-neutral-300 hover:bg-neutral-200 active:bg-neutral-300;
}
.footer-action-button-counter {
  @apply flex w-4 h-4 p-2 justify-center items-center gap-2 rounded-full bg-tertiary-background text-primary-text text-xs font-semibold;
}

.accepted-text {
  @apply inline-flex items-center gap-1 text-sm text-primary-500;
}
</style>
