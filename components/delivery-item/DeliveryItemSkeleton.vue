<template>
  <Card
    pt:root:class="rounded-2xl shadow overflow-clip"
    pt:body:class="p-0 gap-0"
    pt:header:class="flex items-center px-6 py-3 bg-neutral-100"
    pt:content:class="flex flex-col gap-4 p-4"
    pt:footer:class="flex items-center gap-4 p-4"
  >
    <template #header>
      <Skeleton width="40%" height="1.375rem" />
    </template>

    <template #content>
      <Skeleton v-for="i in [33, 50, 33]" :key="i" :width="`${i}%`" />
    </template>

    <template #footer>
      <Skeleton v-for="i in [20, 20]" :key="i" :width="`${i}%`" />
    </template>
  </Card>
</template>
