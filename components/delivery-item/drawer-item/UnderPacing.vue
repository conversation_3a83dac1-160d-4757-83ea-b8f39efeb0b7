<script setup lang="ts">
import { computed, ref } from 'vue';
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import type { UnderPacingSuggestion } from "~/types/delivery-item/suggestion/under-pacing";
import { QuickActionSchema } from "~/types/amazon/guidance";
import type { Selectable } from "~/types/delivery-item/suggestion/selectable";
import type { CounterEmit } from "~/types/counterEmit";
import { useCounterEmit } from "~/composables/useCounterEmit";
import { formatCurrency } from "~/utils/currencyUtils";
import DataTable from "primevue/datatable";
import Column from "primevue/column";

const actionTypeEnum = QuickActionSchema.shape.actionType.unwrap().enum;

const guidanceActionTypeToHeaderMap = {
  [actionTypeEnum.AUDIENCEREMOVAL]: "Audience Removal",
  [actionTypeEnum.AUDIENCEREPLACEMENT]: "Audience Replacement",
  [actionTypeEnum.HIBOUENROLLMENT]: "Hibou Enrollment",
  [actionTypeEnum.VIEWABILITYUPDATE]: "Viewability",
  [actionTypeEnum.BASEBIDUPDATE]: "Base Bid",
  [actionTypeEnum.MOVEBUDGET]: "Move Budget",
  [actionTypeEnum.ORDERBASEBIDUPDATE]: "Order Base Bid",
  [actionTypeEnum.ORDERVIEWABILITYUPDATE]: "Order Viewability",
  [actionTypeEnum.MOVEBUDGETBETA]: "Move Budget",
  [actionTypeEnum.ORDERMAXBIDUPDATE]: "Order Max Bid",
  [actionTypeEnum.MAXBIDUPDATE]: "Max avg. CPM",
  [actionTypeEnum.FREQUENCYCAPUPDATE]: "Frequency Cap",
};

const props = defineProps<{
  item: DeliveryItem;
}>();

const emit = defineEmits<CounterEmit>();

const suggestion = computed<UnderPacingSuggestion | undefined>(() => {
  return props.item.actionSuggestions?.[0] as UnderPacingSuggestion | undefined;
});



interface Change {
  description: string;
  currentValue?: string;
  newValue?: string;
  origin: Selectable;
}

interface LineItem {
  id: string;
  title: string;
  underpacing: string;
  changes: Change[];
}

interface Campaign {
  id: string;
  name: string;
  lineItems: LineItem[];
}

// Computed property to build campaigns data based on suggestion
const campaigns = computed(() => {
  if (!suggestion.value?.payload) {
    return [];
  }

  const campaignMap = new Map<string, Campaign>();

  // Helper function to get or create campaign by ID
  const getOrCreateCampaign = (campaignId: string, campaignName: string) => {
    if (!campaignMap.has(campaignId)) {
      campaignMap.set(campaignId, {
        id: campaignId,
        name: campaignName,
        lineItems: [],
      });
    }
    return campaignMap.get(campaignId)!;
  };

  // Helper function to get or create line item by ID
  const getOrCreateLineItem = (campaign: Campaign, lineItemId: string, lineItemName: string, deliveryRate?: number) => {
    let lineItem = campaign.lineItems.find(li => li.id === lineItemId);

    if (!lineItem) {
      const underpacingRate = deliveryRate !== undefined
        ? ((deliveryRate) * 100).toFixed(1) + '%'
        : "N/A";

      lineItem = {
        id: lineItemId,
        title: lineItemName,
        underpacing: underpacingRate,
        changes: [],
      };
      campaign.lineItems.push(lineItem);
    } else if (deliveryRate !== undefined && lineItem.underpacing === "N/A") {
      // Update underpacing rate if we have delivery rate data and it wasn't set before
      lineItem.underpacing = ((deliveryRate) * 100).toFixed(1) + '%';
    }

    return lineItem;
  };

  // First, create a map to store adGroupsMinimumSpend data by adGroupId
  const adGroupsMinimumSpendMap = new Map<string, any>();
  suggestion.value.payload.adGroupsMinimumSpend?.forEach((adGroup) => {
    if (adGroup.adGroupId) {
      adGroupsMinimumSpendMap.set(adGroup.adGroupId, adGroup);
    }
  });

  // Process adGroupsMinimumSpend that have orderId - these provide minimum spend changes AND delivery rates
  suggestion.value.payload.adGroupsMinimumSpend?.forEach((adGroup) => {
    if (!adGroup.orderId) {
      // Skip here, will be processed when guidance is processed
      return;
    }

    const campaign = getOrCreateCampaign(adGroup.orderId, adGroup.orderName || 'Unknown Campaign');
    const lineItem = getOrCreateLineItem(campaign, adGroup.adGroupId || '', adGroup.adGroupName || 'Unknown Line Item', adGroup.deliveryRate);

    // Add minimum spend change
    lineItem.changes.push({
      description: "Daily minimum spend",
      currentValue: formatCurrency(adGroup.currentMinimumSpend || 0, adGroup.currencyCode || 'USD'),
      newValue: formatCurrency(adGroup.recommendedMinimumSpend || 0, adGroup.currencyCode || 'USD'),
      origin: adGroup,
    });
  });

  // Process guidance recommendations - these provide bid changes and other optimizations
  const recommendations = suggestion.value.payload.guidance
    ?.flatMap((guidance) => guidance.recommendations)
    .filter((recommendation) => recommendation?.lineItem?.id && recommendation?.order?.id);

  recommendations?.forEach((recommendation) => {
    if (!recommendation) return;
    
    const campaignId = recommendation.order!.id!;
    const campaignName = recommendation.order!.name || 'Unknown Campaign';
    const lineItemId = recommendation.lineItem!.id!;
    const lineItemName = recommendation.lineItem!.name || 'Unknown Line Item';

    const campaign = getOrCreateCampaign(campaignId, campaignName);

    // Try to find matching adGroup for delivery rate by adGroupId only
    const matchingAdGroup = adGroupsMinimumSpendMap.get(lineItemId);

    const lineItem = getOrCreateLineItem(campaign, lineItemId, lineItemName, matchingAdGroup?.deliveryRate);

    // If we found a matching adGroup with minimum spend data, add it
    if (matchingAdGroup && !matchingAdGroup.orderId) {
      lineItem.changes.push({
        description: "Daily minimum spend",
        currentValue: formatCurrency(matchingAdGroup.currentMinimumSpend || 0, matchingAdGroup.currencyCode || 'USD'),
        newValue: formatCurrency(matchingAdGroup.recommendedMinimumSpend || 0, matchingAdGroup.currencyCode || 'USD'),
        origin: matchingAdGroup,
      });
    }

    // Add all quick actions as changes
    recommendation.quickactionsData?.currentActions
      ?.filter((action) => action.actionType)
      ?.forEach((action) => {
        lineItem.changes.push({
          description: guidanceActionTypeToHeaderMap[action.actionType!] || action.actionType!,
          currentValue: action.currentValue || '',
          newValue: action.recommendedValue || '',
          origin: action,
        });
      });
  });

  const result = Array.from(campaignMap.values());
  
  return result;
});

// Computed function to calculate selectAll state for a lineItem
const getSelectAllState = (lineItem: LineItem): boolean => {
  return lineItem.changes.length > 0 && lineItem.changes.every(change => change.origin.selected);
};



const selectedCount = computed(() => {
  return campaigns.value.reduce((total, campaign) => {
    return total + campaign.lineItems.reduce((lineItemTotal, lineItem) => {
      return lineItemTotal + lineItem.changes.filter(change => change.origin.selected).length;
    }, 0);
  }, 0);
});

useCounterEmit(emit, selectedCount);

// Get order underpacing rate (same for all line items in the campaign)
const getOrderUnderpacingRate = (campaign: Campaign): string => {
  if (campaign.lineItems.length === 0) return "N/A";
  
  // Find the first line item with a valid underpacing rate
  const firstValidRate = campaign.lineItems.find(lineItem => lineItem.underpacing !== "N/A");
  
  return firstValidRate ? firstValidRate.underpacing : "N/A";
};

// Handle selectAll changes to update individual changes
const handleSelectAllChange = (lineItem: LineItem, newValue: boolean) => {
  lineItem.changes.forEach((change) => {
    change.origin.selected = newValue;
  });
};

// Handle individual change selection
const handleChangeSelection = (lineItem: LineItem, change: Change, newValue: boolean) => {
  change.origin.selected = newValue;
  // selectAll state is computed automatically via getSelectAllState
};
</script>

<template>
  <div>
    <!-- Campaigns List (All Parallel) -->
    <div class="flex flex-col gap-6">
      <div v-for="(campaign, campaignIndex) in campaigns" :key="campaignIndex" class="flex flex-col gap-4">

        <!-- Campaign Header -->
        <div class="flex items-center gap-3">
          <h1 class="header-1 leading-[28px] text-primary-text">{{ campaign.name }}</h1>
        </div>

        <!-- Order Underpacing Info -->
        <div class="flex items-center gap-2 text-xs text-disabled-text">
          <span>Order underpacing by</span>
          <span class="text-sm font-semibold font-inter leading-[22px] tracking-normal align-middle text-primary-text">{{ getOrderUnderpacingRate(campaign) }}</span>
        </div>

        <!-- Line Items for this Campaign -->
        <div v-for="(lineItem, lineItemIndex) in campaign.lineItems" :key="lineItemIndex" class="flex flex-col gap-4">

          <!-- Line Item Header -->
          <div class="line-item-header">
            <h2 class="text-base font-medium font-literata leading-6 align-middle text-primary-text">{{ lineItem.title }}</h2>
          </div>

          <!-- Changes Table for this Line Item -->
          <DataTable :value="lineItem.changes" class="changes-table" :paginator="false" :pt="{
            root: { class: 'p-0' },
            wrapper: { class: 'p-0' },
            table: { class: 'w-full' },
            tableContainer: { class: 'p-0' },
            thead: { class: 'bg-white' },
            tbody: { class: 'bg-white' },
            headerRow: { class: 'border-b border-neutral-300' },
            bodyRow: { class: 'last:border-b-0' },
            headerCell: { class: 'flex flex-col justify-center items-start py-4 pr-4 pl-0 h-[51px] border-b-0' },
            bodyCell: { class: 'py-4 px-0 border-b border-neutral-200' }
          }">
            <Column header="" :style="{ minWidth: '280px', width: '40%' }">
              <template #header>
                <div class="flex items-center p-0 gap-2 h-[17px]">
                  <Checkbox :id="`select-all-${campaignIndex}-${lineItemIndex}`" 
                    :model-value="getSelectAllState(lineItem)" 
                    @update:model-value="(value) => handleSelectAllChange(lineItem, value)" 
                    binary size="small" style="
                      --p-checkbox-checked-background: var(--p-slate-800);
                      --p-checkbox-border-color: var(--p-neutral-300);
                    " />
                  <label :for="`select-all-${campaignIndex}-${lineItemIndex}`" class="flex items-center font-inter font-semibold text-sm leading-[17px] text-neutral-900">
                    Change
                  </label>
                </div>
              </template>
              <template #body="{ data: change, index: changeIndex }">
                <div class="flex items-center gap-3">
                  <Checkbox :id="`change-${campaignIndex}-${lineItemIndex}-${changeIndex}`" 
                    :model-value="change.origin.selected"
                    @update:model-value="(value) => handleChangeSelection(lineItem, change, value)"
                    binary size="small" style="
                      --p-checkbox-checked-background: var(--p-slate-800);
                      --p-checkbox-border-color: var(--p-neutral-300);
                    " />
                  <label :for="`change-${campaignIndex}-${lineItemIndex}-${changeIndex}`"
                    class="body-1 leading-[22px] tracking-normal align-middle">
                    {{ change.description }}
                  </label>
                </div>
              </template>
            </Column>
            <Column field="currentValue" :style="{ minWidth: '120px', width: '30%' }">
              <template #header>
                <span class="flex items-center font-inter font-semibold text-sm leading-[17px] text-neutral-900">Current Value</span>
              </template>
              <template #body="{ data: change }">
                <span class="body-1 leading-[22px] tracking-normal align-middle">{{ 
                  !change.currentValue || 
                  change.currentValue === 0 || 
                  change.currentValue === '$0.00' || 
                  change.currentValue === '0' || 
                  change.currentValue === '0.00' 
                    ? "None" 
                    : change.currentValue 
                }}</span>
              </template>
            </Column>
            <Column field="newValue" :style="{ minWidth: '140px', width: '30%' }">
              <template #header>
                <div class="w-full flex justify-end pr-4">
                  <span class="font-inter font-semibold text-sm leading-[17px] text-neutral-900">New Value</span>
                </div>
              </template>
              <template #body="{ data: change }">
                <div class="flex justify-end items-center pr-4">
                  <span class="body-1 leading-[22px] tracking-normal align-middle font-semibold">{{ change.newValue || "None" }}</span>
                </div>
              </template>
            </Column>
          </DataTable>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.campaign-header {
  @apply pb-2 border-b border-neutral-200;
}

.line-item-header {
  @apply flex flex-col gap-4 py-0;
}

.changes-table {
  @apply w-full rounded-lg overflow-hidden;
}

:deep(.changes-table) {
  @apply p-0 m-0;
}

:deep(.changes-table .p-datatable-table) {
  @apply p-0 m-0;
}
:deep(.changes-table .p-datatable-header-cell:first-child),
:deep(.changes-table .p-datatable-header-cell:nth-child(2)) {
  padding-left: 0 !important;
  padding-right: 16px !important;
}

:deep(.changes-table .p-datatable-header-cell:nth-child(3)) {
  padding-left: 0 !important;
  padding-right: 16px !important;
}

:deep(.changes-table .p-datatable-header-cell:nth-child(3) > div) {
  align-items: flex-end !important;
  justify-content: flex-end !important;
}

:deep(.changes-table .p-datatable-tbody > tr > td:first-child),
:deep(.changes-table .p-datatable-tbody > tr > td:nth-child(2)) {
  padding-left: 0 !important;
  padding-right: 16px !important;
}

:deep(.changes-table .p-datatable-tbody > tr > td:nth-child(3)) {
  padding-left: 0 !important;
  padding-right: 16px !important;
}


</style>

