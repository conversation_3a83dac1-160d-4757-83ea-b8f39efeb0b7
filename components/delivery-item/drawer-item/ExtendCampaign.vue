<script setup lang="ts">
import { computed } from "vue";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import type { Flight } from "~/types/amazon/flight";
import { ExtendCampaignSuggestionSchema } from "~/types/delivery-item/suggestion/extend-campaign";

const props = defineProps<{
  item: DeliveryItem;
}>();

const suggestedFlights = computed(() => {
  const payload = getSuggestionPayload(
    props.item,
    ExtendCampaignSuggestionSchema
  );

  return payload?.changes ?? [];
});

function createNewFlight() {
  return {
    startDateTime: null,
    endDateTime: null,
    budgetAmount: undefined,
    currencyCode: "USD",
  };
}

function areFlightDatesValid(flight: Flight): boolean | null {
  if (!flight.startDateTime || !flight.endDateTime) {
    return null;
  }
  return flight.startDateTime.getTime() < flight.endDateTime.getTime();
}

function doesFlightOverlap(
  targetFlight: Flight,
  allFlights: Flight[]
): boolean {
  if (!areFlightDatesValid(targetFlight)) {
    return false;
  }

  const targetStart = targetFlight.startDateTime!.getTime();
  const targetEnd = targetFlight.endDateTime!.getTime();

  for (const otherFlight of allFlights) {
    if (otherFlight === targetFlight) {
      continue;
    }

    if (!areFlightDatesValid(otherFlight)) {
      continue;
    }

    const otherStart = otherFlight.startDateTime!.getTime();
    const otherEnd = otherFlight.endDateTime!.getTime();

    if (targetStart < otherEnd && targetEnd > otherStart) {
      return true;
    }
  }

  return false;
}
</script>

<template>
  <ul class="flex flex-col gap-4">
    <li
      v-for="campaignUpdate in suggestedFlights"
      :key="campaignUpdate.campaignId"
      class="flex flex-col gap-4"
    >
      <span class="text-2xs font-medium text-disabled-text font-sans uppercase">
        {{ campaignUpdate.campaignName }}
      </span>

      <ul class="flex flex-col gap-4">
        <li
          v-for="(flight, i) in campaignUpdate.flights"
          :key="i"
          class="flex flex-col gap-2 rounded-xl border border-neutral-300 px-4 py-3 self-stretch select-none"
        >
          <div class="flex flex-row justify-between items-center">
            <span
              class="text-2xs font-inter text-neutral-700 bg-neutral-100 py-[0.125rem] px-1 rounded-md w-fit"
            >
              {{ `Flight #${i + 1}` }}
            </span>

            <MaterialIcon
              icon="close"
              class="text-neutral-700"
              size="1rem"
              clickable
              @click="campaignUpdate.flights.splice(i, 1)"
            />
          </div>

          <span
            v-if="areFlightDatesValid(flight) == false"
            class="text-xs text-red-500"
          >
            End date must be after start date.
          </span>
          <span
            v-if="doesFlightOverlap(flight, campaignUpdate.flights)"
            class="text-xs text-red-500"
          >
            This flight overlaps with another flight.
          </span>

          <div class="flex flex-row gap-4">
            <div class="flex flex-col gap-2">
              <span class="text-sm font-semibold">Start Date</span>

              <DatePicker
                v-model="flight.startDateTime"
                :manual-input="false"
                show-time
                size="small"
                :min-date="new Date()"
                hour-format="12"
                class="text-sm font-semibold"
                date-format="yy-mm-dd"
              />
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-sm font-semibold">End Date</span>

              <DatePicker
                v-model="flight.endDateTime"
                :manual-input="false"
                show-time
                size="small"
                :min-date="flight.startDateTime || undefined"
                hour-format="12"
                class="text-sm font-semibold"
                date-format="yy-mm-dd"
              />
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-sm font-semibold">Budget</span>

              <InputNumber
                v-model="flight.budgetAmount"
                class="text-sm font-semibold budget-input"
                :min="1"
                size="small"
                mode="currency"
                :currency="flight.currencyCode || 'USD'"
              />
            </div>
          </div>
        </li>
      </ul>

      <button
        class="text-xs font-semibold text-primary-text self-start border border-primary-border rounded-md px-2 py-1 bg-secondary-background"
        @click="campaignUpdate.flights.push(createNewFlight() as any)"
      >
        Add Flight
      </button>
    </li>
  </ul>
</template>

<style scoped>
.budget-input:deep(.p-inputtext) {
  @apply w-1/4;
}
</style>
