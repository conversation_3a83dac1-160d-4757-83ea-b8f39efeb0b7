<script setup lang="ts">
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import type { AdGroup } from "~/types/amazon/ad-group";
import { BidChangeSuggestionSchema } from "~/types/delivery-item/suggestion/bid-change";
import type { CounterEmit } from "~/types/counterEmit";

const props = defineProps<{
  item: DeliveryItem;
}>();

const emit = defineEmits<CounterEmit>();

const bidChanges = computed(() => {
  const payload = getSuggestionPayload(props.item, BidChangeSuggestionSchema);
  return payload?.changes ?? [];
});

const selectedCount = computed(() => {
  return bidChanges.value.reduce((acc, change) => {
    return acc + change.adGroups.filter((adGroup) => adGroup.selected).length;
  }, 0);
});

const advertiserStore = useAdvertiserStore();
const advertiserCurrencyCode = computed(() => {
  return advertiserStore.getAdvertiserById(props.item.ownerId)?.currencyCode;
});

function getCurrencyCode(adGroup: AdGroup) {
  return adGroup.bid?.currencyCode ?? advertiserCurrencyCode.value;
}

useCounterEmit(emit, selectedCount);
</script>

<template>
  <ul class="flex flex-col gap-4">
    <li
      v-for="change in bidChanges"
      :key="change.campaignName"
      class="flex flex-col gap-4"
    >
      <span class="text-2xs font-medium text-disabled-text font-sans uppercase">
        {{ change.campaignName }}
      </span>

      <ul class="flex flex-col gap-4">
        <li
          v-for="adGroup in change.adGroups"
          :key="adGroup.current.adGroupId"
          :data-selected="adGroup.selected"
          class="ad-group-item"
        >
          <Checkbox v-model="adGroup.selected" binary size="small" />

          <div class="flex flex-col gap-2 self-stretch">
            <span
              class="text-2xs font-inter text-neutral-700 w-fit bg-neutral-100 py-[0.125rem] px-1 rounded-md"
            >
              {{ adGroup.current.name }}
            </span>

            <div class="flex flex-row justify-start gap-10">
              <ValueChange
                label="Base Bid"
                :current="
                  formatCurrency(
                    adGroup.current.bid?.baseBid,
                    getCurrencyCode(adGroup.current)
                  )
                "
                :next="
                  formatCurrency(
                    adGroup.next.bid?.baseBid,
                    getCurrencyCode(adGroup.next)
                  )
                "
              />

              <ValueChange
                label="Max Bid"
                :current="
                  formatCurrency(
                    adGroup.current.bid?.maxAverageCPM,
                    getCurrencyCode(adGroup.current)
                  )
                "
                :next="
                  formatCurrency(
                    adGroup.next.bid?.maxAverageCPM,
                    getCurrencyCode(adGroup.next)
                  )
                "
              />

              <div class="flex flex-col items-center justify-center min-w-[80px]">
                <span class="text-2xs font-inter text-neutral-700 w-fit bg-neutral-100 py-[0.125rem] px-1 rounded-md text-center">
                  Current CPM
                </span>
                <span class="text-sm font-semibold text-neutral-900 text-center mt-1">
                  {{ formatCurrency(adGroup.cpm, getCurrencyCode(adGroup.current)) }}
                </span>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</template>

<style scoped>
.ad-group-item {
  @apply flex flex-row gap-3 rounded-xl border border-default-border px-4 py-3 self-stretch bg-transparent;
}

.ad-group-item[data-selected="true"] {
  @apply bg-secondary-background;
}
</style>
