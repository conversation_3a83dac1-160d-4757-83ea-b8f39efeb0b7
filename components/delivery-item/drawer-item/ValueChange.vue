<script setup lang="ts">
const props = defineProps<{
  label: string;
  current?: string;
  next?: string;
  editable?: boolean;
}>();

const isEditing = ref(false);

const showInput = computed(() => {
  return props.editable && (isEditing.value || !props.next);
});
</script>

<template>
  <div class="flex flex-col items-start gap-2">
    <span class="text-xs text-primary-text font-normal">{{ label }}</span>

    <div v-if="current !== next" class="flex flex-row gap-2 items-center group">
      <span class="text-sm font-semibold">
        {{ current || "None" }}
      </span>

      <MaterialIcon icon="arrow_forward" size="1rem" />

      <div v-if="!showInput" class="flex flex-row items-center gap-2">
        <span class="text-sm font-semibold">{{ next || "None" }}</span>

        <MaterialIcon
          v-if="editable"
          class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          icon="edit"
          size="1rem"
          clickable
          @click="isEditing = true"
        />
      </div>

      <div v-if="showInput" class="flex flex-row items-center gap-2">
        <div class="flex-1 min-w-0">
          <slot name="input" :is-editing="isEditing" />
        </div>

        <MaterialIcon
          icon="check"
          size="1rem"
          clickable
          class="flex-shrink-0"
          @click="isEditing = false"
        />
      </div>
    </div>

    <div v-else class="flex flex-row gap-2">
      <span class="text-sm font-semibold">No change</span>
    </div>
  </div>
</template>
