<script setup lang="ts">
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import { AudienceSwapSuggestionSchema } from "~/types/delivery-item/suggestion/audience-swap";
import type { CounterEmit } from "~/types/counterEmit";

const props = defineProps<{
  item: DeliveryItem;
}>();

const emit = defineEmits<CounterEmit>();

const changes = computed(() => {
  const payload = getSuggestionPayload(
    props.item,
    AudienceSwapSuggestionSchema
  );

  return payload?.changes ?? [];
});

const selectedCount = computed(() => {
  return changes.value.reduce((acc, change) => {
    return acc + change.changes.filter((change) => change.selected).length;
  }, 0);
});

useCounterEmit(emit, selectedCount);
</script>

<template>
  <ul class="flex flex-col gap-4">
    <li
      v-for="change in changes"
      :key="change.campaignName"
      class="flex flex-col gap-4"
    >
      <span class="text-2xs font-medium text-disabled-text font-sans uppercase">
        {{ change.campaignName }}
      </span>

      <ul class="flex flex-col gap-4">
        <li
          v-for="swapChange in change.changes"
          :key="swapChange.adGroupId"
          :data-selected="swapChange.selected"
          class="ad-group-item"
        >
          <Checkbox
            v-model="swapChange.selected"
            binary
            size="small"
            style="
              --p-checkbox-checked-background: var(--p-slate-800);
              --p-checkbox-border-color: var(--p-neutral-300);
            "
          />

          <div class="flex flex-col gap-2 self-stretch flex-1">
            <span
              class="text-2xs font-inter text-neutral-700 w-fit bg-neutral-100 py-[0.125rem] px-1 rounded-md"
            >
              {{ swapChange.adGroupName }}
            </span>

            <div class="flex flex-row justify-between gap-10">
              <ValueChange
                label="Audience"
                :current="swapChange.fromAudienceName"
                :next="swapChange.toAudienceName"
              />
              <ValueChange
                label="Affinity Score"
                :current="swapChange.fromScore.toFixed(2)"
                :next="swapChange.toScore.toFixed(2)"
              />
            </div>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</template>

<style scoped>
.ad-group-item {
  @apply flex flex-row gap-3 rounded-xl border border-neutral-300 px-4 py-3 self-stretch;
}

.ad-group-item[data-selected="true"] {
  @apply border-primary-500;
}
</style>
