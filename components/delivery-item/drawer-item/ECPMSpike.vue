<script setup lang="ts">
import { computed, ref, reactive, onMounted, watch } from "vue";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import type { CounterEmit } from "~/types/counterEmit";
import Markdown from "~/components/common/Markdown.vue";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import Checkbox from "primevue/checkbox";
import { useCounterEmit } from "~/composables/useCounterEmit";

const props = defineProps<{
  item: DeliveryItem;
}>();

const emit = defineEmits<CounterEmit>();

const selectedRecommendations = ref<Set<string>>(new Set());
const expandedReasoning = reactive<Record<string, boolean>>({});

const parsedFullMessage = computed(() => {
  try {
    if (typeof props.item.fullMessage === "string") {
      return JSON.parse(props.item.fullMessage || "{}");
    }
    return props.item.fullMessage || {};
  } catch {
    return {};
  }
});

const description = computed(() => parsedFullMessage.value.description || "");

const groupedTableData = computed(() => {
  const groups: Record<string, Array<any>> = {};
  let globalIndex = 0;
  (props.item.actionSuggestions || []).forEach((suggestion) => {
    const payload = suggestion.payload as any;
    if (!payload) return;
    const orderName = payload.order_name || "Untitled Order";
    const splikeLineItems = payload.splike_lineItems || [];
    splikeLineItems.forEach((lineItem: any) => {
      const lineItemName = lineItem.line_item_name || "N/A";
      const reason = lineItem.reason || "N/A";
      const prevEcpm = lineItem.prev_ecpm;
      const currentEcpm = lineItem.current_ecpm;
      const increasePct = lineItem.increase_pct;
      const pctNum =
        typeof increasePct === "number"
          ? (increasePct * 100).toFixed(2)
          : "0.00";
      const pctSign = increasePct > 0 ? "+" : "";
      const pill = `$${prevEcpm} → $${currentEcpm} (${pctSign}${pctNum}%)`;
      const updatePayload = lineItem.recommendation?.update_payload;
      const uniqueId = `${orderName}__${lineItem.line_item_id}__${globalIndex}`;
      if (!updatePayload || updatePayload.length === 0) {
        if (!groups[orderName]) groups[orderName] = [];
        groups[orderName].push({
          uniqueId: `${uniqueId}__noaction`,
          change: "No action needed",
          lineItem: lineItemName,
          pill,
          reasoning: reason,
          noActionNeeded: true,
        });
      } else {
        updatePayload.forEach((recommendation: any, recIdx: number) => {
          if (!groups[orderName]) groups[orderName] = [];
          groups[orderName].push({
            uniqueId: `${uniqueId}__${recIdx}`,
            change: recommendation.change || "N/A",
            lineItem: lineItemName,
            pill,
            reasoning: reason,
            selected: selectedRecommendations.value.has(
              `${uniqueId}__${recIdx}`
            ),
            payload: recommendation.payload,
            noActionNeeded: false,
          });
        });
      }
      globalIndex++;
    });
  });
  return groups;
});

const selectedCount = computed(() => selectedRecommendations.value.size);

function toggleRecommendation(uniqueId: string) {
  if (selectedRecommendations.value.has(uniqueId)) {
    selectedRecommendations.value.delete(uniqueId);
  } else {
    selectedRecommendations.value.add(uniqueId);
  }
}

function isAllSelected(orderKey: string): boolean {
  const table = groupedTableData.value[orderKey] || [];
  return (
    table.length > 0 &&
    table.every((row) => selectedRecommendations.value.has(row.uniqueId))
  );
}

function isIndeterminate(orderKey: string): boolean {
  const table = groupedTableData.value[orderKey] || [];
  const selected = table.filter((row) =>
    selectedRecommendations.value.has(row.uniqueId)
  );
  return selected.length > 0 && selected.length < table.length;
}

function toggleAllRecommendations(orderKey: string) {
  const table = groupedTableData.value[orderKey] || [];
  if (isAllSelected(orderKey)) {
    table.forEach((row) => selectedRecommendations.value.delete(row.uniqueId));
  } else {
    table.forEach((row) => selectedRecommendations.value.add(row.uniqueId));
  }
}

function toggleReasoning(uniqueId: string) {
  expandedReasoning[uniqueId] = !expandedReasoning[uniqueId];
}

defaultSelectAll();

function defaultSelectAll() {
  const groups = groupedTableData.value;
  Object.values(groups).forEach((table: any) => {
    table.forEach((row: any) => {
      if (!row.noActionNeeded) {
        selectedRecommendations.value.add(row.uniqueId);
      }
    });
  });
  emit("countChanged", selectedCount.value);
}

watch(selectedRecommendations, () => {
  emit("countChanged", selectedCount.value);
});

onMounted(() => {
  defaultSelectAll();
});

useCounterEmit(emit, selectedCount);
</script>

<template>
  <div class="main-content-frame">
    <!-- Description Section -->
    <div v-if="description" class="description-section">
      <Markdown :content="description" />
    </div>
    <div
      v-for="(table, orderName) in groupedTableData"
      :key="orderName"
      class="order-section"
    >
      <div class="order-title">{{ orderName }}</div>
      <DataTable
        :value="table"
        class="recommendations-table"
        :pt="{
          table: { class: 'w-full table-fixed' },
          thead: { class: 'bg-white' },
          tbody: { class: 'bg-white' },
          headerRow: { class: 'border-b border-neutral-200' },
          bodyRow: { class: 'border-b border-neutral-200' },
          headerCell: { class: 'py-4' },
          bodyCell: { class: 'py-4' },
        }"
      >
        <!-- Checkbox Column -->
        <Column
          :style="{ width: '5%' }"
          bodyClass="align-top"
          headerClass="align-top"
        >
          <template #header>
            <Checkbox
              v-if="table.some((row) => !row.noActionNeeded)"
              :model-value="isAllSelected(orderName)"
              :indeterminate="isIndeterminate(orderName)"
              @update:model-value="() => toggleAllRecommendations(orderName)"
              binary
              size="small"
            />
          </template>
          <template #body="{ data }">
            <Checkbox
              v-if="!data.noActionNeeded"
              :model-value="selectedRecommendations.has(data.uniqueId)"
              @update:model-value="() => toggleRecommendation(data.uniqueId)"
              binary
              size="small"
            />
          </template>
        </Column>
        <!-- Change Column -->
        <Column
          :style="{ width: '20%' }"
          bodyClass="align-top"
          headerClass="align-top"
        >
          <template #header>
            <span class="font-semibold text-sm">Change</span>
          </template>
          <template #body="{ data }">
            <span class="text-sm text-gray-700">{{ data.change }}</span>
          </template>
        </Column>
        <!-- Line Item Column -->
        <Column
          :style="{ width: '40%' }"
          bodyClass="align-top"
          headerClass="align-top"
        >
          <template #header>
            <span class="font-semibold text-sm">Line Item</span>
          </template>
          <template #body="{ data }">
            <div class="flex flex-col gap-1">
              <span class="ecpm-pill">{{ data.pill }}</span>
              <span class="text-sm text-gray-700 break-all">{{
                data.lineItem
              }}</span>
            </div>
          </template>
        </Column>
        <!-- Reasoning Column -->
        <Column
          :style="{ width: '35%' }"
          bodyClass="align-top"
          headerClass="align-top"
        >
          <template #header>
            <span class="font-semibold text-sm">Reasoning</span>
          </template>
          <template #body="{ data }">
            <div class="flex flex-col gap-1">
              <span class="text-sm text-gray-700">
                <template v-if="!expandedReasoning[data.uniqueId]">
                  {{
                    data.reasoning.length > 100
                      ? data.reasoning.slice(0, 100) + "..."
                      : data.reasoning
                  }}
                </template>
                <template v-else>
                  {{ data.reasoning }}
                </template>
              </span>
              <button
                v-if="data.reasoning.length > 100"
                class="read-more-btn"
                @click="() => toggleReasoning(data.uniqueId)"
              >
                {{
                  expandedReasoning[data.uniqueId] ? "Show less" : "Show more"
                }}
              </button>
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
    <!-- Error State -->
    <div
      v-if="Object.keys(groupedTableData).length === 0"
      class="error-section"
    >
      <h3 class="section-title">Unable to load ECPM Spike data</h3>
      <p class="error-text">
        The ECPM spike data could not be loaded. Please try refreshing the page
        or contact support if the issue persists.
      </p>
    </div>
  </div>
</template>

<style scoped>
.main-content-frame {
  @apply flex flex-col items-start p-0 gap-6 w-full self-stretch;
}
.decision-summary-section {
  @apply flex flex-col items-start p-4 gap-3 bg-neutral-100 rounded-lg w-full mb-2;
}
.decision-summary-title {
  @apply label-3 text-neutral-800 mb-1.5;
}
.description-section {
  @apply flex flex-col items-start p-4 gap-3 bg-surface-50 rounded-lg w-full mb-2;
}
.order-section {
  @apply w-full mb-8 p-0;
}
.order-title {
  @apply label-3 text-neutral-700 mb-3 mt-2;
}
.recommendations-table {
  @apply w-full rounded-lg overflow-hidden;
}
.ecpm-pill {
  @apply inline-block w-fit bg-neutral-100 text-neutral-700 text-2xs font-medium rounded px-1.5 py-0.5 mb-0.5 align-middle;
}
.read-more-btn {
  @apply text-primary-500 bg-none border-none cursor-pointer text-xs p-0 mt-0.5 self-start;
}
.error-section {
  @apply flex flex-col items-start p-4 gap-3 bg-red-50 rounded-lg border border-red-200 w-full;
}
.section-title {
  @apply label-3 text-red-800 m-0;
}
.error-text {
  @apply body-1 text-red-600 m-0;
}
</style>
