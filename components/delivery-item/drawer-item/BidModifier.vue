<script setup lang="ts">
import {computed, ref} from 'vue';
import type {DeliveryItem} from "~/types/delivery-item/delivery-item";
import type {BidModifierTerm} from "~/types/delivery-item/suggestion/bid-modifier";
import {BidModifierSuggestionSchema} from "~/types/delivery-item/suggestion/bid-modifier";
import type {CounterEmit} from "~/types/counterEmit";
import Markdown from '~/components/common/Markdown.vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

const props = defineProps<{
  item: DeliveryItem;
}>();

const emit = defineEmits<CounterEmit>();

const bidModifierSuggestion = computed(() => {
  return getSuggestionPayload(props.item, BidModifierSuggestionSchema);
});

const reasoningData = computed(() => {
  return bidModifierSuggestion.value?.reason || [];
});

const expandedStates = ref<Record<string, boolean>>({});

const groupedLineItems = computed(() => {
  if (!bidModifierSuggestion.value) return [];
  
  return bidModifierSuggestion.value.line_items.map(lineItem => {
    const reasoning = reasoningData.value.find(r => r.line_item_id === lineItem.line_item_id);
    
    const enrichedTerms = lineItem.bid_modifier_content.ruleExpression.terms.map((term, termIndex) => {
      if (term.selected === undefined) {
        term.selected = true;
      }
      
      const termReasoning = reasoning?.term_reasoning.find(tr => {
        if (Math.abs(tr.term.bidAdjustment - term.bidAdjustment) > 0.001) return false;
        
        const deviceMatch = JSON.stringify(tr.term.deviceType || []) === JSON.stringify(term.deviceType || []);
        const osMatch = JSON.stringify(tr.term.operatingSystem || []) === JSON.stringify(term.operatingSystem || []);
        const regionMatch = JSON.stringify(tr.term.region || []) === JSON.stringify(term.region || []);
        
        const segmentMatch = JSON.stringify(tr.term.segmentIds || []) === JSON.stringify(term.behavioralSegment || []);
        
        const isMatch = deviceMatch && osMatch && regionMatch && segmentMatch;
        
        return isMatch;
      });
      
      const fullReason = termReasoning?.reason || 'Performance-based adjustment';
      const shortReason = fullReason.length > 100 ? fullReason.substring(0, 100) + '...' : fullReason;
      
      const termKey = `${lineItem.line_item_id}-${termIndex}`;
      
      return {
        term: term,
        multiplier: formatMultiplier(term.bidAdjustment),
        category: getDimensionCategory(term),
        categoryColor: getCategoryColor(term),
        dimensions: formatDimensions(term, termReasoning),
        reasoning: shortReason,
        fullReasoning: fullReason,
        expanded: expandedStates.value[termKey] || false,
        termKey: termKey
      };
    });
    
    const selectedTermsCount = enrichedTerms.filter(et => et.term.selected).length;
    
    return {
      lineItemId: lineItem.line_item_id,
      terms: enrichedTerms,
      selectedTermsCount
    };
  });
});



function toggleReadMore(lineItemIndex: number, termIndex: number) {
  const lineItem = groupedLineItems.value[lineItemIndex];
  if (lineItem && lineItem.terms[termIndex]) {
    const termKey = lineItem.terms[termIndex].termKey;
    expandedStates.value[termKey] = !expandedStates.value[termKey];
  }
}

const selectedCount = computed(() => {
  return groupedLineItems.value.reduce((total, lineItem) => {
    return total + lineItem.terms.filter((term: any) => term.term.selected).length;
  }, 0);
});

function isLineItemAllSelected(lineItemIndex: number): boolean {
  const lineItem = groupedLineItems.value[lineItemIndex];
  return lineItem && lineItem.terms.length > 0 && lineItem.terms.every((term: any) => term.term.selected);
}

function isLineItemIndeterminate(lineItemIndex: number): boolean {
  const lineItem = groupedLineItems.value[lineItemIndex];
  if (!lineItem || lineItem.terms.length === 0) return false;
  
  const selectedTerms = lineItem.terms.filter((term: any) => term.term.selected);
  return selectedTerms.length > 0 && selectedTerms.length < lineItem.terms.length;
}

function toggleLineItemSelection(lineItemIndex: number): void {
  const lineItem = groupedLineItems.value[lineItemIndex];
  if (!lineItem) return;
  
  const shouldSelectAll = !isLineItemAllSelected(lineItemIndex);
  
  lineItem.terms.forEach((term: any) => {
    term.term.selected = shouldSelectAll;
  });
}

function getLineItemName(lineItemId: string): string {
  if (!bidModifierSuggestion.value?.reason) {
    return `Line Item ${lineItemId.slice(-4)}`;
  }
  
  const reasonItem = bidModifierSuggestion.value.reason.find(r => r.line_item_id === lineItemId);
  return reasonItem?.line_item_name || `Line Item ${lineItemId.slice(-4)}`;
}

function formatMultiplier(bidAdjustment: number): string {
  const percentage = Math.round((bidAdjustment - 1) * 100);
  const sign = percentage >= 0 ? '+' : '';
  return `x${bidAdjustment.toFixed(2)} bid (${sign}${percentage}%)`;
}

function getDimensionCategory(term: BidModifierTerm): string {
  if (term.region?.length || term.country?.length || term.city?.length) return 'Geography';
  if (term.deviceType?.length || term.operatingSystem?.length || term.deviceMake?.length || term.browser?.length) return 'Technology';
  if (term.domain?.length) return 'Domains';
  if (term.behavioralSegment?.length) return 'Audience';
  if (term.adFormat?.length || term.slotPosition?.length || term.slotSize?.length) return 'Inventory';
  return 'Other';
}

function getCategoryColor(term: BidModifierTerm): string {
  const category = getDimensionCategory(term);
  const colorMap: Record<string, string> = {
    'Geography': 'bg-blue-400',
    'Technology': 'bg-gray-800',
    'Domains': 'bg-blue-300',
    'Audience': 'bg-purple-400',
    'Inventory': 'bg-green-400',
    'Other': 'bg-gray-400'
  };
  return colorMap[category] || 'bg-gray-400';
}

function getCategoryIcon(category: string): string {
  const iconMap: Record<string, string> = {
    'Geography': '🗺️',
    'Technology': '💻',
    'Domains': '🌐',
    'Audience': '👥',
    'Inventory': '📦',
    'Other': '🔗'
  };
  return iconMap[category] || '🔗';
}

function formatDimensions(term: BidModifierTerm, termReasoning?: any): string {
  const dimensions: string[] = [];
  
  if (term.deviceType?.length) dimensions.push(`Device: ${term.deviceType.join(', ')}`);
  if (term.operatingSystem?.length) dimensions.push(`OS: ${term.operatingSystem.join(', ')}`);
  if (term.region?.length) dimensions.push(term.region.join(', '));
  
  if (term.domain?.length) {
    const domainNames = term.domain.map(d => {

      const match = d.match(/(.*?)\s*\(ID:/);
      return match ? match[1].trim() : d;
    });
    dimensions.push(domainNames.join(', '));
  }
  
  if (termReasoning?.term?.segmentNames?.length) {
    dimensions.push(`Segments: ${termReasoning.term.segmentNames.join(', ')}`);
  } else if (term.behavioralSegment?.length) {
    dimensions.push(`Segment IDs: ${term.behavioralSegment.join(', ')}`);
  }
  
  return dimensions.join(', ') || 'Multiple dimensions';
}

const markdownDescription = computed(() => {
  const text = bidModifierSuggestion.value?.description || '';
  if (!text) return '';
  
  return text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // **bold**
    .replace(/\n- /g, '\n• ') 
    .replace(/\n/g, '<br>'); 
});

useCounterEmit(emit, selectedCount);

// Add table data structure
const tableData = computed(() => {
  return groupedLineItems.value.flatMap((lineItemGroup, lineItemIndex) => {
    return lineItemGroup.terms.map((term, termIndex) => ({
      lineItemIndex,
      termIndex,
      term,
      lineItemId: lineItemGroup.lineItemId,
      selected: term.term.selected,
      multiplier: term.multiplier,
      category: term.category,
      dimensions: term.dimensions,
      reasoning: term.reasoning,
      fullReasoning: term.fullReasoning,
      expanded: term.expanded,
      termKey: term.termKey
    }));
  });
});
</script>

<template>
    <!-- Frame 3: Campaign Info + Line Items Container -->
  <div class="main-content-frame">
    <!-- Campaign Name -->
    <div v-if="bidModifierSuggestion?.order_name" class="campaign-name-section">
      <h2 class="campaign-name">{{ bidModifierSuggestion.order_name }}</h2>
    </div>
    
    <!-- Strategic Recommendations -->
    <div v-if="bidModifierSuggestion?.description" class="strategic-recommendations-section">
      <Markdown :content="bidModifierSuggestion.description" />
    </div>

    <!-- Line Items with Grouped Terms -->
    <div 
      v-for="(lineItemGroup, lineItemIndex) in groupedLineItems" 
      :key="lineItemGroup.lineItemId"
      class="line-item-section"
    >
      <!-- Line Item Header -->
      <div class="line-item-header">
        <h3 class="line-item-title">
          {{ getLineItemName(lineItemGroup.lineItemId) }}
        </h3>
        <p class="line-item-subtitle">
          {{ lineItemGroup.selectedTermsCount }} / {{ lineItemGroup.terms.length }} adjustments selected
        </p>
      </div>

      <!-- Line Item Table -->
      <DataTable
        :value="tableData.filter(item => item.lineItemIndex === lineItemIndex)"
        class="line-item-table"
        :pt="{
          table: { class: 'w-full' },
          thead: { class: 'bg-white' },
          tbody: { class: 'bg-white' },
          headerRow: { class: 'border-b border-neutral-200' },
          bodyRow: { class: 'border-b border-neutral-200' },
          headerCell: { class: 'py-4' },
          bodyCell: { class: 'py-4' }
        }"
        @row-select="(event) => console.log('Row selected:', event)"
        @row-unselect="(event) => console.log('Row unselected:', event)"
      >
        <Column :style="{ width: '160px', paddingLeft: '0px' }">
          <template #header>
            <div class="flex items-center gap-2">
              <Checkbox
                :model-value="isLineItemAllSelected(lineItemIndex)"
                :indeterminate="isLineItemIndeterminate(lineItemIndex)"
                @update:model-value="(value) => toggleLineItemSelection(lineItemIndex)"
                binary
                size="small"
              />
              <span class="font-semibold text-sm">Change</span>
            </div>
          </template>
          <template #body="{ data }">
            <div class="flex items-center gap-4">
              <Checkbox
                v-model="data.term.term.selected"
                binary
                size="small"
              />
              <span class="font-semibold">{{ data.multiplier }}</span>
            </div>
          </template>
        </Column>

        <Column :style="{ width: '280px', paddingLeft: '0px' }">
          <template #header>
            <span class="font-semibold text-sm table-header">Affected User Dimensions</span>
          </template>
          <template #body="{ data }">
            <div class="flex flex-col gap-1">
              <div class="category-tag">
                <span class="category-icon">{{ getCategoryIcon(data.category) }}</span>
                <span class="category-label">{{ data.category }}</span>
              </div>
              <div class="dimensions-content">
                <span class="dimensions-text">{{ data.dimensions }}</span>
              </div>
            </div>
          </template>
        </Column>

        <Column :style="{ width: '300px', paddingLeft: '0px', paddingRight: '24px' }">
          <template #header>
            <span class="font-semibold text-sm">Reasoning</span>
          </template>
          <template #body="{ data }">
            <div class="flex flex-col gap-1">
              <div class="reasoning-content">
                <span class="text-sm text-neutral-700" v-if="!data.expanded">{{ data.reasoning }}</span>
                <span class="text-sm text-neutral-700" v-else>{{ data.fullReasoning }}</span>
              </div>
              <button 
                @click="toggleReadMore(data.lineItemIndex, data.termIndex)"
                class="read-more-btn"
              >
                {{ data.expanded ? 'read less' : 'read more' }}
              </button>
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>

<style scoped>
/* Main Content Container - Match Drawer Width */
.main-content-frame {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  gap: 24px;
  width: 100%;
  height: auto;
}

.campaign-name-section {
  width: 100%;
  padding: 0;
}

.campaign-name {
  width: 100%;
  height: 48px;
  @apply label-4 text-primary-text;
  display: flex;
  align-items: center;
  margin: 0;
}

.campaign-description-section {
  width: 100%;
  padding: 0 16px;
  @apply body-1 text-primary-text;
}

/* Strategic Recommendations Section */
.strategic-recommendations-section {
  width: 100%;
  padding: 0;
  margin-bottom: 24px;
  @apply body-1 text-primary-text;
}

/* Line Item Section Styling */
.line-item-section {
  width: 100%;
  margin-bottom: 32px;
  padding: 0;
}

.line-item-header {
  margin-bottom: 16px;
  padding: 0;
}

.line-item-title {
  @apply label-3 text-primary-text;
  margin-bottom: 4px;
}

.line-item-subtitle {
  @apply caption-2 text-disabled-text;
}

/* Line Item Table Styling - Match Figma Design */
.line-item-table {
  @apply w-full rounded-lg overflow-hidden;
  margin: 0;
  padding: 0;
}

/* Category tag styling based on provided CSS */
.category-tag {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 4px;
  gap: 8px;
  width: fit-content;
  height: 20px;
  @apply bg-secondary-background rounded-lg;
  flex: none;
  flex-shrink: 0;
}

.category-icon {
  @apply caption-2 text-disabled-text;
  width: 10px;
  height: 16px;
  display: flex;
  align-items: center;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.category-label {
  @apply caption-2 text-disabled-text;
  width: fit-content;
  height: 16px;
  display: flex;
  align-items: center;
  flex: none;
  order: 1;
  flex-grow: 0;
}

/* Content Areas */
.dimensions-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  gap: 4px;
  width: 100%;
  overflow-wrap: break-word;
}

.dimensions-text {
  @apply text-sm text-neutral-700;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
}

.reasoning-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  gap: 4px;
  
  width: 100%;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

/* Read More Button */
.read-more-btn {
  @apply label-2 text-primary hover:text-primary-emphasis;
  display: flex;
  align-items: center;
  
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

/* Add this new class for the table header */
.table-header {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 