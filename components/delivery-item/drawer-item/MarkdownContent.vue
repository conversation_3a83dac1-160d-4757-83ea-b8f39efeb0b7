<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import Markdown from "~/components/common/Markdown.vue";

const props = defineProps<{
  item: DeliveryItem;
}>();

const markdownContents = ref<string[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

const parsedFullMessage = computed(() => {
  try {
    if (typeof props.item.fullMessage === "string") {
      return JSON.parse(props.item.fullMessage || "{}");
    }
    return props.item.fullMessage || {};
  } catch {
    return {};
  }
});

const title = computed(() => parsedFullMessage.value.title || "Markdown Content");

async function fetchMarkdownContents() {
  try {
    loading.value = true;
    error.value = null;
    
    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 使用模拟数据进行前端测试
    const mockResponse = [
      `# Campaign Performance Summary

## Key Metrics
- **Total Impressions**: 1,245,678
- **Click-through Rate**: 2.34%
- **Conversion Rate**: 4.56%
- **ROAS**: 3.2x

## Top Performing Campaigns
1. **Holiday Sale Campaign** - 45% of total conversions
2. **Brand Awareness Push** - Highest CTR at 3.1%
3. **Retargeting Campaign** - Best ROAS at 4.5x

> 📈 **Overall performance is up 15% compared to last week**`,

      `# Optimization Recommendations

## Immediate Actions
- **Increase Budget** for Holiday Sale Campaign by 20%
- **Pause** underperforming ad groups with CTR < 1%
- **Test** new creative variations for Brand Awareness

## Audience Insights
- Mobile traffic shows **higher engagement** (+25%)
- Evening hours (6-9 PM) have **best conversion rates**
- Users aged 25-34 represent **highest LTV**

### Next Steps
1. Implement mobile-first creative strategy
2. Adjust bid schedules for evening hours
3. Create lookalike audiences based on high-LTV users`,

      `# Budget & Spend Analysis

## Current Spend Breakdown
| Campaign Type | Spend | % of Budget | Performance |
|---------------|-------|-------------|-------------|
| Search | $12,450 | 45% | ⭐⭐⭐⭐⭐ |
| Display | $8,200 | 30% | ⭐⭐⭐ |
| Social | $6,890 | 25% | ⭐⭐⭐⭐ |

## Forecast
- **Projected Monthly Spend**: $82,500
- **Remaining Budget**: $17,500
- **Recommended Reallocation**: Move 10% from Display to Search

⚠️ **Alert**: Display campaigns are 15% over target CPA`
    ];
    
    markdownContents.value = mockResponse;
  } catch (err: any) {
    console.error("Error fetching markdown contents:", err);
    error.value = err.message || "Failed to fetch markdown contents";
    markdownContents.value = [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchMarkdownContents();
});
</script>

<template>
  <div class="main-content-frame">
    <!-- Title Section -->
    <div class="title-section">
      <h3 class="section-title">{{ title }}</h3>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-section">
      <div class="flex justify-center items-center min-h-48">
        <div class="text-center space-y-3">
          <div class="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto"></div>
          <p class="text-sm font-medium text-gray-600">Loading content...</p>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-section">
      <h4 class="error-title">Unable to load content</h4>
      <p class="error-text">{{ error }}</p>
    </div>

    <!-- Content Cards -->
    <div v-else-if="markdownContents.length > 0" class="content-cards">
      <div
        v-for="(content, index) in markdownContents"
        :key="index"
        class="markdown-card"
      >
        <div class="card-header">
          <h4 class="card-title">Content {{ index + 1 }}</h4>
        </div>
        <div class="card-content">
          <Markdown :content="content" />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-section">
      <h4 class="empty-title">No content available</h4>
      <p class="empty-text">There are no markdown contents to display at this time.</p>
    </div>
  </div>
</template>

<style scoped>
.main-content-frame {
  @apply flex flex-col items-start p-0 gap-6 w-full self-stretch;
}

.title-section {
  @apply w-full;
}

.section-title {
  @apply text-xl font-semibold text-neutral-800 mb-4;
}

.loading-section {
  @apply w-full;
}

.error-section {
  @apply flex flex-col items-start p-4 gap-3 bg-red-50 rounded-lg border border-red-200 w-full;
}

.error-title {
  @apply text-lg font-semibold text-red-800 m-0;
}

.error-text {
  @apply text-sm text-red-600 m-0;
}

.empty-section {
  @apply flex flex-col items-start p-4 gap-3 bg-gray-50 rounded-lg border border-gray-200 w-full;
}

.empty-title {
  @apply text-lg font-semibold text-gray-800 m-0;
}

.empty-text {
  @apply text-sm text-gray-600 m-0;
}

.content-cards {
  @apply flex flex-col gap-4 w-full;
}

.markdown-card {
  @apply bg-white border border-neutral-200 rounded-lg overflow-hidden w-full;
}

.card-header {
  @apply px-4 py-3 bg-neutral-50 border-b border-neutral-200;
}

.card-title {
  @apply text-base font-medium text-neutral-800 m-0;
}

.card-content {
  @apply p-4;
}
</style>