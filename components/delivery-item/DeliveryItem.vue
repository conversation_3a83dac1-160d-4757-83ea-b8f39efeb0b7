<script setup lang="ts">
import {type DeliveryItem, DeliveryItemTypeSchema} from "~/types/delivery-item/delivery-item";
import { useIntersectionObserver } from "@vueuse/core";
import {getOverriddenFullMessage} from "~/utils/deliveryItemUtils";
import { ref, computed, onMounted, onUnmounted } from 'vue';
import type { DeliveryItem as DeliveryItemType } from '~/types/delivery-item/delivery-item';
import MarkdownCard from "~/components/delivery-item/MarkdownCard.vue";

const props = defineProps<{
  item: DeliveryItem;
}>();

const { deliveryItemConfig } = useDeliveryItemConfig();
const advertiserStore = useAdvertiserStore();

const drawerVisible = ref(false);
const cardRef = ref<HTMLElement | null>(null);
const currentTimeTick = ref(Date.now());
let intervalId: ReturnType<typeof setInterval> | null = null;
const contentEndMarker = ref<HTMLElement>();

const typeConfig = computed(() => {
  return deliveryItemConfig.value.types[props.item.type];
});

const categoryConfig = computed(() => {
  return deliveryItemConfig.value.categories[typeConfig.value.category];
});

const actioned = computed(() => {
  return !!(props.item.actionBy && props.item.actionAt);
});

const updatedAtText = computed(() => {
  const _tick = currentTimeTick.value;
  return timeAgoText(props.item.updatedAt);
});

const displayAdvertiserInfo = computed(() => {
  return props.item.ownerType === "ADVERTISER" && props.item.ownerId;
});

const advertiser = computed(() => {
  return advertiserStore.getAdvertiserById(props.item.ownerId);
});

const updateTick = () => {
  currentTimeTick.value = Date.now();
};

onMounted(() => {
  intervalId = setInterval(updateTick, 5 * 60 * 1000);

  if (cardRef.value) {
    useIntersectionObserver(
      cardRef,
      ([{ isIntersecting }]: IntersectionObserverEntry[]) => {
        if (isIntersecting) {
          updateTick();
        }
      },
      { threshold: 0.1 }
    );
  }
});

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
});

const overriddenFullMessage = getOverriddenFullMessage(props.item);

</script>
<template>
  <!-- 使用新的 MarkdownCard 组件 -->
  <MarkdownCard v-if="item.type === 'MARKDOWN_CONTENT'" :item="item" />
  
  <!-- 原有的普通卡片 -->
  <Card
    v-else
    ref="cardRef"
    :pt:root:class="[
      'rounded-2xl border border-neutral-300  overflow-clip',
      {
        'shadow-md': item.isRelevant,
        'shadow-none': !item.isRelevant || !!item.actionBy,
      },
    ]"
    pt:body:class="p-0 gap-0"
    :pt:header:class="[
      'flex items-center justify-between pl-6 pr-4 py-3',
      {
        [categoryConfig.backgroundColorClass]: item.isRelevant && !actioned,
        'bg-neutral-100': !item.isRelevant || actioned,
      },
    ]"
    pt:content:class="flex flex-col gap-2 px-4 pt-4"
    pt:footer:class="flex items-center justify-between p-4"
  >
    <template #header>
      <div class="flex items-center gap-4">
        <MaterialIcon
          :icon="categoryConfig.icon"
          :class="{
            [categoryConfig.iconColorClass]: item.isRelevant && !actioned,
            'text-neutral-500': !item.isRelevant || actioned,
          }"
          size="16px"
        />
        <span class="label-3 text-secondary-text">
          {{ categoryConfig.label }}
        </span>
      </div>

      <div class="flex items-center gap-4">
        <span class="caption-2 text-disabled-text">{{ updatedAtText }}</span>
        <MaterialIcon
          v-if="item.hasExecutionError"
          v-tooltip.bottom="'The execution of this item has failed'"
          icon="report"
          size="20px"
          class="text-red-500"
        />
      </div>
    </template>

    <template #content>
      <div v-if="displayAdvertiserInfo" class="flex items-center gap-2">
        <FallbackAvatar
          :image="`/images/advertiser/${advertiser?.advertiserId}`"
          :label="advertiser?.amazonDspAdvertiserName"
          shape="rounded"
        />
        <span class="label-4 text-primary-text">
          {{ advertiser?.amazonDspAdvertiserName }}
        </span>
      </div>

      <span class="body-2-alt text-primary-text">{{ item.summary }}</span>
      <div class="line-clamp-3">
        <Markdown
          :content="overriddenFullMessage"
          class="body-1 text-secondary-text"
        />
        <span ref="contentEndMarker"></span>
      </div>
    </template>

    <template #footer>
      <DeliveryItemFooter
        :item="item"
        :type-config="typeConfig"
        :actioned="actioned"
        show-actions
        :primary-count="undefined"
        @expand="drawerVisible = true"
      />
    </template>
  </Card>

  <DeliveryItemDrawer v-if="item.type !== 'MARKDOWN_CONTENT'" :item="item" v-model:visible="drawerVisible" />
</template>
