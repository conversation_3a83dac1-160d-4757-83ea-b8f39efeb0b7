<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import Markdown from "~/components/common/Markdown.vue";
import Button from "primevue/button";

const props = defineProps<{
  item: DeliveryItem;
}>();

const isExpanded = ref(false);
const isUserQueryExpanded = ref(false);

const categoryConfig = computed(() => ({
  label: "Custom-built Card",
  icon: "info",
  backgroundColorClass: "bg-violet-50", 
  iconColorClass: "text-violet-600"
}));

const typeConfig = computed(() => ({
  category: "BRIEFING",
  allowExpand: false
}));

const actioned = computed(() => {
  return !!(props.item.actionBy && props.item.actionAt);
});

const parsedFullMessage = computed(() => {
  try {
    if (typeof props.item.fullMessage === "string") {
      return JSON.parse(props.item.fullMessage || "{}");
    }
    return props.item.fullMessage || {};
  } catch {
    return {};
  }
});

const title = computed(() => parsedFullMessage.value.title || "Markdown Content");
const markdownContent = computed(() => parsedFullMessage.value.content || "");
const userQuery = computed(() => parsedFullMessage.value.user_query || "");

const displayAdvertiserInfo = computed(() => {
  return props.item.ownerType === "ADVERTISER" && props.item.ownerId;
});

// 简化 advertiser 信息，避免依赖 store
const advertiser = computed(() => {
  if (!displayAdvertiserInfo.value) return null;
  return {
    advertiserId: props.item.ownerId,
    amazonDspAdvertiserName: `Advertiser ${props.item.ownerId}`
  };
});

const updatedAtText = computed(() => {
  return timeAgoText(props.item.updatedAt);
});

function toggleExpand() {
  isExpanded.value = !isExpanded.value;
}

function toggleUserQuery() {
  isUserQueryExpanded.value = !isUserQueryExpanded.value;
}

function handleAcknowledge() {
  // 简单的确认处理，避免依赖可能出错的服务
  console.log('Acknowledged markdown card:', props.item.id);
  // 可以在这里添加成功消息或更新本地状态
}

</script>

<template>
  <div class="markdown-feed-card">
    <!-- 单个 markdown 卡片 -->
    <div
      class="markdown-card"
      :class="{
        'shadow-md': item.isRelevant,
        'shadow-none': !item.isRelevant || actioned,
      }"
    >
      <!-- 卡片头部 -->
      <div 
        class="card-header"
        :class="{
          [categoryConfig.backgroundColorClass]: item.isRelevant && !actioned,
          'bg-neutral-100': !item.isRelevant || actioned,
        }"
      >
        <div class="flex items-center gap-4">
          <MaterialIcon
            :icon="categoryConfig.icon"
            :class="{
              [categoryConfig.iconColorClass]: item.isRelevant && !actioned,
              'text-neutral-500': !item.isRelevant || actioned,
            }"
            size="16px"
          />
          <span class="label-3 text-secondary-text">
            {{ categoryConfig.label }}
          </span>
        </div>

        <div class="flex items-center gap-4">
          <span class="caption-2 text-disabled-text">{{ updatedAtText }}</span>
          <MaterialIcon
            v-if="item.hasExecutionError"
            v-tooltip.bottom="'The execution of this item has failed'"
            icon="report" 
            size="20px"
            class="text-red-500"
          />
        </div>
      </div>

      <!-- 卡片内容 -->
      <div class="card-content">
        <div v-if="displayAdvertiserInfo" class="flex items-center gap-2 mb-4">
          <FallbackAvatar
            :image="`/images/advertiser/${advertiser?.advertiserId}`"
            :label="advertiser?.amazonDspAdvertiserName"
            shape="rounded"
          />
          <span class="label-4 text-primary-text">
            {{ advertiser?.amazonDspAdvertiserName }}
          </span>
        </div>

        <!-- 标题和摘要 -->
        <div class="mb-4">
          <h3 class="markdown-card-title">{{ title }}</h3>
          
          <!-- Original Query -->
          <div v-if="userQuery" class="user-query-section">
            <button 
              @click="toggleUserQuery"
              class="user-query-toggle"
            >
              <MaterialIcon 
                :icon="isUserQueryExpanded ? 'expand_less' : 'expand_more'"
                size="16px"
                style="margin-right: 4px;"
              />
              <span class="user-query-label">Original query</span>
            </button>
            
            <div v-if="isUserQueryExpanded" class="user-query-content">
              {{ userQuery }}
            </div>
          </div>
          
          <p class="markdown-card-summary">{{ item.summary }}</p>
        </div>

        <!-- Markdown 内容 -->
        <div class="markdown-content">
          <div class="markdown-wrapper" :class="{ 'expanded': isExpanded }">
            <Markdown :content="markdownContent" />
          </div>
          <!-- 展开/收起按钮 -->
          <button 
            @click="toggleExpand"
            class="expand-button"
            v-if="markdownContent.length > 300"
          >
            {{ isExpanded ? 'Show less' : 'Show more' }}
          </button>
        </div>
      </div>

      <!-- 卡片底部操作区 -->
      <div class="card-footer">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <MaterialIcon 
              icon="check_circle" 
              size="16px"
              :class="actioned ? 'text-green-600' : 'text-gray-400'"
            />
            <span v-if="actioned" class="text-sm text-green-600 font-medium">
              Acknowledged
            </span>
          </div>
          
          <Button
            v-if="!actioned"
            label="Got it, thanks"
            size="small"
            :pt="{ 
              root: { class: 'px-4 py-2 bg-neutral-700 hover:bg-neutral-800 text-white text-sm rounded-lg transition-colors' }
            }"
            @click="handleAcknowledge"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.markdown-feed-card {
  @apply w-full;
}

.markdown-card {
  @apply bg-white border border-neutral-300 rounded-2xl overflow-hidden;
}

.card-header {
  @apply flex items-center justify-between px-6 py-3;
}

.card-number {
  @apply text-xs bg-white/20 rounded-full px-2 py-1 font-medium;
}

.card-content {
  @apply px-6 py-4;
}

.markdown-card-title {
  @apply text-xl font-semibold text-primary-text mb-2;
}

.markdown-card-summary {
  @apply text-sm text-secondary-text;
}

.markdown-content {
  @apply mt-4;
}

.markdown-wrapper {
  @apply overflow-hidden transition-all duration-300;
  max-height: 200px;
}

.markdown-wrapper.expanded {
  max-height: none;
}

.expand-button {
  @apply text-primary-500 bg-none border-none cursor-pointer text-sm font-medium mt-3 hover:text-primary-600 transition-colors;
}

.card-footer {
  @apply px-6 py-4 border-t border-neutral-200;
}

.loading-section {
  @apply w-full;
}

.error-section {
  @apply w-full;
}

.empty-state {
  @apply w-full;
}

/* 用户查询样式 */
.user-query-section {
  @apply mt-2 mb-3;
}

.user-query-toggle {
  @apply flex items-center text-gray-500 hover:text-gray-700 transition-colors cursor-pointer bg-none border-none p-0 text-sm;
}

.user-query-label {
  @apply text-sm font-medium;
}

.user-query-content {
  @apply mt-2 p-3 bg-gray-50 rounded-lg text-sm text-gray-600 border-l-4 border-gray-300;
}
</style>