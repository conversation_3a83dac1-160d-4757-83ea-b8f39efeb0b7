<script setup lang="tsx">
import AudienceSwap from "~/components/delivery-item/drawer-item/AudienceSwap.vue";
import BidChanges from "~/components/delivery-item/drawer-item/BidChanges.vue";
import BidModifier from "~/components/delivery-item/drawer-item/BidModifier.vue";
import ExtendCampaign from "~/components/delivery-item/drawer-item/ExtendCampaign.vue";
import UnderPacing from "~/components/delivery-item/drawer-item/UnderPacing.vue";
import EcpmSpike from "~/components/delivery-item/drawer-item/ECPMSpike.vue";
import MarkdownContent from "~/components/delivery-item/drawer-item/MarkdownContent.vue";
import {
  type DeliveryItem,
  DeliveryItemTypeSchema,
} from "~/types/delivery-item/delivery-item";
import {getOverriddenFullMessage} from "~/utils/deliveryItemUtils";

const { deliveryItemConfig } = useDeliveryItemConfig();

const props = defineProps<{
  item: DeliveryItem;
}>();
const visible = defineModel<boolean>("visible");

const typeConfig = computed(() => {
  return deliveryItemConfig.value.types[props.item.type];
});

const categoryConfig = computed(() => {
  return deliveryItemConfig.value.categories[typeConfig.value.category];
});

const actioned = computed(() => {
  return !!(props.item.actionBy && props.item.actionAt);
});

const actionContentComponentMap = {
  [DeliveryItemTypeSchema.Enum.BID_CHANGE]: BidChanges,
  [DeliveryItemTypeSchema.Enum.EXTEND_CAMPAIGN]: ExtendCampaign,
  [DeliveryItemTypeSchema.Enum.BID_MODIFIER]: BidModifier,
  [DeliveryItemTypeSchema.Enum.ITEM_FAILED]: <div>Item Failed</div>,
  [DeliveryItemTypeSchema.Enum.AUDIENCE_SWAP]: AudienceSwap,
  [DeliveryItemTypeSchema.Enum.UNDER_PACING]: UnderPacing,
  [DeliveryItemTypeSchema.Enum.ECPM_SPIKE_MONITOR]: EcpmSpike,
  [DeliveryItemTypeSchema.Enum.GENERIC_UPDATE]: undefined,
  [DeliveryItemTypeSchema.Enum.GENERIC_ALERT]: undefined,
  [DeliveryItemTypeSchema.Enum.MARKDOWN_CONTENT]: MarkdownContent,
} as const;

const actionContentComponent = computed(() => {
  return actionContentComponentMap[props.item.type];
});

const selectedCount = ref<number | undefined>();

const overriddenFullMessage = getOverriddenFullMessage(props.item);

const handleCountChanged = (count: number) => {
  selectedCount.value = count;
};
</script>

<template>
  <Drawer
    v-model:visible="visible"
    style="width: 41.5rem"
    position="right"
    pt:root:class="rounded-l-2xl p-0"
    pt:header:class="flex items-center justify-between p-6"
    pt:footer:class="border-neutral-300 border-t p-6"
    pt:content:class="px-6"
    :show-close-icon="false"
  >
    <template #header>
      <div class="flex items-center gap-4">
        <MaterialIcon
          :icon="categoryConfig.icon"
          :class="{
            [categoryConfig.iconColorClass]: item.isRelevant && !actioned,
            'text-neutral-500': !item.isRelevant || actioned,
          }"
          size="16px"
        />
        <span class="body-2-alt text-secondary-text">{{ item.summary }}</span>
      </div>
      <MaterialIcon
        icon="close"
        size="16px"
        class="bg-secondary-background p-2 rounded-full"
        clickable
        @click="visible = false"
      />
    </template>

    <div class="flex flex-col gap-6 py-4">
      <CollapsibleCard title="Decision Summary" icon="tips_and_updates">
        <Markdown :content="overriddenFullMessage" />
      </CollapsibleCard>

      <BlockUI
        :blocked="isActionedOrNotRelevant(item)"
        :pt="{ mask: { class: '!bg-white/50' } }"
      >
        <div class="flex flex-col gap-4">
          <component
            :is="actionContentComponent"
            :item="item"
            @count-changed="handleCountChanged"
          />
        </div>
      </BlockUI>
    </div>

    <template #footer>
      <DeliveryItemFooter
        :item="item"
        :type-config="typeConfig"
        :actioned="actioned"
        :primary-count="selectedCount"
        is-drawer
      />
    </template>
  </Drawer>
</template>
<style scoped>
.footer-icon-button {
  @apply p-2 bg-neutral-50 rounded-lg;
}

.footer-action-button {
  @apply flex items-center gap-2 px-4 py-2 rounded-lg;
}

.footer-action-button-text {
  @apply font-sans text-xs font-semibold text-white;
}

.footer-action-button-primary {
  background: linear-gradient(
    180deg,
    var(--neutral-700, #404040) 0%,
    var(--neutral-800, #262626) 100%
  );
}

.footer-action-button-counter {
  @apply font-sans text-xs font-semibold text-primary-500 bg-neutral-100 rounded-full px-2 py-1;
}

.content-full-message {
  @apply font-sans text-sm text-primary-500;
}

.accepted-text {
  @apply text-sm text-primary-500;
}
</style>
