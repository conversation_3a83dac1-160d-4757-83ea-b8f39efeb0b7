<script setup lang="ts">
import { ref } from "vue";
import Button from "primevue/button";
import Dialog from "primevue/dialog";
import Select from "primevue/select";
import Textarea from "primevue/textarea";

const { user } = useUserSession();
const toast = useToast();

const isLoading = ref(false);
const message = ref("");
const showDialog = ref(false);
const selectedFrequency = ref(null);
const selectedAgency = ref(null);

const frequencyOptions = [
  { label: "60 seconds", value: "60s" },
  { label: "1 day", value: "1d" },
  { label: "7 days", value: "7d" }
];

const agencyOptions = [
  { label: "Catograph", value: "catograph" }
];

function handleCreateCard() {
  showDialog.value = true;
}

async function handleConfirmSelection() {
  if (!selectedFrequency.value || !selectedAgency.value || isLoading.value || !message.value.trim()) return;

  showDialog.value = false;
  isLoading.value = true;
  
  try {
    console.log("Sending message to workorder/run:", message.value);
    
    const workorderRequest = {
      name: "Chat Request",
      prompt: message.value,
      context_objects: {},
      run_async: true,
      ownerId: user.value?.sub || "system",
      ownerType: "USER"
    };

    const response = await $fetch("/relay/linda/workorder/run", {
      method: "POST",
      body: workorderRequest
    });

    console.log("Workorder completed successfully:", response);
    
    // 先关闭 loading
    isLoading.value = false;
    
    // 清空输入框
    message.value = "";
    // Reset selections
    selectedFrequency.value = null;
    selectedAgency.value = null;
    
    // 显示成功提示
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Card created successfully! Please wait...',
      life: 3000
    });
    
    // 延迟刷新页面，让用户看到成功提示
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    }, 2000);

  } catch (error) {
    console.error("Workorder failed:", error);
    isLoading.value = false;
  }
}

function handleCancelSelection() {
  showDialog.value = false;
  selectedFrequency.value = null;
  selectedAgency.value = null;
}

function onKeyDown(event: KeyboardEvent) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleCreateCard();
  }
}

// 移除了 showSuccessMessage 函数，现在使用 toast

</script>

<template>
  <div
    class="flex flex-col w-full gap-4 justify-between items-center py-3 px-4 border border-neutral-300 bg-neutral-50 rounded-xl shadow transition-all duration-200"
    :class="{ 'loading-state': isLoading }"
  >
    <!-- Loading 覆盖层 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <span class="loading-text">Creating your card...</span>
      </div>
    </div>

    <!-- 移除了 Success 覆盖层，现在使用 toast -->

    <Textarea
      v-model="message"
      class="input-textarea"
      :placeholder="isLoading ? 'Creating your card, please wait...' : 'Ask Gigi anything...'"
      :disabled="isLoading"
      :rows="1"
      autoResize
      @keydown="onKeyDown"
    />

    <div class="flex justify-between items-center w-full">
      <div class="flex-1"></div>

      <div class="flex justify-end items-center gap-2">
        <Button
          :disabled="isLoading"
          class="bg-gray-800 hover:bg-gray-700 text-white px-3 py-1.5 rounded text-sm transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
          @click="handleCreateCard"
        >
          {{ isLoading ? 'Creating Card...' : 'Create Customized Card' }}
        </Button>
      </div>
    </div>
  </div>

  <!-- Selection Dialog -->
  <Dialog 
    v-model:visible="showDialog"
    modal 
    header="Customize Your Card"
    :style="{ width: '400px' }"
    class="card-dialog"
    :closable="true"
  >
    <div class="space-y-6">
      <div>
        <label class="block text-sm font-medium text-neutral-700 mb-2">Frequency</label>
        <Select 
          v-model="selectedFrequency"
          :options="frequencyOptions"
          optionLabel="label"
          placeholder="Select frequency"
          class="w-full"
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium text-neutral-700 mb-2">Agency</label>
        <Select 
          v-model="selectedAgency"
          :options="agencyOptions"
          optionLabel="label"
          placeholder="Select agency"
          class="w-full"
        />
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end gap-3">
        <Button 
          label="Cancel" 
          severity="secondary" 
          @click="handleCancelSelection"
        />
        <Button 
          label="Create Card" 
          :disabled="!selectedFrequency || !selectedAgency"
          @click="handleConfirmSelection"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.input-textarea {
  @apply w-full resize-none min-h-6 !overflow-y-auto p-0
    bg-transparent outline-none border-none drop-shadow-none shadow-none
    placeholder:text-neutral-500 body-1 placeholder:body-1;
  max-height: calc(1.25rem * 5) !important; /* 5 lines max height */
}


/* Loading 状态 */
.loading-state {
  @apply relative;
}

.loading-overlay {
  @apply absolute inset-0 bg-neutral-50 bg-opacity-95 flex items-center justify-center rounded-xl z-10;
  backdrop-filter: blur(2px);
}

.loading-content {
  @apply flex flex-col items-center gap-3;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-neutral-200 border-t-neutral-600 rounded-full animate-spin;
}

.loading-text {
  @apply text-sm text-neutral-600 font-medium;
}

/* Dialog styles */
.card-dialog :deep(.p-dialog-header) {
  @apply border-b border-neutral-200 pb-4;
}

.card-dialog :deep(.p-dialog-content) {
  @apply pt-6;
}

.card-dialog :deep(.p-dialog-footer) {
  @apply border-t border-neutral-200 pt-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>