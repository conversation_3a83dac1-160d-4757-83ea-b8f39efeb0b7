import twColors from "tailwindcss/colors";

export const CHART_COLOR_INTENSITY = 100;
export const LEGEND_COLOR_INTENSITY = 50;
export const LEGEND_ACCENT_COLOR_INTENSITY = 500;

export const funnelColorMap = {
  STV: { tailwind: "bg-fuchsia-50 text-fuchsia-800", bgHex: twColors.fuchsia[50], textHex: twColors.fuchsia[800], hoverBgHex: twColors.fuchsia[100]},
  TOF: { tailwind: "bg-orange-50 text-orange-600", bgHex: twColors.orange[50], textHex: twColors.orange[600], hoverBgHex: twColors.orange[100]},
  MOF: { tailwind: "bg-blue-50 text-blue-800", bgHex: twColors.blue[50], textHex: twColors.blue[800], hoverBgHex: twColors.blue[100]},
  BOF: { tailwind: "bg-emerald-50 text-emerald-800", bgHex: twColors.emerald[50], textHex: twColors.emerald[800], hoverBgHex: twColors.emerald[100]},
  SPO: { tailwind: "bg-neutral-50 text-neutral-800", bgHex: twColors.neutral[50], textHex: twColors.neutral[800], hoverBgHex: twColors.neutral[100]}
};
