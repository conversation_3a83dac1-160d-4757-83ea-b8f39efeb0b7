<script setup lang="ts">
import { funnelColorMap } from "./constants";

const props = defineProps<{
  funnel: string;
  size?: "sm" | "md" | "lg";
}>();

const sizeClasses = computed(() => {
  switch (props.size) {
    case "lg":
      return "scale-150";
    case "md":
      return "scale-125";
    case "sm":
    default:
      return "scale-100";
  }
});

const computedClass = computed(() => {
  if (!isKnownFunnel(props.funnel)) {
    return "border border-default-border border-dashed";
  }

  return getFunnelClass(props.funnel);
});

function getFunnelClass(funnel: string) {
  return funnelColorMap[funnel as keyof typeof funnelColorMap].tailwind;
}

function isKnownFunnel(funnel: string) {
  return funnelColorMap[funnel as keyof typeof funnelColorMap] !== undefined;
}
</script>

<template>
  <div
    class="flex items-center justify-center rounded-[3.125rem] flex-shrink-0 px-1 py-0.5 w-10 h-6 label-2"
    :class="[computedClass, sizeClasses]">
    <span>
      {{ isKnownFunnel(props.funnel) ? props.funnel : "N/A" }}
    </span>
  </div>
</template>
