<template>
  <div class="construction-wrapper">
    <div class="construction-content">
      <div class="icon">🚧</div>
      <h1>Under Construction</h1>
      <p>This page is currently under construction. Please check back soon!</p>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.construction-wrapper {
  @apply flex justify-center items-center min-h-[300px] text-center p-5 rounded-lg;
}

.construction-content {
  @apply max-w-2xl;
}

.icon {
  @apply text-6xl mb-5 animate-bounce;
}

h1 {
  @apply text-4xl text-gray-800 mb-2.5;
}

p {
  @apply text-lg text-gray-600 leading-relaxed;
}
</style>
