<script setup lang="ts">
import { ref, watch, nextTick } from "vue";

defineProps<{
  title: string;
  icon?: string;
}>();

const isOpen = ref(false);
const contentRef = ref<HTMLElement | null>(null);
const contentHeight = ref("0px");

watch(isOpen, async (newValue) => {
  if (!contentRef.value) return;

  if (newValue) {
    await nextTick();
    contentHeight.value = `${contentRef.value.scrollHeight}px`;
  } else {
    contentHeight.value = `${contentRef.value.scrollHeight}px`;
    await nextTick();
    contentHeight.value = "0px";
  }
});
</script>

<template>
  <div class="accordion-container">
    <div class="accordion-header" @click="isOpen = !isOpen">
      <div class="flex items-center gap-2">
        <MaterialIcon v-if="icon" :icon="icon" size="16px" />
        <span>{{ title }}</span>
      </div>

      <MaterialIcon
        icon="expand_more"
        class="transition-transform duration-300"
        :class="{ 'rotate-180': isOpen }"
        size="16px"
      />
    </div>

    <div
      ref="contentRef"
      class="transition-all duration-300 overflow-hidden"
      :style="{ height: contentHeight }"
    >
      <div class="accordion-content-inner">
        <slot />
      </div>
    </div>
  </div>
</template>
<style scoped>
.accordion-container {
  @apply rounded-xl border border-primary-border shadow-md px-4 py-3 flex flex-col;
}

.accordion-header {
  @apply flex flex-row items-center justify-between gap-2 label-2 text-neutral-700 cursor-pointer;
}

.accordion-content-inner {
  @apply text-sm font-inter text-primary-text mt-4;
}
</style>
