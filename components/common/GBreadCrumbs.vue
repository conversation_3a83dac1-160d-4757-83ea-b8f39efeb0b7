<script setup lang="ts">
import { computed } from "vue";

export type GBreadcrumbItem = {
  label: string;
  to?: string;
  icon?: string;
};

interface Props {
  items: GBreadcrumbItem[];
  ariaLabel?: string;
  maxItems?: number;
}

const props = withDefaults(defineProps<Props>(), {
  ariaLabel: "Breadcrumb navigation",
  maxItems: 3,
});

const visibleItems = computed((): GBreadcrumbItem[] => {
  if (props.items.length <= props.maxItems) {
    return props.items;
  }

  const result: GBreadcrumbItem[] = [];

  result.push(props.items[0]);

  result.push({
    label: "...",
  });

  result.push(...props.items.slice(-2));

  return result;
});
</script>

<template>
  <nav :aria-label="ariaLabel" role="navigation" class="select-none">
    <ol class="flex items-center gap-2">
      <li
        v-for="(item, index) in visibleItems"
        :key="`${index}-${item.label}`"
        class="flex items-center gap-2"
      >
        <MaterialIcon
          v-if="index > 0"
          icon="chevron_right"
          size="1rem"
          aria-hidden="true"
        />

        <span class="flex items-center gap-2">
          <MaterialIcon
            v-if="item.icon"
            :icon="item.icon"
            size="1rem"
            aria-hidden="true"
          />

          <NuxtLink
            v-if="item.to"
            :to="item.to"
            class="body-1 hover:underline focus:underline focus:outline-none line-clamp-1"
          >
            {{ item.label }}
          </NuxtLink>
          <span v-else class="body-1 line-clamp-1" aria-current="page">
            {{ item.label }}
          </span>
        </span>
      </li>
    </ol>
  </nav>
</template>
