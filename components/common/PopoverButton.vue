<script setup lang="ts">
defineProps<{
  label?: string;
  variant?: "outline" | "default";
  size?: "small" | "medium" | "large";
  position?: "top" | "bottom" | "left" | "right";
  prefixIcon?: string;
  prefixIconConfig?: {
    class?: string;
    size?: string;
  };
  suffixIcon?: string;
  suffixIconConfig?: {
    class?: string;
    size?: string;
  };
}>();

const variantClassMap = {
  outline: " px-4 py-2 rounded-lg border border-default-border",
  default: "",
};

const sizeMap = {
  small: "h-8",
  medium: "h-10",
  large: "h-12",
};

const popoverRef = ref();

function togglePopover(event: MouseEvent) {
  popoverRef.value.toggle(event);
}
</script>

<template>
  <Popover
    ref="popoverRef"
    :position="position ?? 'bottom'"
    pt:content:class="px-0 py-2"
    pt:root:class="border border-default-border rounded-lg shadow-popover select-none"
  >
    <slot />
  </Popover>

  <button
    class="flex items-center gap-2"
    :class="[variantClassMap[variant ?? 'default'], sizeMap[size ?? 'medium']]"
    @click="togglePopover"
  >
    <slot name="button-content">
      <MaterialIcon
        v-if="prefixIcon"
        :icon="prefixIcon"
        :class="prefixIconConfig?.class"
        :size="prefixIconConfig?.size ?? '1rem'"
      />
      <span v-if="label" class="label-3">{{ label }}</span>
      <MaterialIcon
        v-if="suffixIcon"
        :icon="suffixIcon"
        :class="suffixIconConfig?.class"
        :size="suffixIconConfig?.size ?? '1rem'"
      />
    </slot>
  </button>
</template>
