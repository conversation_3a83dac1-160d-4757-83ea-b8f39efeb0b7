<script setup lang="ts">
const robClient = useRob();
const toast = useToast();
const { user } = useUserSession();

const props = defineProps<{
  label?: string;
  disabled?: boolean;
  metadata?: Omit<FileUploadMetadata, "ownerType" | "ownerId">;
}>();

const emit = defineEmits<{
  (e: "fileUploaded", attachment: ReferencedResource): void;
  (e: "uploadDone"): void;
}>();

const agencyStore = useAgencyStore();

const handleUpload = async (event: any) => {
  if (!agencyStore.activeAgency) {
    console.warn("No active agency found");
    return;
  }

  try {
    const files = await Promise.all(
      event.files.map(async (file: any) => {
        if (file.objectURL) {
          return await getFileFromObjectURL(file.objectURL, file.name);
        }
        return file;
      })
    );

    const responses = (await Promise.allSettled(
      files.map(async (file, i) => [
        await robClient.uploadFile(file, {
          ownerType: "USER",
          ownerId: user.value!.sub,
          knowledge: false,
          ...props.metadata,
          agencyId: agencyStore.activeAgency!.id,
        }),
        i,
      ])
    )) as PromiseSettledResult<[FileUploadResponse, number]>[];

    handleUploadSuccess(files, responses);
    handleUploadError(responses);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Upload failed",
      detail: "Failed to upload file",
      life: 3000,
    });
    console.error(error);
  }

  emit("uploadDone");
};

const handleUploadSuccess = (
  files: any,
  responses: PromiseSettledResult<[FileUploadResponse, number]>[]
) => {
  const attachments = responses
    .filter((response) => response.status === "fulfilled")
    .map((response) => {
      const [fileUploadResponse, index] = response.value;

      return {
        key: fileUploadResponse.fileId,
        displayName: files[index as number].name,
        referenceId: fileUploadResponse.fileId,
      } as ReferencedResource;
    });

  if (attachments.length > 0) {
    toast.add({
      severity: "success",
      summary: "Upload successful",
      detail: `${attachments.length} files uploaded successfully`,
      life: 3000,
    });
  }

  attachments.forEach((attachment) => {
    emit("fileUploaded", attachment);
  });
};

const handleUploadError = (
  responses: PromiseSettledResult<[FileUploadResponse, number]>[]
) => {
  const errors = responses.filter((response) => response.status === "rejected");

  if (errors.length > 0) {
    toast.add({
      severity: "error",
      summary: "Upload failed",
      detail: `${errors.length} files failed to upload`,
      life: 3000,
    });
  }
};

const getFileFromObjectURL = async (objectURL: string, name: string) => {
  const response = await fetch(objectURL);
  const blob = await response.blob();
  return new File([blob], name, { type: blob.type });
};
</script>

<template>
  <FileUpload
    unstyled
    pt:input:class="!hidden"
    :max-file-size="10_000_000"
    :auto="true"
    :multiple="true"
    custom-upload
    @select="handleUpload"
  >
    <template #header="{ chooseCallback }">
      <button
        class="flex flex-row gap-2 items-center text-center body-2"
        :disabled="disabled"
        @click="chooseCallback"
      >
        <MaterialIcon icon="upload" size="1.15rem" />
        <span>{{ label || "Select from computer" }}</span>
      </button>
    </template>
  </FileUpload>
</template>
