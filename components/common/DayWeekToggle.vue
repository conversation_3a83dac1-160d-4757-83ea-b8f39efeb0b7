<script setup lang="ts">
const model = defineModel<"day" | "week">({ default: "day" });
</script>

<template>
  <div class="root">
    <span class="toggle-label">Weekly</span>

    <div
      class="toggle-switch-root"
      :data-checked="model === 'day'"
      @click="model = model === 'day' ? 'week' : 'day'"
    >
      <div class="toggle-switch-slider">
        <div class="toggle-switch-handle"></div>
      </div>
    </div>

    <span class="toggle-label">Daily</span>

    <MaterialIcon
      v-tooltip="'AMC metrics may vary due to privacy thresholds'"
      icon="info"
      class="text-neutral-400"
      size="16px"
    />
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-row items-center w-fit gap-2;
}
.toggle-label {
  @apply body-1 text-secondary-text;
}

.toggle-switch-root {
  @apply w-6 h-[14px] rounded-full cursor-pointer p-1;
  background: linear-gradient(180deg, #404040 0%, #262626 100%);
}

.toggle-switch-slider {
  @apply w-full h-full relative;
}

.toggle-switch-handle {
  @apply h-full aspect-square bg-white rounded-full absolute left-0 top-1/2 -translate-y-1/2 transition-all duration-200;
}

.toggle-switch-root[data-checked="true"] .toggle-switch-handle {
  @apply left-full -translate-x-full;
}
</style>
