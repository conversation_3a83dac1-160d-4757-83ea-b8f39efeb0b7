<script lang="ts">
import { WEEK_CONFIG } from "~/constants/weekRangePickerPresets";

export { WEEK_CONFIG };
</script>

<script setup lang="ts">
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

import {
  startOfWeek,
  addDays,
  subWeeks,
  endOfWeek,
  format,
  startOfDay,
  isSameDay,
  addMilliseconds,
} from "date-fns";
import {
  DEFAULT_WEEK_RANGE_PRESETS,
  type PresetDate,
} from "~/constants/weekRangePickerPresets";

type DateRange = {
  start: Date;
  end: Date;
};

type DateMultiRange = [[Date, Date], [Date, Date]] | [[Date, Date]];

type DateDayRange = [Date] | [Date, Date];

type DatePickerModel = DateDayRange | DateMultiRange;

const props = withDefaults(
  defineProps<{
    maxDate?: Date;
    minDate?: Date;
    presetDates?: PresetDate[];
    startOffset?: number;
    endOffset?: number;
    selectionMode?: "day" | "week";
  }>(),
  {
    presetDates: () => DEFAULT_WEEK_RANGE_PRESETS,
    selectionMode: "day",
    startOffset: 0,
    endOffset: 24 * 60 * 60 * 1000 - 1, // 23:59:59
  }
);

const model = defineModel<DateRange>({
  required: false,
  default: () => ({
    start: subWeeks(startOfWeek(new Date(), WEEK_CONFIG), 2),
    end: endOfWeek(subWeeks(new Date(), 1), WEEK_CONFIG),
  }),
});

const datePickerModel = ref<DatePickerModel>(
  buildDatePickerModel(model.value, props.selectionMode)
);

const computedPresetDates = computed(() => {
  return props.presetDates.map((preset) => {
    const isSelected = isPresetSelected(preset);
    return {
      ...preset,
      style: {
        ...preset.style,
        "background-color": isSelected
          ? "var(--tertiary-background, #f5f5f5)"
          : "transparent",
        "font-weight": isSelected ? "500" : "400",
        color: isSelected
          ? "var(--primary-text, #171717)"
          : "var(--primary-text, #171717)",
      },
    };
  });
});

function isPresetSelected(preset: PresetDate): boolean {
  const presetStart = startOfWeek(preset.value[0], WEEK_CONFIG);
  const presetEnd = endOfWeek(preset.value[1], WEEK_CONFIG);
  const modelStart = startOfWeek(model.value.start, WEEK_CONFIG);
  const modelEnd = endOfWeek(model.value.end, WEEK_CONFIG);

  return isSameDay(presetStart, modelStart) && isSameDay(presetEnd, modelEnd);
}

function buildDatePickerModel(
  range: DateRange,
  selectionMode: "day" | "week"
): DatePickerModel {
  if (selectionMode === "day") {
    return [range.start, range.end];
  }

  const start = startOfWeek(range.start, WEEK_CONFIG);
  const end = applyOffsetWithModifiers(range.end, props.endOffset);

  const firstRange: [Date, Date] = [start, addDays(start, 6)];
  const secondRange: [Date, Date] = [
    applyOffsetWithModifiers(addDays(end, -6), props.startOffset),
    applyOffsetWithModifiers(end, props.endOffset),
  ];

  return [firstRange, secondRange];
}

function formatDate(date: DatePickerModel): string {
  const stringFormat = "MMM d, yyyy";
  const range = getRange(date);
  return `${format(range.start, stringFormat)} - ${format(
    range.end,
    stringFormat
  )}`;
}

function daySingleSelection(date: [Date]): DateRange {
  const [start] = date;
  return { start, end: start };
}

function dayMultiSelection(date: [Date, Date]): DateRange {
  const [start, end] = date;
  return { start, end };
}

function weekSingleSelection(date: [Date, Date]): DateRange {
  const [start, end] = date;
  return { start, end };
}

function weekMultiSelection(date: [[Date, Date], [Date, Date]]): DateRange {
  const [firstRange, secondRange] = date;
  return {
    start: firstRange[0],
    end: secondRange[1],
  };
}

function normalizeDate(date: DatePickerModel): DateRange {
  if (!Array.isArray(date)) throw new Error("Unsupported date format");

  const [first, second] = date;
  if (first instanceof Date) {
    return second instanceof Date
      ? dayMultiSelection([first, second])
      : daySingleSelection([first]);
  }

  if (Array.isArray(first)) {
    return Array.isArray(second) && !!second.length
      ? weekMultiSelection([first, second])
      : weekSingleSelection(first);
  }

  throw new Error("Unsupported date format");
}

function applyOffsetWithModifiers(
  date: Date,
  offset: number,
  modifiers?: ((date: Date) => Date)[]
): Date {
  return [
    ...(modifiers ?? []),
    startOfDay,
    (date: Date) => addMilliseconds(date, offset),
  ].reduce((date, modifier) => modifier(date), date);
}

function dayRangeModifiers(dateRange: DateRange): DateRange {
  const { start, end } = dateRange;

  return {
    start: applyOffsetWithModifiers(start, props.startOffset),
    end: applyOffsetWithModifiers(end, props.endOffset),
  };
}

function weekRangeModifiers(dateRange: DateRange): DateRange {
  const { start, end } = dateRange;

  return {
    start: applyOffsetWithModifiers(start, props.startOffset, [
      (date) => startOfWeek(date, WEEK_CONFIG),
    ]),
    end: applyOffsetWithModifiers(end, props.endOffset, [
      (date) => endOfWeek(date, WEEK_CONFIG),
    ]),
  };
}

function getRange(date: DatePickerModel): DateRange {
  const range = normalizeDate(date);
  return props.selectionMode === "day"
    ? dayRangeModifiers(range)
    : weekRangeModifiers(range);
}

function rangeEquals(range1: DateRange, range2: DateRange): boolean {
  return (
    range1.start.getTime() === range2.start.getTime() &&
    range1.end.getTime() === range2.end.getTime()
  );
}

watch(datePickerModel, () => {
  const newRange = getRange(datePickerModel.value);
  if (!rangeEquals(newRange, model.value)) {
    model.value = newRange;
  }
});

// Track the previous selection mode to detect changes
const previousSelectionMode = ref(props.selectionMode);

watch(
  () => [model.value, props.selectionMode] as const,
  ([newValue, selectionMode]) => {
    const currentRange = getRange(datePickerModel.value);
    const sameRange = rangeEquals(currentRange, newValue);
    const selectionModeChanged = previousSelectionMode.value !== selectionMode;

    // Always update if selection mode changed, otherwise only update if range changed
    if (!selectionModeChanged && sameRange) return;

    datePickerModel.value = buildDatePickerModel(newValue, selectionMode);
    previousSelectionMode.value = selectionMode;
  },
  { deep: true }
);
</script>

<template>
  <VueDatePicker
    v-model="datePickerModel"
    :week-picker="selectionMode === 'week'"
    range
    multi-calendars
    exact-match
    prevent-min-max-navigation
    six-weeks="fair"
    :week-start="1"
    :max-date="maxDate"
    :min-date="minDate"
    :preset-dates="computedPresetDates"
    :enable-time-picker="false"
    :clearable="false"
    :format="(date: any) => formatDate(date)"
    class="week-range-picker"
    style="
      --dp-border-radius: 0.5rem;
      --dp-cell-border-radius: 1rem;
      --dp-cell-size: 2rem;
      --dp-cell-padding: 0;
      --dp-font-size: 0.875rem;
      --dp-font-family: 'Inter', sans-serif;
      --dp-primary-color: #e5e5e5;
      --dp-text-color: #171717;
      --dp-disabled-color: rgba(23, 23, 23, 0.5);
      --dp-two-calendars-spacing: 1.5rem;
    "
  >
    <template #dp-input="{ value }">
      <button
        class="flex flex-row items-center justify-center h-8 px-4 py-2 gap-2 rounded-lg border border-default-border bg-white w-auto max-w-fit shrink-0"
      >
        <MaterialIcon icon="calendar_today" size="1rem" />
        <span class="label-3 text-primary-text line-clamp-1">{{ value }}</span>
        <MaterialIcon icon="arrow_drop_down" size="1rem" />
      </button>
    </template>
    <template #action-row="{ selectDate, closePicker }">
      <div class="flex flex-row items-center justify-end gap-3 w-full">
        <button
          class="action-button text-primary-text bg-secondary-background"
          @click="closePicker"
        >
          Clear
        </button>
        <button
          class="action-button text-white bg-gradient-to-b from-neutral-700 to-neutral-800 shadow-inner shadow-neutral-500"
          @click="selectDate"
        >
          Done
        </button>
      </div>
    </template>
  </VueDatePicker>
</template>
<style scoped>
/* VueDatePicker width control */
.week-range-picker {
  @apply inline-flex w-auto max-w-fit;
}

/* Override library's input container styles */
.week-range-picker :deep(.dp__input_wrap) {
  @apply w-auto max-w-fit inline-flex;
}

.week-range-picker :deep(.dp__input) {
  @apply w-auto max-w-fit;
}

.week-range-picker :deep(.dp__range_between) {
  @apply bg-neutral-200 text-neutral-900 rounded-none border-0;
}

.week-range-picker :deep(.dp__range_between_week) {
  @apply bg-neutral-200 text-neutral-900 rounded-none border-0;
}

.week-range-picker :deep(.dp__range_end),
.week-range-picker :deep(.dp__range_start),
.week-range-picker :deep(.dp__active_date) {
  @apply bg-tertiary-background text-primary-text;
}

.week-range-picker :deep(.dp__today) {
  @apply bg-[#DD4227] text-white border-0;
}

.week-range-picker :deep(.dp__today.dp__cell_disabled) {
  @apply bg-[#DD4227]/20 border-0;
}

.week-range-picker :deep(.dp__range_start) {
  @apply border-l-2;
}

.week-range-picker :deep(.dp__range_end) {
  @apply border-r-2;
}

.week-range-picker :deep(.dp__cell_auto_range),
.week-range-picker :deep(.dp__cell_auto_range_start),
.week-range-picker :deep(.dp__cell_auto_range_end) {
  @apply border-t border-b border-l-0 border-r-0 border-dashed border-default-border;
}

.week-range-picker :deep(.dp__cell_auto_range_start) {
  @apply border-l;
}

.week-range-picker :deep(.dp__cell_auto_range_end) {
  @apply border-r;
}

/* Remove right border radius from last cell of a row if next row starts with range_between */
.week-range-picker
  :deep(
    .dp__calendar_row:has(
        + .dp__calendar_row .dp__calendar_item:first-child .dp__range_between
      )
  )
  .dp__calendar_item:last-child
  .dp__cell_inner {
  @apply !rounded-tr-none !rounded-br-none;
}

/* Remove left border radius from first cell of a row if previous row ends with range_between */
.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__calendar_item:first-child .dp__range_between)
      + .dp__calendar_row
      .dp__calendar_item:last-child
      .dp__cell_inner
  ) {
  @apply !rounded-tl-none !rounded-bl-none;
}

/* Handle adjacent rows with range_between_week - remove right radius from last cell if next row starts with range_between_week */
.week-range-picker
  :deep(
    .dp__calendar_row:has(
        + .dp__calendar_row
          .dp__calendar_item:first-child
          .dp__range_between_week
      )
  )
  .dp__calendar_item:last-child
  .dp__cell_inner {
  @apply !rounded-tr-none !rounded-br-none;
}

/* Handle adjacent rows with range_between_week - remove left radius from first cell if previous row ends with range_between_week */
.week-range-picker
  :deep(
    .dp__calendar_row:has(
        .dp__calendar_item:first-child .dp__range_between_week
      )
      + .dp__calendar_row
      .dp__calendar_item:last-child
      .dp__cell_inner
  ) {
  @apply !rounded-tl-none !rounded-bl-none;
}

/* Also handle when last cell of row has range_between_week and next row's first cell has range_between_week */
.week-range-picker
  :deep(
    .dp__calendar_row:has(
        .dp__calendar_item:last-child .dp__range_between_week
      ):has(
        + .dp__calendar_row
          .dp__calendar_item:first-child
          .dp__range_between_week
      )
  )
  .dp__calendar_item:last-child
  .dp__cell_inner {
  @apply !rounded-tr-none !rounded-br-none;
}

.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__calendar_item:last-child .dp__range_between_week)
      + .dp__calendar_row:has(
        .dp__calendar_item:first-child .dp__range_between_week
      )
      .dp__calendar_item:first-child
      .dp__cell_inner
  ) {
  @apply !rounded-tl-none !rounded-bl-none;
}

/* Handle non-adjacent rows with range_between_week */
/* Remove right border radius from last cell of any row with range_between_week that has another range_between_week row somewhere below */
.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__range_between_week):has(
        ~ .dp__calendar_row .dp__range_between_week
      )
      .dp__calendar_item:last-child
      .dp__cell_inner
  ) {
  @apply !rounded-tr-none !rounded-br-none;
}

/* Remove left border radius from first cell of any row with range_between_week that has another range_between_week row somewhere above */
.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__range_between_week)
      ~ .dp__calendar_row:has(.dp__range_between_week)
      .dp__calendar_item:first-child
      .dp__cell_inner
  ) {
  @apply !rounded-tl-none !rounded-bl-none;
}

/* Ensure proper corner radius for multi-row selections */
/* First row first cell: only top-left radius when there are rows below */
.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__range_between_week):has(
        ~ .dp__calendar_row .dp__range_between_week
      )
      .dp__calendar_item:first-child
      .dp__cell_inner
  ) {
  @apply rounded-tl-[1rem] rounded-tr-none rounded-bl-none rounded-br-none;
}

/* Last row last cell: only bottom-right radius when there are rows above */
.week-range-picker
  :deep(
    .dp__calendar_row:has(.dp__range_between_week)
      ~ .dp__calendar_row:has(.dp__range_between_week)
      .dp__calendar_item:last-child
      .dp__cell_inner
  ) {
  @apply rounded-tl-none rounded-tr-none rounded-bl-none rounded-br-[1rem];
}

/* Add box shadow to the outer menu wrapper */
.week-range-picker :deep(.dp__menu) {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}

.week-range-picker :deep(.dp__arrow_top) {
  @apply !hidden;
}

.action-button {
  @apply flex h-10 w-[6.625rem] px-4 py-2 justify-center items-center rounded-lg;
}
</style>
