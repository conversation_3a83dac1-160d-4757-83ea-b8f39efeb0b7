<script setup lang="ts">
interface EntityData {
  label: string | undefined;
  image: string | undefined | null;
}

const props = defineProps<{
  entityId: string;
  entityType: RoleScope;
  labelClass?: string;
  shape?: "square" | "circle" | "rounded";
  size?: "normal" | "large" | "xlarge";
}>();

const advertiserStore = useAdvertiserStore();
const agencyStore = useAgencyStore();
const userSession = useUserSession();

const entityData = computed<EntityData | undefined>(() => {
  switch (props.entityType) {
    case "AGENCY":
      return getAgencyData();
    case "ADVERTISER":
      return getAdvertiserData();
    case "USER":
      return getUserData();
  }
  return undefined;
});

function getAgencyData() {
  const agency = agencyStore.getAgencyById(props.entityId);
  return {
    label: agency?.name,
    image: `/images/agency/${props.entityId}`,
  };
}

function getAdvertiserData() {
  const advertiser = advertiserStore.getAdvertiserById(props.entityId);
  return {
    label: advertiser?.amazonDspAdvertiserName,
    image: `/images/advertiser/${props.entityId}`,
  };
}

function getUserData() {
  return {
    label: userSession.user?.value?.fullName,
    image: undefined,
  };
}

</script>
<template>
  <div class="flex flex-row items-center gap-4">
    <FallbackAvatar
      :image="entityData?.image"
      :label="entityData?.label"
      :shape="shape"
      :size="size"
    />

    <span v-if="entityData?.label" :class="labelClass" class="body-1">{{
      entityData.label
    }}</span>
  </div>
</template>
