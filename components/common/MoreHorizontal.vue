<script setup lang="ts">
interface Item {
  label: string;
  icon: string;
  itemClass?: string;
  onClick: (data: any) => void;
}

const props = defineProps<{
  items: Item[];
  actionData?: any;
}>();

const popover = ref();

function handleClick(item: Item) {
  if (item.onClick) {
    item.onClick(props.actionData);
  }
  popover.value.hide();
}
</script>

<template>
  <i
    class="material-symbols-outlined cursor-pointer select-none"
    @click="if (items.length > 0) popover.toggle($event);"
  >
    more_horiz
  </i>

  <Popover ref="popover" pt:content:class="p-0">
    <ul class="flex flex-col py-2">
      <li v-for="item in items" :key="item.label">
        <button
          class="flex w-full items-center gap-4 px-4 py-2 hover:bg-neutral-100"
          :class="item.itemClass"
          @click="handleClick(item)"
        >
          <i class="material-symbols-outlined" style="font-size: 1.25rem">
            {{ item.icon }}
          </i>
          <span class="text-sm">{{ item.label }}</span>
        </button>
      </li>
    </ul>
  </Popover>
</template>
