<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, nextTick } from "vue";

const props = withDefaults(
  defineProps<{
    label?: string;
    data: T;
    height?: number;
    defaultContent?: string;
    fieldPath?: string;
  }>(),
  {
    height: 170,
  }
);

const emit = defineEmits<{
  (e: "save", data: T): void;
  (e: "cancel", data: T): void;
  (e: "cancel-completed", data: T): void;
  (e: "update:data", data: T): void;
}>();

const editableData = ref<T>(JSON.parse(JSON.stringify(props.data)));
const isFieldFocused = ref(false);
const isFieldTouched = ref(false);

const hasChanges = computed(() => {
  return JSON.stringify(editableData.value) !== JSON.stringify(props.data);
});

const showButtons = computed(() => {
  return isFieldFocused.value || hasChanges.value;
});

// Check if current content is default content
const isDefaultContent = computed(() => {
  if (!props.defaultContent || !props.fieldPath) return false;

  // Get the field value using the field path
  const fieldValue = getNestedValue(editableData.value, props.fieldPath);
  return !isFieldTouched.value && fieldValue === props.defaultContent;
});

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  return path.split(".").reduce((current, key) => current?.[key], obj);
}

watch(
  () => props.data,
  (newData) => {
    editableData.value = JSON.parse(JSON.stringify(newData));
  }
);

watch(editableData, () => {
  emit("update:data", editableData.value);
});

onMounted(() => {
  emit("update:data", editableData.value);
});

function onCancel() {
  editableData.value = JSON.parse(JSON.stringify(props.data));
  isFieldFocused.value = false;
  emit("cancel", editableData.value);
  // Reset touched state after data restoration to ensure proper color calculation
  nextTick(() => {
    isFieldTouched.value = false;
    emit("cancel-completed", editableData.value);
  });
}

function onSave() {
  isFieldFocused.value = false;
  emit("save", editableData.value);
}

function onFocus() {
  isFieldFocused.value = true;
  isFieldTouched.value = true; // Mark as touched when focused
}

function onBlur() {
  // Keep buttons visible if there are unsaved changes
  if (!hasChanges.value) {
    isFieldFocused.value = false;
  }
}

function onInput() {
  isFieldTouched.value = true; // Mark as touched when input changes
}
</script>

<template>
  <div class="flex flex-col items-start gap-2 w-full max-w-[488px]">
    <div
      class="flex flex-col items-start gap-2 self-stretch"
      :style="{ height: `${height}px` }"
    >
      <div class="flex flex-col items-start gap-2">
        <h3 v-if="label" class="label-3 text-primary-text">{{ label }}</h3>
      </div>

      <div
        class="flex flex-row items-start gap-2 flex-1 w-full bg-white border border-neutral-300 rounded-lg focus-within:border-neutral-500 focus-within:shadow-sm transition-colors"
        @focusin="onFocus"
        @focusout="onBlur"
      >
        <slot
          :data="editableData"
          :hasChanges="hasChanges"
          :isDefaultContent="isDefaultContent"
          :onInput="onInput"
        />
      </div>
    </div>

    <div
      v-if="showButtons"
      class="flex items-center justify-between w-full h-8"
    >
      <span v-if="hasChanges" class="caption-2 text-disabled-text">
        You have unsaved changes
      </span>

      <div class="flex gap-3 ml-auto">
        <button
          @click="onCancel"
          class="flex items-center justify-center px-4 py-2 h-8 label-3 text-neutral-900 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50"
        >
          Cancel
        </button>
        <button
          :disabled="!hasChanges"
          class="flex items-center justify-center px-4 py-2 h-8 label-3 min-w-[127px] text-white rounded-lg disabled:text-neutral-200 disabled:bg-neutral-500 disabled:cursor-not-allowed"
          :style="{
            borderRadius: '8px',
            background: hasChanges
              ? 'linear-gradient(180deg, #404040 0%, #262626 100%)'
              : '',
          }"
          @click="onSave"
        >
          Save changes
        </button>
      </div>
    </div>
  </div>
</template>
