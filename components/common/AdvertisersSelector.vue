<script setup lang="ts">
import Select, { type SelectProps } from "primevue/select";

const props = defineProps<{
  variantPt?: SelectProps["pt"];
  singleAccount?: boolean;
}>();

const emit = defineEmits<{
  (e: "change", value: string[]): void;
}>();

const advertiserFilterStore = useAdvertiserFilterStore();
advertiserFilterStore.setSingleAccount(props.singleAccount || false);

const isLoading = computed(() => {
  return advertiserFilterStore.status === "loading";
});

watch(
  () => advertiserFilterStore.value,
  (newValue) => {
    emit("change", newValue);
  },
  { immediate: true }
);

defineExpose({
  advertiserIdsScope: computed(() => advertiserFilterStore.value),
  isLoading,
});
</script>

<template>
  <Select
    :pt="variantPt"
    v-model="advertiserFilterStore.value"
    :options="advertiserFilterStore.options"
    option-label="label"
    option-value="value"
    empty-message="No accounts found"
    loading-message="Loading accounts..."
    :loading="isLoading"
  />
</template>
