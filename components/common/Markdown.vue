<script setup lang="ts">
import { MarkdownProcessor } from "@/utils/markdown-processor";
import { computedAsync } from "@vueuse/core";

const props = defineProps<{
  content: string;
  breaklines?: boolean;
}>();

const processor = computed(() => {
  return new MarkdownProcessor({
    breaklines: props.breaklines,
    sanitize: true,
  });
});

const processedContent = computedAsync(async () => {
  return await processor.value.process(props.content);
});
</script>

<template>
  <ClientOnly>
    <div
      class="prose dark:prose-invert max-w-none prose-neutral dark:prose-neutral"
      v-enhance-table
      v-html="processedContent"
    ></div>
    <template #fallback>
      <div
        class="prose dark:prose-invert max-w-none prose-neutral dark:prose-neutral"
        v-html="processedContent"
      ></div>
    </template>
  </ClientOnly>
</template>
