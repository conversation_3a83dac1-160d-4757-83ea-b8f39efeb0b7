<script setup lang="ts" generic="T">
type Placement = `${"top" | "bottom"}-${"left" | "right"}`;
const props = defineProps<{
  label?: string;
  description?: string;
  data: T;
  placement?: Placement;
  placementClass?: string;
}>();

const emit = defineEmits<{
  (e: "save", data: T): void;
  (e: "cancel", data: T): void;
  (e: "update:data", data: T): void;
}>();

const xPlacement = computed(() => props.placement?.split("-")[1]);
const yPlacement = computed(() => props.placement?.split("-")[0]);

const editableData = ref<T>(JSON.parse(JSON.stringify(props.data)));
const hasChanges = computed(() => {
  return JSON.stringify(editableData.value) !== JSON.stringify(props.data);
});

watch(
  () => props.data,
  (newData) => {
    editableData.value = JSON.parse(JSON.stringify(newData));
  }
);

watch(editableData, () => {
  emit("update:data", editableData.value);
});

onMounted(() => {
  emit("update:data", editableData.value);
});

function onCancel() {
  editableData.value = JSON.parse(JSON.stringify(props.data));
  emit("cancel", editableData.value);
}

function onSave() {
  emit("save", editableData.value);
}
</script>

<template>
  <div class="flex flex-1 flex-col gap-3 relative">
    <h3 v-if="label" class="section-title">{{ label }}</h3>
    <p v-if="description" class="section-description">{{ description }}</p>

    <div class="flex flex-1 flex-col gap-2">
      <slot :data="editableData" :hasChanges="hasChanges" />
    </div>

    <Transition name="fade">
      <div
          v-show="hasChanges"
          class="flex flex-row absolute bg-neutral-50 rounded-lg overflow-clip border border-neutral-300"
          :class="[
          {
            'right-0': xPlacement === 'right',
            'left-0': xPlacement === 'left',
            'translate-y-[calc(100%+0.5rem)] bottom-0': yPlacement === 'bottom',
            'top-0': yPlacement === 'top',
          },
          placementClass,
        ]"
      >
        <MaterialIcon
            icon="check"
            class="text-green-500 py-1 px-2 hover:bg-neutral-100"
            size="1rem"
            @click="onSave"
        />
        <MaterialIcon
            icon="undo"
            size="1rem"
            class="py-1 px-2 hover:bg-neutral-100"
            @click="onCancel"
        />
      </div>
    </Transition>
  </div>
</template>
<style scoped>
.section-title {
  @apply label-3 text-neutral-800;
}

.section-description {
  @apply text-neutral-700 text-sm;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
