<script setup lang="ts" generic="T">
import { ref, computed } from "vue";

export type GCarouselProps<T> = {
  items: T[];
  state?: "loading" | "success" | "error";
  errorMessage?: string;
  loadingMessage?: string;
  emptyMessage?: string;
  emptyIcon?: string;
};

const props = withDefaults(defineProps<GCarouselProps<T>>(), {
  state: "success",
  emptyIcon: "image",
});

const currentIndex = ref(0);
const itemList = ref<HTMLElement>();

const isSingleItem = computed(() => props.items.length <= 1);
const isEmpty = computed(() => props.items.length === 0);

// Validate state and fallback to error for unknown states
const validatedState = computed(() => {
  const validStates = ["loading", "success", "error"];
  return validStates.includes(props.state) ? props.state : "error";
});

const scrollToIndex = (index: number) => {
  if (!itemList.value || isSingleItem.value) return;

  const itemWidth = itemList.value.clientWidth;
  itemList.value.scrollTo({
    left: index * itemWidth,
    behavior: "smooth",
  });

  currentIndex.value = index;
};

const goToPrevious = () => {
  if (isSingleItem.value) return;
  const newIndex =
    currentIndex.value > 0 ? currentIndex.value - 1 : props.items.length - 1;
  scrollToIndex(newIndex);
};

const goToNext = () => {
  if (isSingleItem.value) return;
  const newIndex =
    currentIndex.value < props.items.length - 1 ? currentIndex.value + 1 : 0;
  scrollToIndex(newIndex);
};
</script>

<template>
  <div class="root">
    <!-- Error State - first priority and fallback -->
    <div v-if="validatedState === 'error'" class="state-container">
      <div class="state-content">
        <div
          class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto"
        >
          <MaterialIcon icon="error_outline" class="text-red-500 text-xl" />
        </div>
        <p class="label-3 text-gray-700">
          {{ errorMessage || "An error occurred" }}
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else-if="validatedState === 'loading'" class="state-container">
      <div class="state-content">
        <div
          class="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto"
        ></div>
        <p class="label-3 text-gray-700">
          {{ loadingMessage || "Loading..." }}
        </p>
      </div>
    </div>

    <!-- Empty State - automatically shown when items array is empty -->
    <div v-else-if="isEmpty" class="state-container">
      <div class="state-content">
        <div
          class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto"
        >
          <MaterialIcon :icon="emptyIcon" class="text-gray-400 text-xl" />
        </div>
        <p class="label-3 text-gray-700">
          {{ emptyMessage || "No data available" }}
        </p>
      </div>
    </div>

    <!-- Carousel Content -->
    <div v-else-if="validatedState === 'success'" class="viewport">
      <div ref="itemList" class="item-list">
        <div class="item" v-for="(item, index) in items" :key="index">
          <div class="item-content">
            <slot :item="item" :index="index" />
          </div>
        </div>
      </div>

      <div class="item-list-overlay">
        <button
          class="nav-button"
          :class="{ 'nav-button-disabled': isSingleItem }"
          :disabled="isSingleItem"
          @click="goToPrevious"
        >
          <MaterialIcon
            icon="chevron_left"
            size="2rem"
            class="text-white drop-shadow-lg"
          />
        </button>

        <button
          class="nav-button"
          :class="{ 'nav-button-disabled': isSingleItem }"
          :disabled="isSingleItem"
          @click="goToNext"
        >
          <MaterialIcon
            icon="chevron_right"
            size="2rem"
            class="text-white drop-shadow-lg"
          />
        </button>

        <div class="dot-list" v-if="!isSingleItem">
          <div
            class="dot"
            :class="{ 'dot-active': index === currentIndex }"
            v-for="(item, index) in items"
            :key="index"
            @click="scrollToIndex(index)"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.root {
  @apply w-full h-full border border-default-border rounded-xl bg-secondary-background;
}

.state-container {
  @apply w-full h-full flex justify-center items-center text-gray-600;
}

.state-content {
  @apply text-center space-y-3;
}

.viewport {
  @apply relative overflow-hidden w-full h-full rounded-xl;
}

.item-list {
  @apply flex flex-row h-full overflow-x-hidden;
  scroll-snap-type: x mandatory;
}

.item {
  @apply flex-shrink-0 w-full h-full;
  scroll-snap-align: start;
}

.item-content {
  @apply w-full h-full flex items-center justify-center;
}

.item-list-overlay {
  @apply absolute top-0 left-0 w-full h-full;
}

.nav-button {
  @apply absolute top-1/2 -translate-y-1/2 flex items-center justify-center p-1 rounded-full hover:bg-white/50 transition-opacity;
}

.nav-button:first-of-type {
  left: 0.85rem;
}

.nav-button:last-of-type {
  right: 0.85rem;
}

.nav-button-disabled {
  @apply opacity-30 cursor-not-allowed;
}

.nav-button-disabled:hover {
  @apply bg-transparent;
}

.dot-list {
  @apply absolute bottom-6 right-6 flex items-center justify-center gap-2;
}

.dot {
  @apply w-2 h-2 rounded-full cursor-pointer transition-all bg-white/80;
}

.dot-active {
  @apply w-8;
}
</style>
