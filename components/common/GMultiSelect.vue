<script setup lang="ts" generic="T extends { label: string; value: any, description?: string }">
import { onUnmounted } from "vue";

export type GMultiSelectProps<T> = {
  label: string;
  prefixIcon?: string;
  suffixIcon?: string;
  maxSelection: number;
  options: T[];
  clearable?: boolean;
  showSearch?: boolean;
  showTooltips?: boolean;
  
  filterCallback?: (option: T, filterValue: string) => boolean;
};

const props = withDefaults(defineProps<GMultiSelectProps<T>>(), {
  filterCallback: (option: T, filterValue: string) =>
    option.label.toLowerCase().includes(filterValue.toLowerCase()),
  clearable: false,
  showSearch: true,
  showTooltips: false,
});

const selectRef = ref();
const searchInputRef = ref();

const modelValue = defineModel<string[]>({ default: [] });

const internalValue = ref([...modelValue.value]);

const options = computed(() =>
  props.options.sort((a, b) =>
    modelValue.value.includes(a.value) ? -1 : 1
  )
);

const filterValue = ref("");
const filteredOptions = computed(() => {
  if (!props.showSearch || !filterValue.value) {
    return options.value;
  }
  return options.value.filter((option) =>
    props.filterCallback(option, filterValue.value)
  );
});

watch(
  modelValue,
  (newValue) => {
    internalValue.value = [...newValue];
  },
  { immediate: true }
);

const hasChanged = computed(() => {
  return (
    JSON.stringify(internalValue.value.sort()) !==
    JSON.stringify(modelValue.value.sort())
  );
});

function applySelection() {
  modelValue.value = [...internalValue.value];
  selectRef.value.hide();
}

function onShow() {
  if (props.showSearch) {
    focusSearchInput();
    searchInputRef.value?.addEventListener("blur", focusSearchInput);
  }
}

function onHide() {
  if (searchInputRef.value) {
    searchInputRef.value.removeEventListener("blur", focusSearchInput);
  }

  filterValue.value = "";
}

function focusSearchInput() {
  if (searchInputRef.value) {
    searchInputRef.value.focus();
  }
}

function clearSelection() {
  internalValue.value = [];
  applySelection();
}

onUnmounted(() => {
  if (searchInputRef.value) {
    searchInputRef.value.removeEventListener("blur", focusSearchInput);
  }
});
</script>

<template>
  <MultiSelect
    ref="selectRef"
    :options="filteredOptions"
    v-model="internalValue"
    :selection-limit="maxSelection"
    option-label="label"
    option-value="value"
    :pt="{
      labelContainer: { class: '!hidden' },
      root: { class: 'outline-none border-none w-fit shrink-0' },
      dropdown: { class: 'flex-1 w-full' },
      overlay: {
        class:
          'mt-2 w-[19.5rem] pt-2 rounded-xl bg-white border border-default-border shadow-lg',
      },
      listContainer: { class: '!max-h-[400px] p-0' },
      option: { class: 'px-4 py-2' },
    }"
    @show="onShow()"
    @hide="onHide()"
  >
    <template #dropdownicon>
      <div class="button text-primary">
        <MaterialIcon v-if="prefixIcon" :icon="prefixIcon" size="20px" />
        <span class="label-3">{{ label }}</span>

        <MaterialIcon v-if="clearable && modelValue.length > 0" icon="clear" size="16px" @click="clearSelection()" clickable/>
        <MaterialIcon v-else-if="suffixIcon" :icon="suffixIcon" size="16px" />
      </div>
    </template>

    <template #header v-if="showSearch">
      <div class="search-container">
        <MaterialIcon icon="search" class="search-icon" size="16px" />
        <input
          ref="searchInputRef"
          id="metrics-pool-selector-search"
          v-model="filterValue"
          type="text"
          class="search-input"
          placeholder="Search metrics..."
        />
      </div>
    </template>

    <template #option="{ option }">
      <div class="option-container">
        <span class="option">{{ option.label }}</span>
        <MaterialIcon
          v-if="showTooltips"
          v-tooltip="option.description"
          icon="info"
          class="text-disabled-text"
          size="16px"
        />
      </div>
    </template>

    <template #footer>
      <div class="footer-container">
        <span
          v-if="internalValue.length === maxSelection"
          class="selection-limit-text"
        >
          Maximum amount of metrics selected
        </span>

        <div class="footer-actions">
          <button class="button text-primary label-3" @click="selectRef.hide()">
            Cancel
          </button>
          <button
            class="button bg-neutral-900 disabled:bg-neutral-500 disabled:cursor-not-allowed text-white label-3"
            :disabled="!hasChanged"
            @click="applySelection()"
          >
            Apply
          </button>
        </div>
      </div>
    </template>
  </MultiSelect>
</template>

<style scoped>
.button {
  @apply h-8 flex-1 flex flex-row items-center justify-center gap-2 px-4 py-2 border border-default-border rounded-lg select-none;
}

.option-container {
  @apply flex-1 flex flex-row items-center gap-2 justify-between h-8;
}

.option {
  @apply flex-1 body-1 text-wrap line-clamp-2 max-w-32;
}

.search-input {
  @apply w-full h-full border border-default-border rounded-lg focus:border-primary outline-none pl-8 pr-3 py-1;
}

.search-icon {
  @apply absolute top-1/2 left-2 -translate-y-1/2 text-disabled-text;
}

.search-container {
  @apply flex-1 relative flex flex-row items-center gap-2 justify-between mx-4 my-2;
}

.footer-container {
  @apply flex-1 flex flex-col items-center gap-2 justify-between p-4;
}

.footer-actions {
  @apply flex-1 flex flex-row items-center gap-2 justify-between w-full;
}

.selection-limit-text {
  @apply caption-2 text-orange-600;
}
</style>
