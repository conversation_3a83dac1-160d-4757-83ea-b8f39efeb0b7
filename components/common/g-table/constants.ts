import type { DataTableProps } from "primevue/datatable";

export const defaultPt: DataTableProps["pt"] = {
  headerRow: {
    class: "!bg-primary-background label-3",
  },
  tableContainer: {
    class:
      "w-full border-t border-l border-r border-default-border rounded-t-lg",
  },
  pcPaginator: {
    paginatorContainer: {
      class: "border-b border-l border-r border-default-border rounded-b-lg",
    },
  },
} as const;

export const borderlessPt: DataTableProps["pt"] = {
  headerRow: {
    class: "bg-white label-3",
  },
  tableContainer: {
    class: "",
  },
  pcPaginator: {
    paginatorContainer: {
      class: "border-none",
    },
  },
} as const;
