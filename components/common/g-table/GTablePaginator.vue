<script setup lang="ts">
import type { DataTableSlots } from "primevue/datatable";

const props = withDefaults(
  defineProps<{
    first: number;
    last: number;
    totalRecords: number;
    rowsPerPageOptions?: number[];
    page: number;
    pageCount: number;
    rowChangeCallback: (rows: number) => void;
    firstPageCallback: () => void;
    prevPageCallback: () => void;
    nextPageCallback: () => void;
    lastPageCallback: () => void;
  }>(),
  {
    rowsPerPageOptions: () => [10, 20, 50, 100],
  }
);

const rows = ref(props.rowsPerPageOptions[0]);

onMounted(() => {
  props.rowChangeCallback(rows.value);
});
</script>

<template>
  <div class="flex flex-row justify-end flex-1 px-6 pb-1 pt-3">
    <div class="flex flex-row items-center gap-4">
      <span class="caption-2">
        {{ first }} - {{ last }} of {{ totalRecords }}
      </span>

      <!-- Select results per page -->
      <Select
        v-model="rows"
        class="h-8 border-default-border !rounded-lg"
        pt:label:class="caption-2"
        :options="rowsPerPageOptions"
        @change="() => rowChangeCallback(rows)"
      >
        <template #value="slotProps">
          <span class="caption-2">Results per page: {{ slotProps.value }}</span>
        </template>

        <template #dropdownicon>
          <MaterialIcon icon="arrow_drop_down" class="text-primary-text" />
        </template>
      </Select>

      <span class="label-2">Page {{ page + 1 }} of {{ pageCount }}</span>

      <div class="flex flex-row items-center">
        <MaterialIcon
          :disabled="page === 0"
          icon="first_page"
          clickable
          @click="firstPageCallback"
        />

        <MaterialIcon
          :disabled="page === 0"
          icon="chevron_left"
          clickable
          @click="prevPageCallback"
        />

        <MaterialIcon
          :disabled="page === pageCount - 1"
          icon="chevron_right"
          clickable
          @click="nextPageCallback"
        />

        <MaterialIcon
          :disabled="page === pageCount - 1"
          icon="last_page"
          clickable
          @click="lastPageCallback"
        />
      </div>
    </div>
  </div>
</template>
