<script setup lang="ts">
import Column from "primevue/column";
import Skeleton from "primevue/skeleton";
import DataTable from "primevue/datatable";
import type { DataTableProps } from "primevue/datatable";

const props = defineProps<{
  rows: number;
  columns: number;
  columnFlexWidths?: number[];
  pt?: DataTableProps["pt"];
}>();

const data = computed(() =>
  Array(props.rows).map(() => ({
    id: crypto.randomUUID(),
  }))
);
</script>

<template>
  <DataTable
    :value="data"
    data-key="id"
    :pt="pt"
    style="--p-datatable-header-cell-background: transparent"
  >
    <Column v-for="(col, i) in columns" :key="i">
      <template #header>
        <Skeleton
          height="1.5rem"
          class="bg-neutral-200"
          :style="{
            width: `calc(${Math.random() * 100}%)`,
            minWidth: '30%',
            maxWidth: '100%',
          }"
        />
      </template>
      <template #body>
        <Skeleton
          :style="{
            width: `calc(${Math.random() * 100}%)`,
            minWidth: '30%',
            maxWidth: '100%',
          }"
          height="1.5rem"
          class="rounded-full bg-primary-background"
        />
      </template>
    </Column>
  </DataTable>
</template>
