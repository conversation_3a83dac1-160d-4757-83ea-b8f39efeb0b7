<script setup lang="ts" generic="T extends object">
import DataTable from "primevue/datatable";
import { borderlessPt, defaultPt } from "./constants";

export interface GTableProps<T extends object> {
  data: T[];
  dataKey: keyof T | Function;
  paginator?: boolean;
  filterFields?: (string | ((data: any) => string))[];
  emptyMessage?: string;
  header?: boolean;
  headerItemsTemplate?: string[];
  headerUserItems?: Component[];
  variant?: "default" | "borderless";
  loading?: boolean;
  scrollHeight?: string;
  rowsPerPageOptions?: number[];
}

defineOptions({
  inheritAttrs: false,
});

const props = withDefaults(defineProps<GTableProps<T>>(), {
  rows: 10,
  paginator: true,
  emptyMessage: "No items found",
  scrollHeight: "flex",
  rowsPerPageOptions: () => [10, 20, 50, 100],
});
const filterFields = computed(
  () =>
    props.filterFields ??
    (props.data.length > 0 ? Object.keys(props.data[0]) : [])
);
const filters = ref({
  global: { value: null, matchMode: "contains" },
});

const pt = computed(() =>
  props.variant === "borderless" ? borderlessPt : defaultPt
);
</script>

<template>
  <TableSkeleton v-if="loading" :rows="3" :columns="4" :pt="pt" />

  <DataTable
    v-else
    v-model:filters="filters"
    :globalFilterFields="filterFields"
    :value="data"
    :paginator="paginator"
    scrollable
    :scroll-height="scrollHeight"
    :pt="pt"
    style="--p-datatable-header-cell-background: white"
    v-bind="$attrs"
  >
    <template #header v-if="!!header">
      <GTableHeader
        v-model:searchFilter="filters.global.value"
        :items-template="headerItemsTemplate"
        :user-items="headerUserItems"
      />
    </template>

    <template #paginatorcontainer="slotProps: any">
      <GTablePaginator
        :first="slotProps.first"
        :last="slotProps.last"
        :totalRecords="slotProps.totalRecords"
        :rowsPerPageOptions="rowsPerPageOptions"
        :page="slotProps.page"
        :pageCount="slotProps.pageCount"
        :rowChangeCallback="slotProps.rowChangeCallback"
        :firstPageCallback="slotProps.firstPageCallback"
        :prevPageCallback="slotProps.prevPageCallback"
        :nextPageCallback="slotProps.nextPageCallback"
        :lastPageCallback="slotProps.lastPageCallback"
      />
    </template>

    <template #empty>
      <div class="flex items-center justify-center h-full">
        {{ emptyMessage }}
      </div>
    </template>
    <slot />
  </DataTable>
</template>
