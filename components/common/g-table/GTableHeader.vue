<script setup lang="tsx">
import InputText from "primevue/inputtext";
import I<PERSON><PERSON>ield from "primevue/iconfield";
import InputIcon from "primevue/inputicon";
import MaterialIcon from "~/components/common/MaterialIcon.vue";
import type { Component } from "vue";

const searchFilterModel = defineModel<string | null>("searchFilter");

const props = defineProps<{
  searchPlaceholder?: string;
  itemsTemplate?: string[];
  userItems?: Component[];
}>();

const itemsMaps = {
  search: (
    <IconField>
      <InputIcon>
        <MaterialIcon icon="search" size="1rem" class="text-disabled-text" />
      </InputIcon>
      <InputText
        v-model={searchFilterModel.value}
        size="small"
        placeholder={props.searchPlaceholder || "Search"}
        pt={{ root: { class: "rounded-lg border-default-border h-8" } }}
      />
    </IconField>
  ),
  spacer: <div class="flex-1"></div>,
  hello: <div>hello</div>,
};

const defaultItems = ["search", "spacer"];
const items = computed(() => {
  let itemsToMap = props.itemsTemplate || defaultItems;

  return itemsToMap.map((item) => {
    if (!(item in itemsMaps))
      throw new Error(`Item ${item} not found in itemsMaps`);
    return itemsMaps[item as keyof typeof itemsMaps];
  });
});
</script>

<template>
  <div class="flex items-center gap-4">
    <template v-for="item in items" :key="item.name">
      <component :is="item" />
    </template>
  </div>
</template>
