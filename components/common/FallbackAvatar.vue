<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    image?: string | null;
    label?: string;
    truncate?: boolean;
    shape?: "square" | "circle" | "rounded";
    size?: "normal" | "large" | "xlarge";
    class?: string;
  }>(),
  {
    truncate: true,
    shape: "rounded",
  }
);

const failedLoadingImage = ref(false);

// Reset failed state when image prop changes
watch(
  () => props.image,
  () => {
    failedLoadingImage.value = false;
  }
);

const displayLabel = computed(() => {
  if (!props.image || failedLoadingImage.value) {
    if (props.truncate) {
      return props.label?.slice(0, 2);
    }
    return props.label;
  }
  return undefined;
});

const displayImage = computed(() => {
  if (props.image && !failedLoadingImage.value) {
    return props.image;
  }
  return undefined;
});

function handleImageError() {
  failedLoadingImage.value = true;
}

const styleAndShape = computed(() => {
  if (props.shape === "rounded") {
    return ["rounded-lg overflow-clip", undefined] as const;
  }
  return ["''", props.shape] as const;
});
const [processedClass, processedShape] = styleAndShape.value;
</script>

<template>
  <Avatar
    :image="displayImage"
    :label="displayLabel"
    :shape="processedShape"
    :size="props.size || 'normal'"
    :class="props.class + ' ' + processedClass"
    @error="handleImageError"
  />
</template>
