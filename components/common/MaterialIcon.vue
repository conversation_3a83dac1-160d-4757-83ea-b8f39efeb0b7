<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    icon: string;
    size?: string;
    clickable?: boolean;
    disabled?: boolean;
  }>(),
  {
    size: "1.25rem",
    clickable: false,
    disabled: false,
  }
);

const emit = defineEmits<{
  (e: "click", event: MouseEvent): void;
}>();

function handleClick(event: MouseEvent) {
  if (props.disabled) return;
  emit("click", event);
}
</script>

<template>
  <i
    class="material-symbols-outlined select-none"
    :class="[
      { 'cursor-pointer': clickable },
      { '!text-disabled-text !cursor-not-allowed': disabled },
    ]"
    :style="{ fontSize: size }"
    :data-disabled="disabled"
    @click="handleClick"
  >
    {{ icon }}
  </i>
</template>
