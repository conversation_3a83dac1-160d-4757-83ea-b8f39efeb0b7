<script setup lang="ts">
import { ref, computed } from "vue";
import { format } from "date-fns";
import { useDebounceFn } from "@vueuse/core";
import type { CreateCampaignFormData } from "~/utils/forms/create-campaign";
import type {
  FormComponentEmits,
  FormComponentProps,
} from "~/types/form-component";

const CAMPAIGN_TYPE_OPTIONS = {
  streaming_tv: "Streaming TV",
  olv: "OLV",
  display: "Display",
};

const GOAL_OPTIONS = {
  awareness: "Awareness",
  consideration: "Consideration",
  conversions: "Conversions",
};

const KPI_OPTIONS = {
  roas: "Return on ad spend (ROAS)",
  total_roas: "Total ROAS (T-ROAS)",
  cpa: "Cost per action (CPA)",
  combined_roas: "Combined ROAS (C-ROAS)",
  cpsu: "Cost per sign up (CPSU)",
  cpfao: "Cost per first app open (CPFAO)",
  reach: "Reach",
  frequency: "Frequency",
  ctr: "Click-through rate (CTR)",
  cpc: "Cost per click (CPC)",
  cpvc: "Cost per video completion (CPVC)",
  vcr: "Vedio completion rate (VCR)",
  cpdpv: "Cost per detail page view (CPDPV)",
  dpvr: "Detail page view rate (DPVR)",
};

const props = defineProps<FormComponentProps<CreateCampaignFormData>>();

const emit = defineEmits<FormComponentEmits<CreateCampaignFormData>>();

const localFormData = ref<CreateCampaignFormData>(
  JSON.parse(JSON.stringify(props.initialData))
);

const totalBudget = computed(() => {
  const total = localFormData.value.flights.reduce(
    (total: number, flight: { budgetAmount: number }) => {
      return total + (flight.budgetAmount || 0);
    },
    0
  );

  return total.toLocaleString();
});

const earliestStartDate = computed(() => {
  const dates = localFormData.value.flights
    .map((flight) => flight.startDateTime)
    .filter((date) => date !== null && date !== undefined) // Filter out null/undefined
    .map((date) => {
      const d = new Date(date as string);
      return new Date(d.valueOf() + d.getTimezoneOffset() * 60 * 1000); // Adjust for local timezone offset
    })
    .filter((date) => !isNaN(date.getTime())); // Filter out invalid dates

  if (dates.length === 0) return "";

  const earliestDate = new Date(
    Math.min(...dates.map((date) => date.getTime()))
  );
  return format(earliestDate, "yyyy-MM-dd");
});

const latestEndDate = computed(() => {
  const dates = localFormData.value.flights
    .map((flight) => flight.endDateTime)
    .filter((date) => date !== null && date !== undefined) // Filter out null/undefined
    .map((date) => {
      const d = new Date(date as string);
      return new Date(d.valueOf() + d.getTimezoneOffset() * 60 * 1000); // Adjust for local timezone offset
    })
    .filter((date) => !isNaN(date.getTime())); // Filter out invalid dates

  if (dates.length === 0) return "";

  const latestDate = new Date(Math.max(...dates.map((date) => date.getTime())));
  return format(latestDate, "yyyy-MM-dd");
});

const updateDebounced = useDebounceFn(() => {
  emit("dataChange", localFormData.value);
}, 100);

watch(
  localFormData,
  () => {
    updateDebounced();
  },
  { deep: true, immediate: true }
);

function addFlight() {
  localFormData.value.flights.push({
    budgetAmount: 0,
    startDateTime: "",
    endDateTime: "",
  });
}

function removeFlight(index: number) {
  if (localFormData.value.flights.length > 1) {
    localFormData.value.flights.splice(index, 1);
  }
}
</script>

<template>
  <div class="campaign-form">
    <!-- Campaign Type Section -->
    <div class="form-option">
      <h3 class="section-title-2">Campaign Type</h3>
      <div class="radio-group">
        <label class="radio-option">
          <input
            v-model="localFormData.campaignType"
            type="radio"
            name="campaign_type"
            :value="CAMPAIGN_TYPE_OPTIONS.streaming_tv"
            :disabled="disabled"
          />
          <span class="radio-label">
            {{ CAMPAIGN_TYPE_OPTIONS.streaming_tv }}
          </span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.campaignType"
            type="radio"
            name="campaign_type"
            :value="CAMPAIGN_TYPE_OPTIONS.olv"
            :disabled="disabled"
          />
          <span class="radio-label">{{ CAMPAIGN_TYPE_OPTIONS.olv }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.campaignType"
            type="radio"
            name="campaign_type"
            :value="CAMPAIGN_TYPE_OPTIONS.display"
            :disabled="disabled"
          />
          <span class="radio-label">{{ CAMPAIGN_TYPE_OPTIONS.display }}</span>
        </label>
      </div>
    </div>

    <!-- Goal Section -->
    <div class="form-option">
      <h3 class="section-title-2">Goal</h3>
      <div class="radio-group">
        <label class="radio-option">
          <input
            v-model="localFormData.goal"
            type="radio"
            name="goal"
            :value="GOAL_OPTIONS.awareness"
            :disabled="disabled"
          />
          <span class="radio-label">{{ GOAL_OPTIONS.awareness }}</span>
        </label>
        <label class="radio-option">
          <input
            v-model="localFormData.goal"
            type="radio"
            name="goal"
            :value="GOAL_OPTIONS.consideration"
            :disabled="disabled"
          />
          <span class="radio-label">{{ GOAL_OPTIONS.consideration }}</span>
        </label>
        <label class="radio-option">
          <input
            v-model="localFormData.goal"
            type="radio"
            name="goal"
            :value="GOAL_OPTIONS.conversions"
            :disabled="disabled"
          />
          <span class="radio-label">{{ GOAL_OPTIONS.conversions }}</span>
        </label>
      </div>
    </div>

    <!-- KPI Section -->
    <div class="form-option">
      <h3 class="section-title-2">KPI</h3>

      <div class="radio-group kpi-radio-group">
        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.roas"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.roas }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.total_roas"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.total_roas }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.cpa"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.cpa }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.combined_roas"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.combined_roas }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.cpsu"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.cpsu }}</span>
        </label>

        <label class="radio-option">
          <input
            v-model="localFormData.kpi"
            type="radio"
            name="kpi"
            :value="KPI_OPTIONS.cpfao"
            :disabled="disabled"
          />
          <span class="radio-label">{{ KPI_OPTIONS.cpfao }}</span>
        </label>
      </div>
    </div>

    <!-- KPI Target Section -->
    <div class="form-option">
      <h3 class="section-title-2">KPI Target</h3>
      <input
        v-model="localFormData.kpiTarget"
        type="text"
        class="input-field-base kpi-target-input"
        placeholder="Enter your KPI target (e.g., 3.0, 50%, $2.50)"
        :disabled="disabled"
      />
    </div>

    <!-- Additional Context Section -->
    <div class="form-option">
      <h3 class="section-title-2">Additional context (required)</h3>
      <p class="section-description">
        Describe your audience, inventory, and product strategies in detail.
      </p>
      <textarea
        v-model="localFormData.additionalContext"
        rows="4"
        class="input-field-base context-textarea"
        placeholder="Describe your audience, inventory, and product strategies in detail."
        :disabled="disabled"
      />
    </div>

    <hr class="my-6 border-default-border" />

    <!-- Flights Section -->
    <div class="form-option">
      <h3 class="section-title">Flights</h3>

      <div class="flights-container">
        <div
          v-for="(flight, index) in localFormData.flights"
          :key="index"
          class="flight-row"
        >
          <div class="flight-field">
            <label class="flight-label">Budget</label>
            <div class="budget-input-wrapper">
              <span class="budget-prefix">$</span>
              <input
                v-model="localFormData.flights[index].budgetAmount"
                type="number"
                class="input-field-base flight-input !pl-6"
                placeholder="20,000"
                :disabled="disabled"
              />
            </div>
          </div>

          <div class="flight-field">
            <label class="flight-label">Start</label>
            <input
              v-model="localFormData.flights[index].startDateTime"
              type="date"
              class="input-field-base flight-input"
              :disabled="disabled"
            />
          </div>

          <div class="flight-field">
            <label class="flight-label">End</label>
            <input
              v-model="localFormData.flights[index].endDateTime"
              type="date"
              class="input-field-base flight-input"
              :disabled="disabled"
            />
          </div>

          <button
            v-if="localFormData.flights.length > 1 && props.disabled"
            type="button"
            @click="() => removeFlight(index)"
            class="remove-flight-btn"
          >
            <MaterialIcon icon="remove_circle_outline" size="16px" />
          </button>
        </div>
      </div>

      <button
        v-if="props.disabled"
        type="button"
        @click="addFlight"
        class="add-flight-btn mt-4"
      >
        <MaterialIcon icon="add" size="16px" />
        <p class="label-3">Add flight</p>
      </button>
    </div>

    <!-- Flight Total Section -->
    <div v-if="localFormData.flights.length > 1" class="form-option">
      <h3 class="section-title-2">Flight Total</h3>
      <div class="flight-total-row">
        <div class="total-field">
          <div class="budget-input-wrapper">
            <span class="budget-prefix">$</span>
            <input
              :value="totalBudget"
              readonly
              type="text"
              class="input-field-base flight-input !pl-6"
              :disabled="disabled"
            />
          </div>
        </div>
        <div class="total-field">
          <input
            :value="earliestStartDate"
            readonly
            type="date"
            class="input-field-base flight-input"
            :disabled="disabled"
          />
        </div>

        <div class="total-field">
          <input
            :value="latestEndDate"
            readonly
            type="date"
            class="input-field-base flight-input"
            :disabled="disabled"
          />
        </div>

        <!-- Spacer -->
        <div class="min-w-4"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.campaign-form {
  @apply flex flex-col gap-6 p-6 max-w-[41.125rem] mx-auto;
}

.form-option {
  @apply flex flex-col gap-2;
}

.section-title-2 {
  @apply label-3 text-primary-text;
}

.section-title {
  @apply header-1 text-secondary-text;
}

.section-description {
  @apply caption-2 text-disabled-text;
}

.radio-group {
  @apply flex gap-4;
}

.kpi-radio-group {
  @apply flex flex-wrap gap-4;
}

.kpi-radio-group .radio-option {
  @apply flex-none w-[calc(50%-0.5rem)];
}

.radio-option {
  @apply flex items-center gap-3 p-2 border border-default-border rounded-lg cursor-pointer hover:bg-surface-main transition-colors flex-1;
}

.radio-option input[type="radio"] {
  @apply sr-only;
}

.radio-option input[type="radio"]:checked + .radio-label {
  @apply body-1;
}

.radio-option:has(input[type="radio"]:checked) {
  @apply border-focus-border bg-surface-main;
}

.radio-label {
  @apply body-1 text-primary-text text-center w-full;
}

.input-field-base {
  @apply border border-default-border rounded-lg focus:outline-none focus:ring-1 focus:ring-focus-border;
}

.kpi-target-input {
  @apply w-full px-3 py-2;
}

.context-textarea {
  @apply body-1 w-full px-4 pt-3 pb-4 resize-none text-primary-text;
}

.flights-container {
  @apply flex flex-col gap-6;
}

.flight-row {
  @apply flex items-end gap-3;
}

.flight-field {
  @apply flex-1;
}

.flight-label {
  @apply block label-3 text-primary-text mb-1;
}

.budget-input-wrapper {
  @apply relative flex items-center;
}

.budget-prefix {
  @apply absolute left-3 text-primary-text pointer-events-none;
}

.flight-input {
  @apply w-full h-12 px-3 py-2;
}

.remove-flight-btn {
  @apply h-10 items-center justify-center rounded-full text-secondary-text hover:text-red-500 transition-colors;
}

.add-flight-btn {
  @apply inline-flex items-center gap-2 px-4 py-2 border border-default-border rounded-lg text-primary-text hover:bg-white transition-colors max-w-fit;
}

.flight-total-row {
  @apply flex items-center gap-3;
}

.total-field {
  @apply flex-1;
}
</style>
