<script setup lang="ts">
import type { GBreadcrumbItem } from "~/components/common/GBreadCrumbs.vue";

defineProps<{
  title?: string;
  breadcrumbItems?: GBreadcrumbItem[];
}>();
</script>

<template>
  <div
    class="flex border-b border-default-border h-16 justify-start items-center px-10 py-6 bg-neutral-50"
  >
    <SimpleHeaderTitle v-if="title && !breadcrumbItems" :title="title" />
    <GBreadCrumbs
      class="flex-1 max-w-[360px]"
      v-else-if="breadcrumbItems"
      :items="breadcrumbItems"
      :max-items="3"
    />

    <span
      class="self-stretch border-l border-default-border w-[1px] mx-4 h-[140%] -translate-y-0.5 first:hidden last:hidden"
    ></span>

    <slot />
  </div>
</template>

<style scoped></style>
