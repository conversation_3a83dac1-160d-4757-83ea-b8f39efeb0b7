<script setup lang="ts">
import { AttributionModel } from "~/utils/advertiser-datasets/metrics/types";
import type { FunnelStagesData } from "~/utils/advertiser-datasets/transformers";
import { RequirementsFactory } from "~/utils/advertiser-datasets";

const props = defineProps<{
  advertiserId: string;
  startDate: Date;
  endDate: Date;
  currencyCode: string;
}>();

type FunnelStage = {
  category: string;
  value: number;
  funnelStageStr: string;
};
export type { FunnelStage };

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const funnelData = ref<FunnelStagesData>({});

const hasRequiredProps = computed(() => {
  return (
    props.advertiserId && props.startDate && props.endDate && props.currencyCode
  );
});

const advertiserDatasetStore = useAdvertiserDatasetStore();
const funnelStagesRequirement = computed(() =>
  RequirementsFactory.createRequirement("funnel-stages", {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  })
);

const convertToPercentage = (num: number, max_range: number): number => {
  if (max_range <= 0 || num <= 0) {
    return 0;
  }
  const percentage = (num / max_range) * 100;

  // multiple by 100 so truncation get 2 points after decimal point
  return Math.trunc(percentage * 100) / 100;
};

const selectedAttributionModels = ref<ViewableOption<AttributionModel>[]>([
  { label: "Linear Touch", value: AttributionModel.LINEAR_TOUCH },
]);

const categories = [
  { label: "Spend", value: "spend" },
  { label: "Total sales", value: "total_sales" },
  { label: "CAC", value: "cac" },
];

const funnelStagesInOrder = ["STV", "TOF", "MOF", "BOF"];

const selectedCategory = ref(categories[0].value);

const processedFunnelData = computed(() => {
  if (!funnelData.value || Object.keys(funnelData.value).length === 0) {
    return [];
  }

  // Ensure we have a selected attribution model
  if (
    !selectedAttributionModels.value ||
    selectedAttributionModels.value.length === 0
  ) {
    return [];
  }

  const stages = funnelStagesInOrder.map((stageName) => {
    let value: number;

    const stageMetrics = funnelData.value[stageName];
    const selectedModel = selectedAttributionModels.value[0];

    if (!stageMetrics) {
      return {
        category: stageName,
        value: 0,
        percentage: 0,
      };
    }

    switch (selectedCategory.value) {
      case "spend":
        value = stageMetrics.cost as number;
        break;
      case "total_sales":
        value = stageMetrics[
          `${selectedModel.value}TotalProductSales`
        ] as number;
        break;
      case "cac":
        const ntbUsers = stageMetrics[
          `${selectedModel.value}NtbPurchaseUsers`
        ] as number;
        value = ntbUsers > 0 ? stageMetrics.cost / ntbUsers : 0;
        break;
      default:
        value = 0;
    }

    return {
      category: stageName,
      value,
      percentage: 0, // Will be calculated below
    };
  });

  // Calculate percentages only if not in CAC mode
  const totalValue = stages.reduce((sum, stage) => sum + stage.value, 0);
  return stages.map((stage) => ({
    ...stage,
    funnelStageStr: selectedCategory.value === "cac" 
      ? new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: props.currencyCode,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(stage.value)
      : `${convertToPercentage(stage.value, totalValue)}%`,
  }));
});

watch(
  [hasRequiredProps, funnelStagesRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";
    funnelData.value = {};

    try {
      const result = await advertiserDatasetStore.fulfillRequirement(
        funnelStagesRequirement.value
      );
      funnelData.value = result;

      state.value =
        Object.keys(funnelData.value).length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <FunnelCard
    v-model:attribution-models="selectedAttributionModels"
    title="Funnel by"
    :categories="categories"
    description="Compare Spend, CAC, and Sales across attribution models"
    info-tooltip="View Spend, CAC and Sales with different attribution models to value each part of the funnel appropriately."
    :data="processedFunnelData"
    :currency-code="currencyCode"
    :hide-values-column="selectedCategory === 'cac'"
    support-attribution-model="single"
    @category-change="selectedCategory = $event as string"
    :state="state"
  />
</template>
