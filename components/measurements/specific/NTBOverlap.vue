<script setup lang="ts">
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import type { NTBOverlapData } from "~/utils/advertiser-datasets/transformers";
import { CampaignTacticsSetDefinitions } from "../venn/constants";

const props = defineProps<{
  advertiserId: string;
  startDate: Date;
  endDate: Date;
}>();

const hasRequiredProps = computed(() => {
  return props.advertiserId && props.startDate && props.endDate;
});

const advertiserDatasetStore = useAdvertiserDatasetStore();

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<NTBOverlapData>([]);

const categories = [
  { label: "NTB Purchasers", value: "ntbUsers" },
  { label: "NTB Purchases", value: "ntbPurchases" },
  { label: "% of Total NTB Purchasers", value: "ntbPurchaserPercentage" },
  { label: "% of Total NTB Purchases", value: "ntbPurchasePercentage" },
];

const selectedCategory = ref<string>(categories[0].value);

const regionsData = computed(() => {
  return Object.entries(data.value).reduce((acc, [key, value]) => {
    acc[key] = value[selectedCategory.value as keyof typeof value] ?? 0;
    return acc;
  }, {} as Record<string, number>);
});

const ntbOverlapRequirement = computed(() =>
  RequirementsFactory.createRequirement("ntb-overlap", {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  })
);

watch(
  [hasRequiredProps, ntbOverlapRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";

    try {
      data.value = await advertiserDatasetStore.fulfillRequirement(
        ntbOverlapRequirement.value
      );
      state.value = Object.keys(data.value).length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <VennChartCard
    title="New to Brand Overlap by"
    description="Identify strategy combos that bring in NTB buyers"
    info-tooltip="Understand which strategies work together to increase the likelihood to obtain NTB purchasers.  Action this data by adjusting budget towards the campaigns that obtain higher NTB purchasers together."
    :state="state"
    :regions-data="regionsData"
    :core-set-definitions="CampaignTacticsSetDefinitions"
    :categories="categories"
    :selected-category="selectedCategory"
    :format-type="
      ['ntbPurchaserPercentage', 'ntbPurchasePercentage'].includes(
        selectedCategory
      )
        ? 'percentage'
        : 'default'
    "
    @category-change="selectedCategory = $event as string"
  />
</template>
