<script setup lang="ts">
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import type { BrandedSearchOverlapData } from "~/utils/advertiser-datasets/transformers";
import { CampaignTacticsSetDefinitions } from "../venn/constants";

const props = defineProps<{
  advertiserId: string;
  startDate: Date;
  endDate: Date;
}>();

const hasRequiredProps = computed(() => {
  return props.advertiserId && props.startDate && props.endDate;
});

const advertiserDatasetStore = useAdvertiserDatasetStore();

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<BrandedSearchOverlapData>([]);

const categories = [
  { label: "# of Searches", value: "brandedSearches" },
  { label: "% Influenced", value: "influencedSearchPercentage" },
];

const selectedCategory = ref<string>(categories[0].value);

const regionsData = computed(() => {
  return Object.entries(data.value).reduce((acc, [key, value]) => {
    acc[key] = value[selectedCategory.value as keyof typeof value] ?? 0;
    return acc;
  }, {} as Record<string, number>);
});

const brandedSearchOverlapRequirement = computed(() =>
  RequirementsFactory.createRequirement("branded-search-overlap", {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  })
);

watch(
  [hasRequiredProps, brandedSearchOverlapRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";

    try {
      data.value = await advertiserDatasetStore.fulfillRequirement(
        brandedSearchOverlapRequirement.value
      );
      state.value = Object.keys(data.value).length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <VennChartCard
    title="Branded Search Overlap by"
    description="See which strategies drive branded search and shift budget toward high-impact ones."
    info-tooltip="Understand which strategies work together to influence a branded search.  Action this data by ensuring there is proper budget for the strategies that have a higher influence."
    :state="state"
    :regions-data="regionsData"
    :core-set-definitions="CampaignTacticsSetDefinitions"
    :categories="categories"
    :selected-category="selectedCategory"
    :format-type="
      selectedCategory === 'influencedSearchPercentage'
        ? 'percentage'
        : 'default'
    "
    @category-change="selectedCategory = $event as string"
  />
</template>
