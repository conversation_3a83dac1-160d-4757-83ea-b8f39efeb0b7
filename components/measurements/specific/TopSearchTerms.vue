<script setup lang="ts">
import type { TopSearchTermsData } from "~/utils/advertiser-datasets/transformers";
import { RequirementsFactory } from "~/utils/advertiser-datasets";

const props = defineProps<{
  advertiserId: string;
  startDate: Date;
  endDate: Date;
}>();

const hasRequiredProps = computed(() => {
  return props.advertiserId && props.startDate && props.endDate;
});

const advertiserDatasetStore = useAdvertiserDatasetStore();

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<TopSearchTermsData>([]);

const topSearchTermsRequirement = computed(() =>
  RequirementsFactory.createRequirement("top-search-terms", {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  })
);

watch(
  [hasRequiredProps, topSearchTermsRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";

    try {
      data.value = await advertiserDatasetStore.fulfillRequirement(
        topSearchTermsRequirement.value
      );
      state.value = data.value.length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <TableCard
    title="Top Search Terms"
    description="Uncover what users search after ad exposure and prioritize presence on those terms."
    info-tooltip="Understand what terms are searched post funnel exposure.  Ensure the advertiser has high exposure on the most searched terms to capture purchases."
    :state="state"
    :loading="state === 'loading'"
    :data="data"
    data-key="searchTerm"
  >
    <Column header="Search Terms" class="max-w-[250px]">
      <template #body="{ data }">
        <div class="flex items-center justify-start gap-4">
          <FunnelPill :funnel="data.funnel" />

          <span v-tooltip="data.searchTerm" class="truncate">
            {{ data.searchTerm }}
          </span>
        </div>
      </template>
    </Column>
    <Column header="NTB Purchases" field="ntbPurchases" sortable />
    <Column header="NTB Purchase Users" field="ntbPurchaseUsers" sortable />
    <Column header="Purchases" field="purchases" sortable />
    <Column header="Purchase Users" field="purchaseUsers" sortable />
    <Column header="Search Users" field="searchUsers" sortable />
  </TableCard>
</template>

<style scoped>
/* Add top and bottom borders to all header cells */
:deep(.p-datatable-thead > tr > th) {
  box-shadow: inset 0 1px 0 0 #e9ecef, inset 0 -1px 0 0 #e9ecef;
}
</style>
