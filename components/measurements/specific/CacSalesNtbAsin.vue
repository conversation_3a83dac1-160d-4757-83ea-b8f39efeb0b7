<script setup lang="ts">
import { useAdvertiserDatasetStore } from "~/stores/advertiser-dataset-store";
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import type {
  CacSalesNtbAsinData,
  CacSalesNtbCategoryData,
} from "~/utils/advertiser-datasets/transformers";

const props = defineProps<{
  advertiserId: string;
  currencyCode: string;
  startDate: Date;
  endDate: Date;
}>();

const advertiserDatasetStore = useAdvertiserDatasetStore();

const hasRequiredProps = computed(() => {
  return (
    props.advertiserId && props.currencyCode && props.startDate && props.endDate
  );
});

const categories = [
  { label: "ASIN", value: "asin" },
  { label: "Category", value: "category" },
];

const selectedCategory = ref<string>(categories[0].value);

const cacSalesNtbAsinRequirement = computed(() => {
  const requirementType =
    selectedCategory.value === "asin"
      ? "cac-sales-ntb-asin"
      : "cac-sales-ntb-category";

  return RequirementsFactory.createRequirement(requirementType as any, {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  });
});

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<CacSalesNtbAsinData | CacSalesNtbCategoryData>([]);

watch(
  [hasRequiredProps, cacSalesNtbAsinRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";

    try {
      const result = await advertiserDatasetStore.fulfillRequirement(
        cacSalesNtbAsinRequirement.value
      );

      data.value = result;
      state.value = data.value.length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <TableCard
    title="CAC, Sales & NTB by"
    description="Spot top-performing ASINs with strong sales and low CAC to boost"
    info-tooltip="Identify high performing ASINs that have higher Total & NTB Product Sales in conjunction with lower CAC to promote within DSP and Sponsored campaigns."
    :categories="categories"
    :default-category="selectedCategory"
    :data="data as any"
    :data-key="selectedCategory === 'asin' ? 'asin' : 'category'"
    :state="state"
    @category-change="selectedCategory = $event as string"
  >
    <Column v-if="selectedCategory === 'asin'" header="ASIN" field="asin" />
    <Column header="Category" field="category" />
    <Column
      header="Total Product Sales"
      field="totalProductSales"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    >
      <template #body="{ data }">
        {{
          data.totalProductSales
            ? formatCurrency(data.totalProductSales, currencyCode)
            : "-"
        }}
      </template>
    </Column>
    <Column
      header="NTB Product Sales"
      field="ntbProductSales"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    >
      <template #body="{ data }">
        {{
          data.ntbProductSales
            ? formatCurrency(data.ntbProductSales, currencyCode)
            : "-"
        }}
      </template>
    </Column>
    <Column
      header="CAC"
      field="cac"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    >
      <template #body="{ data }">
        {{ data.cac ? formatCurrency(data.cac, currencyCode) : "-" }}
      </template>
    </Column>
  </TableCard>
</template>
<style scoped>
:deep(.text-right .p-datatable-column-header-content) {
  justify-content: flex-end;
}

/* Add top and bottom borders to all header cells */
:deep(.p-datatable-thead > tr > th) {
  box-shadow: inset 0 1px 0 0 #e9ecef, inset 0 -1px 0 0 #e9ecef;
}
</style>
