<script setup lang="ts">
import { useAdvertiserDatasetStore } from "@/stores/advertiser-dataset-store";
import { RequirementsFactory } from "@/utils/advertiser-datasets";
import type { PurchaseOverlapData } from "@/utils/advertiser-datasets/transformers";
import { CampaignTacticsSetDefinitions } from "@/components/measurements/venn/constants";

const props = defineProps<{
  advertiserId: string;
  startDate?: Date;
  endDate?: Date;
}>();

const advertiserDatasetStore = useAdvertiserDatasetStore();
const dataRequirement = computed(() =>
  RequirementsFactory.createRequirement("purchase-overlap", {
    advertiserId: props.advertiserId,
    startDate: props.startDate!,
    endDate: props.endDate!,
  })
);

const hasRequiredProps = computed(() => {
  return props.advertiserId && props.startDate && props.endDate;
});

const data = ref<PurchaseOverlapData>({});
const state = ref<"loading" | "success" | "error" | "empty">("loading");

const categories = [
  { label: "Users with Purchases", value: "purchaseUsers" },
  { label: "Total Users", value: "impressionUsers" },
  { label: "Purchases", value: "totalPurchases" },
  { label: "Purchase Rate", value: "purchaseRate" },
];
const selectedCategory = ref<string>(categories[0].value);

const regionsData = computed(() => {
  return Object.entries(data.value).reduce((acc, [key, value]) => {
    acc[key] = value[selectedCategory.value as keyof typeof value] ?? 0;
    return acc;
  }, {} as Record<string, number>);
});

watch(
  [hasRequiredProps, dataRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";
    try {
      const result = await advertiserDatasetStore.fulfillRequirement(
        dataRequirement.value
      );
      data.value = result;
      state.value = Object.keys(data.value).length > 0 ? "success" : "empty";
    } catch (error) {
      console.error(error);
      state.value = "error";
    }
  },
  { immediate: true }
);
</script>

<template>
  <VennChartCard
    title="Overlap Report by"
    description="See which strategies work well together to drive purchases and ensure retargeting is in place."
    info-tooltip="Understand which strategies work together to increase the likelihood to purchase.  Action this data by ensuring retargeting is properly implemented to increase purchase rates."
    :categories="categories"
    :default-category="selectedCategory"
    :regions-data="regionsData"
    :core-set-definitions="CampaignTacticsSetDefinitions"
    :state="state"
    :format-type="
      selectedCategory === 'purchaseRate' ? 'percentage' : 'default'
    "
    @category-change="selectedCategory = $event as string"
  >
  </VennChartCard>
</template>
