<script setup lang="ts">
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import type { CrossSellAsinsData } from "~/utils/advertiser-datasets/transformers";

const props = defineProps<{
  advertiserId: string;
  startDate: Date;
  endDate: Date;
}>();

const advertiserDatasetStore = useAdvertiserDatasetStore();

const hasRequiredProps = computed(() => {
  return props.advertiserId && props.startDate && props.endDate;
});

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<CrossSellAsinsData>([]);

const categories = [
  { label: "ASINs", value: "asin" },
  { label: "Category", value: "category" },
];

const selectedCategory = ref<string>(categories[0].value);

const crossSellAsinsRequirement = computed(() => {
  const requirementType =
    selectedCategory.value === "asin"
      ? "cross-sell-asins"
      : "cross-sell-category";

  return RequirementsFactory.createRequirement(requirementType as any, {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  });
});

watch(
  [hasRequiredProps, crossSellAsinsRequirement],
  async () => {
    if (!hasRequiredProps.value) return;

    state.value = "loading";

    try {
      data.value = await advertiserDatasetStore.fulfillRequirement(
        crossSellAsinsRequirement.value
      );
      state.value = data.value.length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <TableCard
    title="Cross Sell by"
    :description="[
      'Find high-overlap ASIN pairs to build targeted cross-sell campaigns',
      'Lifetime',
    ]"
    info-tooltip="Identify ASINs pairs which have a high cross-sell rate (overlap %) which is a data driven way to create cross-sell campaigns."
    :data="data"
    :state="state"
    :loading="state === 'loading'"
    :categories="categories"
    :default-category="selectedCategory"
    @category-change="selectedCategory = $event as string"
    data-key="key"
  >
    <Column
      v-if="selectedCategory === 'asin'"
      header="Lead ASIN"
      field="leadAsin"
    />
    <Column header="Lead Category" field="leadCategory" />
    <Column
      v-if="selectedCategory === 'asin'"
      header="Follow-up ASIN"
      field="followUpAsin"
    />
    <Column header="Follow-up Category" field="followUpCategory" />
    <Column
      header="Lead Purchases"
      field="leadPurchases"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    />
    <Column
      header="Follow-up Purchases"
      field="followUpPurchases"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    />
    <Column
      header="Overlap"
      field="overlap"
      :body-style="{ textAlign: 'right' }"
      header-class="text-right"
      sortable
    >
      <template #body="{ data }">
        {{ data.overlap ? `${data.overlap.toFixed(3)}%` : "N/A" }}
      </template>
    </Column>
  </TableCard>
</template>

<style scoped>
:deep(.text-right .p-datatable-column-header-content) {
  justify-content: flex-end;
}

/* Add top and bottom borders to all header cells */
:deep(.p-datatable-thead > tr > th) {
  box-shadow: inset 0 1px 0 0 #e9ecef, inset 0 -1px 0 0 #e9ecef;
}
</style>
