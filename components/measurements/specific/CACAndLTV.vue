<script setup lang="ts">
import { useAdvertiserDatasetStore } from "~/stores/advertiser-dataset-store";
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import type { CacLtvData } from "~/utils/advertiser-datasets/transformers";

const props = defineProps<{
  advertiserId?: string;
  startDate?: Date;
  endDate?: Date;
  currencyCode?: string;
}>();

const advertiserDatasetStore = useAdvertiserDatasetStore();
const cacLtvRequirement = computed(() =>
  RequirementsFactory.createRequirement("cac-ltv", {
    advertiserId: props.advertiserId!,
    startDate: props.startDate!,
    endDate: props.endDate!,
  })
);

const cacLtvData = ref<CacLtvData>();

const dataKey = ref(crypto.randomUUID());
const state = ref<"loading" | "success" | "error" | "empty">("loading");

const hasRequiredProps = computed(() => {
  return (
    props.advertiserId && props.startDate && props.endDate && props.currencyCode
  );
});

watch(
  [hasRequiredProps, cacLtvRequirement],
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";

    try {
      const result = await advertiserDatasetStore.fulfillRequirement(
        cacLtvRequirement.value
      );
      cacLtvData.value = result;
      state.value = cacLtvData.value.cac.length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }

    dataKey.value = crypto.randomUUID(); // force re-render
  },
  { immediate: true }
);

const data = computed(() => {
  if (!cacLtvData.value) {
    return { labels: [], datasets: [] };
  }

  const labels = cacLtvData.value.cac.map((item) => item.label);
  const datasets = [
    {
      label: "CAC",
      data: cacLtvData.value.cac.map((item) => item.value),
      backgroundColor: "rgba(255, 99, 132, 0.2)",
      borderColor: "rgba(255, 99, 132, 1)",
      borderWidth: 1,
    },
  ];
  return { labels, datasets };
});

const ltvPlugin = computed(() => {
  if (cacLtvData.value?.ltv) {
    const plugin = createHorizontalLinePlugin({
      value: cacLtvData.value.ltv,
      color: "#736CFF",
      label: "LTV",
      labelTextColor: "#736CFF",
      labelBorderColor: "rgba(115, 108, 255, 0.3)",
      formatValue: (value: number) =>
        formatCurrency(value, props.currencyCode) || `$${value.toFixed(2)}`,
      autoAdjustScale: true,
      scalePadding: 0.1,
    });

    return [plugin];
  }
  return [];
});

const chartOptions = computed(() => {
  return {
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function (value: any) {
            return formatCurrency(value as number, props.currencyCode);
          },
        },
      },
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return formatCurrency(context.parsed.y, props.currencyCode);
          },
        },
      },
    },
  };
});
</script>

<template>
  <BarChartCard
    :key="dataKey"
    title="CAC and LTV"
    description="Customer Acquisition Cost & Lifetime Value"
    info-tooltip="View CAC and LTV with a 5 year lookback to determine if the amount you are spending to acquire a customer is more or less than the value a customer brings back to your brand.  If CAC > LTV, this is unprofitable, if LTV > CAC, this is profitable."
    :data="data"
    :options="chartOptions"
    :plugins="ltvPlugin"
    :state="state"
  />
</template>
