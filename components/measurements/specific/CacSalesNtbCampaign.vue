<script setup lang="ts">
import { useAdvertiserDatasetStore } from "~/stores/advertiser-dataset-store";
import { RequirementsFactory } from "~/utils/advertiser-datasets";
import { AttributionModel } from "~/utils/advertiser-datasets/metrics/types";
import type { CacSalesNtbCampaignData } from "~/utils/advertiser-datasets/transformers";
import { formatCurrency } from "~/utils/currencyUtils";

const METRICS: ViewableOption<string>[] = [
  { label: "Total Product Sales", value: "TotalProductSales" },
  { label: "Ntb Product Sales", value: "NtbProductSales" },
  { label: "Cac", value: "Cac" },
];

const props = defineProps<{
  advertiserId: string;
  currencyCode: string;
  startDate: Date;
  endDate: Date;
}>();

const hasRequiredProps = computed(() => {
  return (
    props.advertiserId && props.currencyCode && props.startDate && props.endDate
  );
});

const advertiserDatasetStore = useAdvertiserDatasetStore();
const dataRequirement = computed(() =>
  RequirementsFactory.createRequirement("cac-sales-ntb-campaign", {
    advertiserId: props.advertiserId,
    startDate: props.startDate,
    endDate: props.endDate,
  })
);

const state = ref<"loading" | "success" | "error" | "empty">("loading");
const data = ref<CacSalesNtbCampaignData>([]);
const selectedAttributionModels = ref<ViewableOption<string>[]>([
  { label: "Linear Attribution", value: AttributionModel.LINEAR_TOUCH },
]);

watch(
  hasRequiredProps,
  async () => {
    if (!hasRequiredProps.value) {
      return;
    }

    state.value = "loading";
    data.value = [];

    try {
      const result = await advertiserDatasetStore.fulfillRequirement(
        dataRequirement.value
      );

      data.value = result;
      state.value = result.length > 0 ? "success" : "empty";
    } catch (error) {
      state.value = "error";
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<template>
  <TableCard
    title="CAC, Sales & NTB by Campaign"
    description="Compare campaign performance & evaluate by funnel stage"
    info-tooltip="Identify higher and lower performing campaigns to adjust budget your budget across the campaigns.  Use different attribution models to evaluate each funnel appropriately."
    :data="data"
    data-key="campaignId"
    :state="state"
    support-attribution-model="multiple"
    v-model:attribution-models="selectedAttributionModels"
  >
    <ColumnGroup type="header">
      <Row>
        <Column header="Campaign" :rowspan="2" />

        <template v-for="metric in METRICS">
          <Column
            :header="`${metric.label}`"
            :colspan="selectedAttributionModels.length"
            class="text-center"
            :pt="{
              headerCell: {
                class: 'group-start',
              },
            }"
          />
        </template>
      </Row>

      <Row>
        <template v-for="_ in METRICS">
          <template
            v-for="(attributionModel, index) in selectedAttributionModels"
          >
            <Column
              :header="`${attributionModel.label}`"
              class="!label-2 text-right"
              :pt="{
                headerCell: {
                  class: index === 0 ? 'group-start' : '',
                },
              }"
            />
          </template>
        </template>
      </Row>
    </ColumnGroup>

    <Column style="width: 50%; min-width: 400px">
      <template #body="{ data }">
        <div class="flex items-center justify-start gap-4">
          <FunnelPill class="label-2" :funnel="data.campaignMetadata.funnel" />
          <span class="body-1">{{ data.campaignMetadata.campaignName }}</span>
        </div>
      </template>
    </Column>

    <template v-for="metric in METRICS">
      <template v-for="(attributionModel, index) in selectedAttributionModels">
        <Column
          :field="`${attributionModel.value}${metric.value}`"
          :body-style="{ textAlign: 'right' }"
          header-class="text-right"
          sortable
          :class="{
            'group-start': index === 0,
          }"
          :style="{
            minWidth: '100px',
          }"
        >
          <template #body="{ data }">
            {{
              data[`${attributionModel.value}${metric.value}`]
                ? formatCurrency(
                    data[`${attributionModel.value}${metric.value}`],
                    currencyCode
                  )
                : "-"
            }}
          </template>
        </Column>
      </template>
    </template>
  </TableCard>
</template>

<style scoped lang="postcss">
:deep(.text-right .p-datatable-column-header-content) {
  justify-content: flex-end;
}

:deep(.text-center .p-datatable-column-header-content) {
  justify-content: center;
}

/* Ensure header cells have proper background for shadow visibility */
:deep(.p-datatable-thead > tr > th) {
  @apply bg-white !border-t-0 !border-b-0;
}

/* Left borders for first column of each metric group (except the first group) */
:deep(.p-datatable-thead > tr > th.group-start),
:deep(.p-datatable-tbody > tr > td.group-start) {
  box-shadow: inset 1px 0 0 0 #e9ecef;
}

/* Add top and bottom borders to header cells with group-start */
:deep(.p-datatable-thead > tr > th.group-start) {
  box-shadow: inset 1px 0 0 0 #e9ecef, inset 0 1px 0 0 #e9ecef,
    inset 0 -1px 0 0 #e9ecef;
}

/* Add top and bottom borders to all header cells */
:deep(.p-datatable-thead > tr > th) {
  box-shadow: inset 0 1px 0 0 #e9ecef, inset 0 -1px 0 0 #e9ecef;
}

/* Override for group-start headers to include left border */
:deep(.p-datatable-thead > tr > th.group-start) {
  box-shadow: inset 1px 0 0 0 #e9ecef, inset 0 1px 0 0 #e9ecef,
    inset 0 -1px 0 0 #e9ecef;
}
</style>
