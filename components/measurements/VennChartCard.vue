<script setup lang="ts">
import { Chart, LinearScale, Tooltip, type ChartOptions } from "chart.js";
import { VennDiagramController, ArcSlice, type ISet } from "chartjs-chart-venn";
import { merge } from "chart.js/helpers";

import type { BaseCardProps } from "@/components/measurements/BaseCard.vue";
import type { Prettify } from "@/types/utils";
import type { VennChartDataLabelsOptions } from "@/types/chart";

import type {
  VennCoreSetDefinitions,
  VennRegionsData,
  SelectedCoreSet,
} from "@/components/measurements/venn/types";
import {
  generateMasksGosperInts,
  projectPowerSet,
  getMixedColorsArray,
  multiplyColorsMixer,
  generatePowerSet,
} from "@/components/measurements/venn/utils";
import * as constants from "@/components/measurements/venn/constants";

const nuxtApp = useNuxtApp();

Chart.register(
  VennDiagramController,
  ArcSlice,
  LinearScale,
  Tooltip,
  nuxtApp.$VennDataLabelsPlugin
);

type VennChartCardProps = {
  coreSetDefinitions: VennCoreSetDefinitions;
  regionsData: VennRegionsData;
  options?: Prettify<ChartOptions<"venn">>;
  maxSelection?: number;
  formatType?: "percentage" | "default";
} & Omit<BaseCardProps, "titleIcon">;

const props = defineProps<VennChartCardProps>();

const chartRef = ref<HTMLCanvasElement | null>(null);
const chartInstance = ref();

const _maxSelection = computed(() =>
  [
    props.maxSelection ?? Infinity,
    constants.COLOR_PALETTE.length,
    Object.keys(props.regionsData).length,
  ].reduce((acc, curr) => Math.min(acc, curr), Infinity)
);

const selectedSets = ref<SelectedCoreSet[]>([]);

watch(
  [() => props.regionsData, () => props.maxSelection],
  () => {
    updateSelectedSetsOnCategoryChange();
  },
  { immediate: true }
);

watch(
  [selectedSets, () => props.options],
  async () => {
    await nextTick();
    destroyChart();
    await buildChart();
  },
  { deep: true, flush: "post" }
);

onBeforeUnmount(() => {
  destroyChart();
});

async function waitForCanvasRef(): Promise<boolean> {
  let retries = 0;
  const maxRetries = 10;

  while (!chartRef.value && retries < maxRetries) {
    await nextTick();
    retries++;
  }

  if (!chartRef.value) {
    console.warn("Canvas ref is not available after retries");
    return false;
  }

  return true;
}

function updateSelectedSetsOnCategoryChange() {
  const newSelectedSets: SelectedCoreSet[] = [];
  const currentSelectedKeys = new Set(
    selectedSets.value.map((item) => item.key)
  );

  // Preserve existing valid selections and their colors
  for (const selectedSet of selectedSets.value) {
    if (props.coreSetDefinitions[selectedSet.key]) {
      newSelectedSets.push(selectedSet);
    }
  }

  // Identify available colors from the palette
  const usedColors = new Set(
    newSelectedSets.map((item) => item.colorConfig.chartColor)
  );
  const availableColors = constants.COLOR_PALETTE.filter(
    (colorTuple) => !usedColors.has(colorTuple[constants.CHART_COLOR_INTENSITY])
  );

  // Add new sets from coreSetDefinitions up to _maxSelection
  let colorIndex = 0;
  for (const key of Object.keys(props.coreSetDefinitions)) {
    if (newSelectedSets.length >= _maxSelection.value) {
      break; // Reached max selection
    }
    if (!currentSelectedKeys.has(key)) {
      if (colorIndex < availableColors.length) {
        const colorTuple = availableColors[colorIndex];
        newSelectedSets.push({
          key,
          colorConfig: {
            chartColor: colorTuple[constants.CHART_COLOR_INTENSITY],
            legendColor: colorTuple[constants.LEGEND_COLOR_INTENSITY],
            legendAccentColor:
              colorTuple[constants.LEGEND_ACCENT_COLOR_INTENSITY],
          },
        });
        colorIndex++;
      } else {
        // No more available colors, stop adding new sets
        break;
      }
    }
  }
  selectedSets.value = newSelectedSets;
}

async function buildChart() {
  const isCanvasReady = await waitForCanvasRef();
  if (!isCanvasReady) return;

  const ctx = chartRef.value!.getContext("2d");
  if (!ctx) return;

  const options = prepareOptions();
  const data = prepareData();

  chartInstance.value = new Chart(ctx, {
    type: "venn",
    data: data as any,
    options,
  });
}

function destroyChart() {
  if (!chartInstance.value) return;
  (chartInstance.value as Chart<"venn">).destroy();
  chartInstance.value = null;
}

function prepareData() {
  const selectedSetsKeys = selectedSets.value.map(
    (item) => props.coreSetDefinitions[item.key].bitmask
  );
  const regionValues = new Map(
    Object.entries(props.regionsData).map(([key, value]) => [
      parseInt(key),
      value as number,
    ])
  );
  const projectedRegionValues = projectPowerSet(selectedSetsKeys, regionValues);

  const projectedSetLabels = generatePowerSet(
    selectedSets.value.map((item) => props.coreSetDefinitions[item.key].label)
  );

  const keys = generateMasksGosperInts(selectedSetsKeys.length);

  const data: ISet<number>[] = Array.from(keys).map((key) => {
    const labels = projectedSetLabels.get(key);
    if (!labels) throw new Error("Missing definition for key: " + key);

    const label = labels.join(" & ");
    const value = projectedRegionValues.get(key) ?? 0;
    const degree = labels.length;

    return { label, value, sets: labels, degree, values: [] };
  });

  return {
    labels: data.map((item) => item.label),
    datasets: [{ label: "Venn Diagram", data }],
  };
}

function prepareOptions() {
  const chartColors = getChartColors();

  const baseOptions = merge(constants.DEFAULT_OPTIONS, {
    backgroundColor: chartColors,
    scales: {
      x: {
        ticks: { display: false },
      },
    },
  } as ChartOptions<"venn">);

  const mergedOptions = merge(baseOptions, props.options);
  if (!mergedOptions.plugins) {
    mergedOptions.plugins = {};
  }
  mergedOptions.plugins.vennDataLabels = {
    ...(mergedOptions.plugins.vennDataLabels || {}),
    enabled: true,
    formatType: props.formatType,
  } as VennChartDataLabelsOptions;

  return mergedOptions;
}

function getChartColors() {
  const baseColors = selectedSets.value.map(
    (item) => item.colorConfig.chartColor
  );
  return getMixedColorsArray(baseColors, multiplyColorsMixer);
}

function handleLabelClick(key: string) {
  if (selectedSets.value.some((item) => item.key === key)) {
    selectedSets.value = selectedSets.value.filter((item) => item.key !== key);
    return;
  }
  const usedColors = new Set(
    selectedSets.value.map((item) => item.colorConfig.chartColor)
  );
  const availableColors = constants.COLOR_PALETTE.filter(
    (color) => !usedColors.has(color[constants.CHART_COLOR_INTENSITY])
  );
  if (selectedSets.value.length < _maxSelection.value) {
    selectedSets.value.push({
      key,
      colorConfig: {
        chartColor: availableColors[0][constants.CHART_COLOR_INTENSITY],
        legendColor: availableColors[0][constants.LEGEND_COLOR_INTENSITY],
        legendAccentColor:
          availableColors[0][constants.LEGEND_ACCENT_COLOR_INTENSITY],
      },
    });
    return;
  }
}
</script>

<template>
  <BaseCard v-bind="{ ...$props, ...$attrs }" title-icon="join_inner">
    <div
      class="flex flex-row flex-1 min-w-0 justify-between items-center gap-4"
    >
      <ul class="flex flex-col gap-4 shrink-0">
        <span class="label-2 text-disabled-text">
          Choose up to {{ _maxSelection }} reports to plot at once
        </span>

        <li
          v-for="[key, definition] in Object.entries(coreSetDefinitions)"
          :key="key"
          class="inline-flex gap-2 items-center rounded-xl p-3 w-full border border-default-border hover:cursor-pointer select-none"
          :style="{
            backgroundColor: selectedSets.find((item) => item.key === key)
              ?.colorConfig.chartColor,
          }"
          @click="handleLabelClick(key)"
        >
          <div
            class="rounded-full w-3 h-3 border border-default-border"
            :style="{
              backgroundColor: selectedSets.find((item) => item.key === key)
                ?.colorConfig.legendAccentColor,
            }"
          ></div>

          <span class="flex-1 label-2">{{ definition.label }}</span>
        </li>
      </ul>

      <div class="flex-1 min-w-0 self-stretch p-4">
        <canvas ref="chartRef" />
      </div>
    </div>
  </BaseCard>
</template>
