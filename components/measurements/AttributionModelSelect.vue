<script lang="ts">
const ATTRIBUTION_MODEL_OPTIONS: ViewableOption<string>[] = [
  { label: "Linear Attribution", value: "linearTouch" },
  { label: "Gigi Attribution", value: "gigi" },
  { label: "Last Touch Attribution", value: "lastTouch" },
  { label: "First Touch Attribution", value: "firstTouch" },
];
</script>

<script setup lang="ts">
const props = defineProps<{
  multiple?: boolean;
}>();

const modelValue = defineModel<ViewableOption<string>[]>({
  default: () => [ATTRIBUTION_MODEL_OPTIONS[0]],
});

// Initialize internal value with validation
const internalValue = ref<string[]>(
  modelValue.value.length > 0 
    ? modelValue.value.map((model) => model.value)
    : [ATTRIBUTION_MODEL_OPTIONS[0].value]
);

// Computed for converting internal values to model values
const computedModelValue = computed(() => 
  ATTRIBUTION_MODEL_OPTIONS.filter((model) =>
    internalValue.value.includes(model.value)
  )
);

// Single watcher to handle all internal value changes
watch(
  internalValue,
  (newValue) => {
    // Prevent empty selection
    if (newValue.length === 0) {
      internalValue.value = [ATTRIBUTION_MODEL_OPTIONS[0].value];
      return;
    }

    // In single mode, keep only the last selected item
    if (!props.multiple && newValue.length > 1) {
      internalValue.value = [newValue[newValue.length - 1]];
      return;
    }

    const newModelValue = computedModelValue.value;
    if (!arrayEquals(modelValue.value.map(m => m.value), newModelValue.map(m => m.value))) {
      modelValue.value = newModelValue;
    }
  },
  { immediate: true }
);

// Watch for external model value changes
watch(
  modelValue,
  (newValue) => {
    const newInternalValue = newValue.length > 0 
      ? newValue.map((model) => model.value)
      : [ATTRIBUTION_MODEL_OPTIONS[0].value];
    
    if (!arrayEquals(internalValue.value, newInternalValue)) {
      internalValue.value = newInternalValue;
    }
  },
  { immediate: true }
);

// Computed values for templates
const singleValue = computed({
  get: () => internalValue.value[0] || ATTRIBUTION_MODEL_OPTIONS[0].value,
  set: (value: string) => internalValue.value = [value]
});

const commonPtProps = {
  labelContainer: { class: "!hidden" },
  root: { class: "outline-none border-none shadow-none" }
};
</script>

<template>
  <!-- Multi-select mode with checkboxes -->
  <MultiSelect
    v-if="multiple"
    v-model="internalValue"
    :options="ATTRIBUTION_MODEL_OPTIONS"
    option-label="label"
    option-value="value"
    :pt="commonPtProps"
    :show-toggle-all="false">
    <template #dropdownicon>
      <div class="flex flex-row items-center gap-1 bg-secondary-background rounded-lg p-2 text-primary-text">
        <MaterialIcon icon="touch_app" size="1rem" />
        <MaterialIcon icon="arrow_drop_down" size="1rem" />
      </div>
    </template>
  </MultiSelect>

  <!-- Single-select mode with radio buttons -->
  <Select
    v-else
    v-model="singleValue"
    :options="ATTRIBUTION_MODEL_OPTIONS"
    option-label="label"
    option-value="value"
    :pt="{ ...commonPtProps, label: { class: '!hidden' } }">
    <template #option="{ option }">
      <div class="flex justify-center items-center gap-2">
        <RadioButton 
          :value="option.value"
          v-model="singleValue"
          :pt="{ 
            box: { class: 'w-4 h-4' },
            icon: { class: 'w-2 h-2' }
          }"/>
        <span>{{ option.label }}</span>
      </div>
    </template>
    <template #dropdownicon>
      <div class="flex flex-row items-center gap-1 bg-secondary-background rounded-lg p-2 text-primary-text">
        <MaterialIcon icon="touch_app" size="1rem" />
        <MaterialIcon icon="arrow_drop_down" size="1rem" />
      </div>
    </template>
  </Select>
</template>
