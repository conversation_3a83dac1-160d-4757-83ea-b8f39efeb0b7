<script setup lang="ts" generic="T extends object">
import type { BaseCardProps } from "@/components/measurements/BaseCard.vue";
import type { GTableProps } from "@/components/common/g-table/GTable.vue";

type TableCardProps<T extends object> = Omit<BaseCardProps, "titleIcon"> &
  Omit<GTableProps<T>, "variant" | "paginator">;

const props = defineProps<TableCardProps<T>>();

const data = computed(() => {
  return props.data;
});
</script>

<template>
  <BaseCard v-bind="{ ...$props, ...$attrs }" title-icon="table_chart">
    <GTable
      v-bind="$props"
      :key="data.length"
      :paginator="true"
      scroll-height="flex"
      variant="borderless"
      class="w-full h-full"
    >
      <slot />
    </GTable>
  </BaseCard>
</template>
