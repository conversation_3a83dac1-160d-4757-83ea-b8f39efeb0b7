<script setup lang="ts">
import type {
  BaseCardProps,
  BaseCardEmits,
} from "@/components/measurements/BaseCard.vue";
import type { FunnelStage } from "./specific/FunnelBy.vue";
import { computed } from "vue";
import { funnelColorMap } from "@/components/common/funnel/constants";

type FunnelCardProps = {
  data: FunnelStage[];
  currencyCode?: string;
  hideValuesColumn?: boolean;
} & Omit<BaseCardProps, "titleIcon">;

const props = defineProps<FunnelCardProps>();

// SVG path variables
const svgConfig = computed(() => ({
  viewBox: {
    width: 200,
    height: 200,
  },
  // Top edge coordinates
  topLeft: 20,
  topRight: 180,
  topY: 10,

  // Bottom edge coordinates
  bottomLeft: 30,
  bottomRight: 170,
  bottomY: 190,

  // Curve parameters
  curveRadius: 7,
  curveControlOffset: 1,

  // Top stage specific (rounded top)
  topStage: {
    topLeftCurve: 30,
    topRightCurve: 170,
    topLeftControl: 20,
    topRightControl: 180,
    roundedY: 30,
  },

  // Bottom stage specific (rounded bottom)
  bottomStage: {
    bottomLeftCurve: 50,
    bottomRightCurve: 150,
    bottomLeftControl: 35,
    bottomRightControl: 165,
    roundedBottomY: 170,
  },
}));

// Generate SVG paths using variables
const generatePath = (type: "top" | "middle" | "bottom") => {
  const config = svgConfig.value;
  var path;

  switch (type) {
    case "top":
      path = `M${config.topLeft} ${config.topStage.roundedY} 
              Q${config.topStage.topLeftControl} ${config.topY} ${config.topStage.topLeftCurve} ${config.topY}
              L${config.topStage.topRightCurve} ${config.topY} 
              Q${config.topStage.topRightControl} ${config.topY} ${config.topRight} ${config.topStage.roundedY}
              L${config.bottomRight} ${config.bottomY} ${config.bottomLeft} ${config.bottomY}
              Z`;
      break;

    case "bottom":
      path = `M${config.topLeft} ${config.topY} ${config.topRight} ${
        config.topY
      } ${config.bottomRight - 2} ${config.bottomStage.roundedBottomY}
              Q${config.bottomStage.bottomRightControl} ${config.bottomY} ${
        config.bottomStage.bottomRightCurve
      } ${config.bottomY} 
              L${config.bottomStage.bottomLeftCurve} ${config.bottomY} 
              Q${config.bottomStage.bottomLeftControl} ${config.bottomY} ${
        config.bottomLeft + 2
      } ${config.bottomStage.roundedBottomY}
              Z`;
      break;

    case "middle":
    default:
      path = `M${config.topLeft} ${config.topY}
              L${config.topRight} ${config.topY}
              L${config.bottomRight} ${config.bottomY}
              L${config.bottomLeft} ${config.bottomY} 
              Z`;
  }
  return path;
};

const getFunnelCategoryStyle = (category: string) => {
  return {
    "--stage-bg-color":
      funnelColorMap[category as keyof typeof funnelColorMap].bgHex,
    "--stage-hover-bg-color":
      funnelColorMap[category as keyof typeof funnelColorMap].hoverBgHex,
    "--stage-text-color":
      funnelColorMap[category as keyof typeof funnelColorMap].textHex,
  };
};

const calculateStageWidth = (index: number): number => {
  const top_edge = svgConfig.value.topRight - svgConfig.value.topLeft;
  const bottom_edge = svgConfig.value.bottomRight - svgConfig.value.bottomLeft;
  const ratio = bottom_edge / top_edge;

  // scale down each stage a little bit more to make a nicer alignment
  return Math.pow(ratio, index) * Math.pow(0.99, index);
};
</script>

<template>
  <BaseCard v-bind="{ ...$props, ...$attrs }" title-icon="filter_alt">
    <div class="funnel-container">
      <!-- Labels Column -->
      <div class="funnel-column labels-column">
        <div
          v-for="(stage, index) in data"
          :key="`label-${stage.category}`"
          class="stage-row"
        >
          <div class="stage-label-container">
            <FunnelPill :funnel="stage.category" size="sm" />
          </div>
        </div>
      </div>

      <!-- Values Column -->
      <div v-if="!hideValuesColumn" class="funnel-column values-column">
        <div
          v-for="(stage, index) in data"
          :key="`value-${stage.category}`"
          class="stage-row"
        >
          <div class="stage-value-cell">
            {{ formatCurrency(stage.value, currencyCode) }}
          </div>
        </div>
      </div>

      <!-- Funnel Shapes Column -->
      <div class="funnel-column shapes-column">
        <div
          v-for="(stage, index) in data"
          :key="`shape-${stage.category}`"
          class="stage-row interactive-row"
          :style="getFunnelCategoryStyle(stage.category)"
        >
          <div class="stage-shape-cell">
            <div
              class="stage-shape-container"
              :style="{
                // scale funnel stages only on X axis
                transform: `scaleX(${calculateStageWidth(index)})`,
              }"
            >
              <svg
                class="funnel-svg"
                :viewBox="`0 0 ${svgConfig.viewBox.width} ${svgConfig.viewBox.height}`"
                preserveAspectRatio="none"
              >
                <path
                  v-if="index === 0"
                  :d="generatePath('top')"
                  class="funnel-path"
                />
                <path
                  v-else-if="index === data.length - 1"
                  :d="generatePath('bottom')"
                  class="funnel-path"
                />
                <path v-else :d="generatePath('middle')" class="funnel-path" />
              </svg>

              <!-- Text overlay -->
              <div
                class="stage-text-overlay"
                :style="{
                  // scale the text back to avoid distortion
                  transform: `scaleX(${1 / calculateStageWidth(index)})`,
                }"
              >
                <span class="stage-percentage-text">
                  {{ stage.funnelStageStr }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<style scoped>
.funnel-container {
  @apply flex py-6 w-full gap-4 flex-1;
}

.funnel-column {
  @apply flex min-w-fit flex-col;
}

.labels-column {
  @apply flex-shrink-0 w-1/6;
}

.values-column {
  @apply flex-shrink-0 w-1/6;
}

.shapes-column {
  @apply flex-1 min-w-0 overflow-hidden;
}

.stage-row {
  @apply flex items-center flex-1 min-h-0;
}

.interactive-row {
  @apply transition-all;
}

.stage-label-container {
  @apply flex ml-8 justify-start items-center w-full;
}

.stage-value-cell {
  @apply mr-2 body-1 text-right w-full flex justify-end items-center;
}

.stage-shape-cell {
  @apply flex-1 h-full overflow-hidden;
}

.stage-shape-container {
  @apply relative flex-1 h-full flex items-center justify-center overflow-hidden;
}

.interactive-stage {
  transition: all 0.1s ease-in-out;
}

.funnel-path {
  fill: var(--stage-bg-color);
  transition: fill 0.1s ease-in-out;
}

.interactive-row:hover .funnel-path {
  fill: var(--stage-hover-bg-color);
}

.interactive-stage:hover {
  transform-origin: center;
}

.funnel-svg {
  @apply w-full h-full block;
}

.stage-text-overlay {
  @apply absolute inset-0 flex items-center justify-center;
}

.stage-percentage-text {
  @apply label-3;
  color: var(--stage-text-color);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .funnel-container {
    @apply flex-col gap-2;
  }

  .funnel-column {
    @apply w-full;
  }

  .stage-row {
    @apply justify-center;
  }

  .stage-shape-container {
    @apply h-10;
  }

  .stage-value-cell {
    @apply body-1;
  }

  .stage-percentage-text {
    @apply label-3;
  }
}
</style>
