import type { ISet } from "chartjs-chart-venn";

export type VennChartData = {
  labels: string[];
  datasets: {
    label: string;
    data: Prettify<ISet<number>>[];
  }[];
};

export type VennCoreSetDefinition = {
  bitmask: number;
  label: string;
};

export type SelectedCoreSetColorConfig = {
  chartColor: string;
  legendColor: string;
  legendAccentColor: string;
};

export type SelectedCoreSet = {
  key: string;
  colorConfig: SelectedCoreSetColorConfig;
};

export type VennCoreSetDefinitions = Record<string, VennCoreSetDefinition>;

export type NumericKey = `${number}`;
export type VennRegionsData = Record<NumericKey, number>;

export type RgbColor = { r: number; g: number; b: number };
