import twColors from "tailwindcss/colors";
import type { VennCoreSetDefinitions } from "./types";
import type { VennDiagramChart } from "chartjs-chart-venn";
import type { ChartOptions } from "chart.js";

// Locally define or augment the ChartOptions for plugins to include vennDataLabels
interface LocalChartOptionsForTooltip extends ChartOptions<"venn"> {
  plugins?: ChartOptions<"venn">["plugins"] & {
    vennDataLabels?: {
      enabled?: boolean;
      formatType?: "percentage" | "default";
    };
  };
}

export const COLOR_PALETTE = [twColors.red, twColors.indigo, twColors.orange];

export const CHART_COLOR_INTENSITY = 100;
export const LEGEND_COLOR_INTENSITY = 50;
export const LEGEND_ACCENT_COLOR_INTENSITY = 500;

export const CampaignTacticsSetDefinitions = {
  STREAMING_TV: { bitmask: 1 << 4, label: "Streaming TV" },
  TOF: { bitmask: 1 << 3, label: "Top of Funnel (TOF)" },
  MOF: { bitmask: 1 << 1, label: "Mid of Funnel (MOF)" },
  BOF: { bitmask: 1 << 0, label: "Bottom of Funnel (BOF)" },
  SPONSORED: { bitmask: 1 << 2, label: "Sponsored" },
} as const satisfies VennCoreSetDefinitions;

const compactFmt = new Intl.NumberFormat(undefined, { notation: "compact" });

export const DEFAULT_OPTIONS: VennDiagramChart["options"] = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      enabled: true,
      backgroundColor: "rgba(255, 255, 255, 1)",
      titleColor: "black",
      bodyColor: "black",
      borderColor: "rgba(0, 0, 0, 0.1)",
      borderWidth: 1,
      boxPadding: 4,
      callbacks: {
        label: function (context) {
          const { raw, chart } = context as any;
          const { label, value } = raw as { label: string; value: number };

          if (label === undefined || value === undefined || value === null)
            return "N/A";

          const options = (chart.options as LocalChartOptionsForTooltip).plugins
            ?.vennDataLabels;
          const formatType = options?.formatType;

          let formattedValue: string;
          if (value === undefined || value === null) {
            formattedValue = "N/A";
          } else if (formatType === "percentage") {
            formattedValue = new Intl.NumberFormat(undefined, {
              style: "percent",
              minimumFractionDigits: 2,
              maximumFractionDigits: 4,
            }).format(value / 100);
          } else {
            formattedValue = new Intl.NumberFormat(undefined, {
              minimumFractionDigits: 0,
              maximumFractionDigits: 4,
            }).format(value);
          }

          return `${label}: ${formattedValue}`;
        },
      },
    },
  },
};
