import type { RgbColor } from "./types";

/**
 * Projects a key onto a set of selected sets and reduces it's dimension.
 * @param currentKey - The key to project.
 * @param selectedSets - The set of selected sets.
 * @returns The projected key.
 */
export function projectKey(currentKey: number, selectedSets: number[]) {
  return selectedSets.reduce((projectedKey, setKey, index) => {
    if (currentKey & setKey) {
      projectedKey |= 1 << index;
    }
    return projectedKey;
  }, 0);
}

/**
 * Projects a power set onto a set of selected sets and reduces it's dimension.
 * @param selectedSetsKeys - The set of selected sets keys.
 * @param originalPowerSet - The power set to project.
 * @returns The projected power set.
 *
 * The selected sets keys array values should correspond with the bitmask key of the original core set
 * So if the original is a power set of 3 core sets whose values are A - 001, B - 010, C - 100 and we want to project onto the sets B and C
 * Then the selected sets keys should be [0b010, 0b100]
 * The order od the provided keys would dictate the order of the sets in the projected power set
 */
export function projectPowerSet(
  selectedSetsKeys: number[],
  originalPowerSet: Map<number, number>
): Map<number, number> {
  const projectedPowerSet: Map<number, number> = new Map();

  for (const [key, value] of originalPowerSet.entries()) {
    const projectedKey = projectKey(key, selectedSetsKeys);
    const aggregated = projectedPowerSet.get(projectedKey) ?? 0;
    projectedPowerSet.set(projectedKey, aggregated + value);
  }

  return projectedPowerSet;
}

/**
 * Generates a power set of a given set.
 * @param set - The set to generate the power set of.
 * @returns A map of the bitmask key to the subset.
 */
export function generatePowerSet<T>(set: T[]): Map<number, T[]> {
  const powerSetDimension = set.length;
  const powerSetSize = 1 << powerSetDimension;

  const powerSet: Map<number, T[]> = new Map();

  for (let i = 0; i < powerSetSize; i++) {
    const subset: T[] = [];
    for (let j = 0; j < powerSetDimension; j++) {
      if (i & (1 << j)) {
        subset.push(set[j]);
      }
    }
    powerSet.set(i, subset);
  }
  return powerSet;
}

/**
 * Generates bitmasks using Gosper's algorithm. It iterates through masks
 * with k set bits (for k from 1 to n), and for each k, generates all
 * masks in lexicographical order.
 *
 * The overall sequence of yielded masks for n=3 would be (in binary):
 * 001, 010, 100 (for k=1),
 * 011, 101, 110 (for k=2),
 * 111 (for k=3).
 * As numbers, this sequence is: 1, 2, 4, 3, 5, 6, 7.
 *
 * @param n The number of elements or bits. Masks represent subsets of these n elements.
 *          Masks generated will have values less than 2^n.
 * @returns A generator yielding numbers, where each number is a bitmask.
 */
export function* generateMasksGosperInts(n: number): Generator<number> {
  const limit = 1 << n; // Masks are for subsets of n items, so they are < 2^n.

  // Iterate through the desired number of set bits (k) in the mask, from 1 to n.
  for (let k = 1; k <= n; k++) {
    // Initialize `mask` to the smallest number with `k` bits set (e.g., for k=3, mask is 0...00111).
    let mask = (1 << k) - 1;

    // For the current `k`, generate all masks with `k` set bits in lexicographical order.
    while (mask < limit) {
      yield mask;

      // Gosper's algorithm for the next lexicographical k-subset:
      const c = mask & -mask; // Smallest power of 2 in mask (rightmost 1-bit).
      // E.g., if mask = (binary ...01011), c = (...00001).
      const r = mask + c; // Creates the "prefix" for the next mask.
      // E.g., if mask = (...01011), r = (...01100).
      mask = (((r ^ mask) >>> 2) / c) | r; // Forms the next mask by arranging the remaining 1s
      // (from original mask, distinct from `c`'s block)
      // into the lowest bit positions after the prefix `r`.
      // E.g., r^mask=(...00111). ((r^mask >>> 2)/c)=(...00001). new_mask = r | (...00001) = ...01101.
    }
  }
}

export function getMixedColorsArray(
  baseColors: string[],
  mixer: (colors: RgbColor[]) => RgbColor
): string[] {
  const baseColorsRgb = baseColors.map(hexToRgb);
  if (!baseColorsRgb.every((color) => color !== null)) {
    throw new Error("Invalid color");
  }

  const n = baseColors.length;

  const mixedColors: RgbColor[] = [];
  for (const mask of generateMasksGosperInts(n)) {
    const selectedColors: RgbColor[] = [];

    for (let i = 0; i < n; i++) {
      if (maskBitAtIsOn(mask, i)) {
        selectedColors.push(baseColorsRgb[i]);
      }
    }

    const mixedColor = mixer(selectedColors);
    mixedColors.push(mixedColor);
  }

  return mixedColors.map(rgbToHex);
}

function hexToRgb(hex: string): RgbColor | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

function rgbToHex(rgb: RgbColor) {
  return `#${((1 << 24) + (rgb.r << 16) + (rgb.g << 8) + rgb.b)
    .toString(16)
    .slice(1)}`;
}

export function multiplyColorsMixer(colors: RgbColor[]): RgbColor {
  const normalizedMixedColor = colors.map(normalizeColor).reduce(
    (acc, color) => {
      return {
        r: acc.r * color.r,
        g: acc.g * color.g,
        b: acc.b * color.b,
      };
    },
    { r: 1, g: 1, b: 1 }
  );

  return {
    r: Math.round(normalizedMixedColor.r * 255),
    g: Math.round(normalizedMixedColor.g * 255),
    b: Math.round(normalizedMixedColor.b * 255),
  };
}

function normalizeColor(color: RgbColor): RgbColor {
  return {
    r: color.r / 255,
    g: color.g / 255,
    b: color.b / 255,
  };
}

function maskBitAtIsOn(bitmask: number, bit: number): boolean {
  return (bitmask & (1 << bit)) !== 0;
}
