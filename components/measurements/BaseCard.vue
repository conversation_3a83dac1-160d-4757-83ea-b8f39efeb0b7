<script setup lang="ts">
export interface BaseCardProps {
  title: string;
  description?: string | string[];
  infoTooltip?: string;
  titleIcon: string;
  categories?: {
    label: string;
    value: string;
  }[];
  defaultCategory?: string;
  supportAttributionModel?: "single" | "multiple" | boolean;
  state?: "loading" | "empty" | "success" | "error";
  errorMessage?: string;
  loadingMessage?: string;
  emptyMessage?: string;
}

export interface BaseCardEmits {
  (e: "categoryChange", value: string | undefined): void;
}

const props = defineProps<BaseCardProps>();
const emit = defineEmits<BaseCardEmits>();

const modelValueAttributionModels =
  defineModel<ViewableOption<string>[]>("attributionModels");

const descriptionText = computed(() => {
  const normalizedDescription = Array.isArray(props.description)
    ? props.description
    : [props.description];

  const parts = [...normalizedDescription];
  if (modelValueAttributionModels.value?.length) {
    parts.push(
      modelValueAttributionModels.value.map((model) => model.label).join(", ")
    );
  }

  return parts.join(" • ");
});
</script>

<template>
  <div class="root-container">
    <div class="header-container">
      <div class="title-container">
        <span class="title">
          <MaterialIcon :icon="titleIcon" class="text-disabled-text" />

          {{ title }}

          <CategorySelector
            v-if="categories"
            :categories="categories"
            :default-category="defaultCategory"
            @change="emit('categoryChange', $event)"
          />

          <MaterialIcon
            v-if="infoTooltip"
            v-tooltip.bottom="infoTooltip"
            icon="info"
            class="text-neutral-400"
            size="0.75rem"
          />
        </span>

        <span class="description">{{ descriptionText }}</span>
      </div>

      <div class="actions-container">
        <AttributionModelSelect
          v-if="!!supportAttributionModel"
          :multiple="supportAttributionModel === 'multiple'"
          v-model="modelValueAttributionModels"
        />

        <PopoverButton prefix-icon="more_vert">
          <div class="flex flex-col">
            <button class="more-options-button">
              <MaterialIcon icon="download" />
              Download as .csv
            </button>
          </div>
        </PopoverButton>
      </div>
    </div>

    <div class="content-container">
      <div
        v-if="state === 'loading'"
        class="flex justify-center items-center min-h-48 text-gray-600"
      >
        <div class="text-center space-y-3">
          <div
            class="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto"
          ></div>
          <p class="text-sm font-medium">
            {{ loadingMessage || "Loading..." }}
          </p>
        </div>
      </div>

      <div
        v-else-if="state === 'error'"
        class="flex justify-center items-center min-h-48 text-gray-600"
      >
        <div class="text-center space-y-3">
          <div
            class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto"
          >
            <MaterialIcon icon="error_outline" class="text-red-500 text-xl" />
          </div>
          <p class="text-sm font-medium text-gray-700">
            {{ errorMessage || "An error occurred" }}
          </p>
        </div>
      </div>

      <div
        v-else-if="state === 'empty'"
        class="flex justify-center items-center min-h-48 text-gray-600"
      >
        <div class="text-center space-y-3">
          <div
            class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto"
          >
            <MaterialIcon :icon="titleIcon" class="text-gray-400 text-xl" />
          </div>
          <p class="text-sm font-medium text-gray-700">
            {{ emptyMessage || "No data available" }}
          </p>
        </div>
      </div>

      <slot v-else />
    </div>
  </div>
</template>

<style scoped>
.root-container {
  @apply flex flex-col min-w-0 h-[640px] max-h-[640px]
    border border-default-border rounded-2xl;
}

.header-container {
  @apply flex flex-col sm:flex-row gap-2 sm:gap-0 justify-between items-start sm:items-center p-4;
}

.title-container {
  @apply flex flex-col gap-2 min-w-0 flex-1 items-start;
}

.title {
  @apply inline-flex items-center gap-2 label-4 text-primary-text flex-wrap;
}

.description {
  @apply caption-2 text-disabled-text;
}

.actions-container {
  @apply flex flex-row gap-2 flex-wrap;
}

.content-container {
  @apply flex place-content-center min-h-0 flex-1 px-3 py-2;
}

.more-options-button {
  @apply flex flex-row items-center gap-2 px-2 py-1 hover:bg-secondary-background;
}
</style>
