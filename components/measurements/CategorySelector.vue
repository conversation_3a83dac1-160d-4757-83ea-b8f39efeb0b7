<script setup lang="ts">
const props = defineProps<{
  categories: ViewableOption<string>[];
  defaultCategory?: string;
}>();

const emit = defineEmits<{
  (e: "change", value: string | undefined): void;
}>();

const value = ref<string | undefined>(
  typeof props.defaultCategory === "string"
    ? props.categories.find((c) => c.value === props.defaultCategory)?.value
    : props.defaultCategory || props.categories[0]?.value
);

watch(
  value,
  (newValue) => {
    emit("change", newValue);
  },
  { immediate: true }
);
</script>

<template>
  <Select
    v-if="categories"
    v-model="value"
    :options="categories"
    option-label="label"
    option-value="value"
    :pt="{
      root: {
        class: 'border-none shadow-none',
      },
      label: {
        class: 'underline-dotted p-0',
      },
      dropdown: {
        class: 'w-fit pl-1',
      },
    }"
  >
    <template #dropdownicon>
      <MaterialIcon icon="arrow_drop_down" size="1rem" />
    </template>
  </Select>
</template>
