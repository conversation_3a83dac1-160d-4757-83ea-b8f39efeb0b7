<script setup lang="ts">
import {
  type ChartData,
  type ChartOptions,
  CategoryScale,
  LinearScale,
  BarElement,
  registry,
  Tooltip,
} from "chart.js";
import { merge } from "chart.js/helpers";
import { Bar } from "vue-chartjs";

registry.add(CategoryScale, LinearScale, BarElement, Tooltip);

import type { BaseCardProps } from "@/components/measurements/BaseCard.vue";
import type { Prettify } from "@/types/utils";

type BarCharCardProps = {
  data: Prettify<ChartData<"bar">>;
  options?: Prettify<ChartOptions<"bar">>;
  plugins?: any[];
} & Omit<BaseCardProps, "titleIcon">;

const props = defineProps<BarCharCardProps>();

const defaultOptions: ChartOptions<"bar"> = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      type: "category",
      grid: {
        display: true,
      },
    },
    y: {
      type: "linear",
      grid: {
        display: true,
      },
    },
  },
  plugins: {
    colors: {
      enabled: true,
    },
    legend: {
      display: false,
    },
  },
};

const _options = computed(() => merge(defaultOptions, props.options));
</script>

<template>
  <BaseCard v-bind="{ ...$props, ...$attrs }" title-icon="table_chart">
    <Bar
      class="h-full"
      :data="props.data"
      :options="_options"
      :plugins="props.plugins"
    />
  </BaseCard>
</template>
