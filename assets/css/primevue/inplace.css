.p-inplace-display {
    @apply inline cursor-pointer border border-transparent px-3 py-2 rounded-md
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
        transition-colors duration-200
}

.p-inplace-display:not(.p-disabled):hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-inplace-content {
    @apply block
}