.p-contextmenu {
    @apply bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0
        border border-surface-200 dark:border-surface-700
        rounded-md min-w-52 shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}

.p-contextmenu-root-list,
.p-contextmenu-submenu {
    @apply m-0 p-1 list-none outline-none flex flex-col gap-[2px]
}

.p-contextmenu-submenu {
    @apply absolute min-w-full z-10 rounded-md
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0
        border border-surface-200 dark:border-surface-700
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}

.p-contextmenu-item {
    @apply relative
}

.p-contextmenu-item-content {
    @apply transition-colors duration-200 rounded-sm text-surface-700 dark:text-surface-0
}

.p-contextmenu-item-link {
    @apply cursor-pointer flex items-center no-underline overflow-hidden relative text-inherit
        px-3 py-2 gap-2 select-none outline-none
}

.p-contextmenu-item-icon {
    @apply text-surface-400 dark:text-surface-500
}

.p-contextmenu-submenu-icon {
    @apply text-surface-400 dark:text-surface-500 ms-auto text-sm w-[0.875rem] h-[0.875rem]
}

.p-contextmenu-item.p-focus > .p-contextmenu-item-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-contextmenu-item.p-focus > .p-contextmenu-item-content .p-contextmenu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-item.p-focus > .p-contextmenu-item-content .p-contextmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover .p-contextmenu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover .p-contextmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-item-active > .p-contextmenu-item-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-contextmenu-separator {
    @apply border-t border-surface-200 dark:border-surface-700
}

.p-contextmenu-enter-from,
.p-contextmenu-leave-active {
    @apply opacity-0
}

.p-contextmenu-enter-active {
    @apply transition-opacity duration-[250ms]
}

.p-contextmenu-mobile .p-contextmenu-submenu {
    @apply static shadow-none border-none ps-4 pe-0
}

.p-contextmenu-mobile .p-contextmenu-submenu-icon {
    @apply transition-transform duration-200 rotate-90
}

.p-contextmenu-mobile .p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-submenu-icon {
    @apply -rotate-90
}
