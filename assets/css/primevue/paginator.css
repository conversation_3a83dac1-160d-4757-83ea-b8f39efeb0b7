.p-paginator {
    @apply flex items-center justify-center flex-wrap py-2 px-4 rounded-md gap-1
        bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0
}

.p-paginator-content {
    @apply flex items-center justify-center flex-wrap gap-1
}

.p-paginator-content-start {
    @apply ms-auto
}

.p-paginator-content-end {
    @apply me-auto
}

.p-paginator-page,
.p-paginator-next,
.p-paginator-last,
.p-paginator-first,
.p-paginator-prev {
    @apply cursor-pointer inline-flex items-center justify-center leading-none overflow-hidden relative
        bg-transparent border-none min-w-10 h-10 transition-colors duration-200 rounded-full p-0 m-0
        text-surface-500 dark:text-surface-400
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
}

.p-paginator-page:not(.p-disabled):not(.p-paginator-page-selected):hover,
.p-paginator-first:not(.p-disabled):hover,
.p-paginator-prev:not(.p-disabled):hover,
.p-paginator-next:not(.p-disabled):hover,
.p-paginator-last:not(.p-disabled):hover {
    @apply bg-surface-100 text-surface-600 dark:bg-surface-800 dark:text-surface-300
}

.p-paginator-page.p-paginator-page-selected {
    @apply bg-highlight
}

.p-paginator-current {
    @apply text-surface-500 dark:text-surface-400
}

.p-paginator-pages {
    @apply flex items-center gap-1
}

.p-paginator-jtp-input .p-inputtext {
    @apply max-w-10
}

.p-paginator-first:dir(rtl),
.p-paginator-prev:dir(rtl),
.p-paginator-next:dir(rtl),
.p-paginator-last:dir(rtl) {
    @apply rotate-180
}
