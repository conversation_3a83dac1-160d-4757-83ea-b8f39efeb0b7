.p-cascadeselect {
    @apply inline-flex cursor-pointer relative select-none rounded-md
        bg-surface-0 dark:bg-surface-950
        border border-surface-300 dark:border-surface-700
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        transition-colors duration-200
}

.p-cascadeselect:not(.p-disabled):hover {
    @apply border-surface-400 dark:border-surface-600
}

.p-cascadeselect:not(.p-disabled).p-focus {
    @apply border-primary
}

.p-cascadeselect.p-variant-filled {
    @apply bg-surface-50 dark:bg-surface-800
}

.p-cascadeselect.p-invalid {
    @apply border-red-400 dark:border-red-300
}

.p-cascadeselect.p-disabled {
    @apply bg-surface-200 text-surface-500 dark:bg-surface-700 dark:text-surface-400 opacity-100 cursor-default
}

.p-cascadeselect-dropdown {
    @apply flex items-center justify-center shrink-0 bg-transparent
        text-surface-500 dark:text-surface-400 w-10 rounded-e-md
}

.p-cascadeselect-clear-icon {
    @apply absolute top-1/2 -mt-2 text-surface-500 dark:text-surface-400 end-10
}

.p-cascadeselect-label {
    @apply block whitespace-nowrap overflow-hidden flex-auto w-[1%]
        py-2 px-3 overflow-ellipsis
        text-surface-700 dark:text-surface-0 bg-transparent border-none outline-none
}

.p-cascadeselect-label.p-placeholder {
    @apply text-surface-500 dark:text-surface-400
}

.p-cascadeselect.p-disabled .p-cascadeselect-label {
    @apply text-surface-500 dark:text-surface-400
}

.p-cascadeselect-label-empty {
    @apply overflow-hidden opacity-0
}

.p-cascadeselect-fluid {
    @apply flex
}

.p-cascadeselect-fluid .p-cascadeselect-label {
    @apply w-[1%]
}

.p-cascadeselect-overlay {
    @apply absolute top-0 left-0 rounded-md
        bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}

.p-cascadeselect .p-cascadeselect-overlay {
    @apply min-w-full
}

.p-cascadeselect-option-list {
    @apply hidden min-w-full absolute z-10
}

.p-cascadeselect-list {
    @apply min-w-full m-0 list-none p-1 flex flex-col gap-[2px]
}

.p-cascadeselect-option {
    @apply cursor-pointer font-normal whitespace-nowrap
        text-surface-700 dark:text-surface-0 bg-transparent border-none
        transition-colors duration-200 rounded-sm
}

.p-cascadeselect-option-active {
    @apply overflow-visible
}

.p-cascadeselect-option-active > .p-cascadeselect-option-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-cascadeselect-option:not(.p-cascadeselect-option-selected):not(.p-disabled).p-focus > .p-cascadeselect-option-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-cascadeselect-option:not(.p-cascadeselect-option-selected):not(.p-disabled).p-focus .p-cascadeselect-group-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-cascadeselect-option-selected .p-cascadeselect-option-content {
    @apply bg-highlight
}

.p-cascadeselect-option-selected.p-focus {
    @apply bg-highlight-emphasis
}

.p-cascadeselect-option-active > .p-cascadeselect-option-list {
    @apply block start-full top-0
}

.p-cascadeselect-option-content {
    @apply flex items-center justify-between overflow-hidden relative px-3 py-2
}

.p-cascadeselect-group-icon {
    @apply text-sm w-[0.875rem] h-[0.875rem] text-surface-400 dark:text-surface-500
}

.p-cascadeselect-group-icon:dir(rtl) {
    @apply rotate-180
}


.p-cascadeselect-mobile-active .p-cascadeselect-option-content {
    @apply rounded-sm
}

.p-cascadeselect-mobile-active-active .p-cascadeselect-list {
    @apply flex flex-col top-full start-0 z-10
}

.p-cascadeselect-mobile-active .p-cascadeselect-list > .p-cascadeselect-option > .p-cascadeselect-option-content .p-cascadeselect-group-icon {
    @apply ms-auto transition-transform duration-200
}

.p-cascadeselect-mobile-active .p-cascadeselect-list .p-cascadeselect-group-icon {
    @apply transition-transform duration-200 rotate-90
}

.p-cascadeselect-mobile-active .p-cascadeselect-option-active > .p-cascadeselect-option-content .p-cascadeselect-group-icon {
    @apply -rotate-90
}

.p-cascadeselect-mobile-active .p-cascadeselect-option-list {
    @apply static shadow-none border-none ps-4
}

.p-cascadeselect-sm .p-cascadeselect-label {
    @apply text-sm px-[0.625rem] py-[0.375rem]
}

.p-cascadeselect-sm .p-cascadeselect-dropdown .p-icon {
    @apply text-sm w-[0.875rem] h-[0.875rem]
}

.p-cascadeselect-lg .p-cascadeselect-label {
    @apply text-lg px-[0.875rem] py-[0.625rem]
}

.p-cascadeselect-lg .p-cascadeselect-dropdown .p-icon {
    @apply text-lg w-[1.125rem] h-[1.125rem]
}
