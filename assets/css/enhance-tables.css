.enhance-table-backdrop {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50;
}

.enhance-table-modal {
  @apply bg-white rounded-lg p-6 w-full mx-4 h-auto max-h-[95vh] overflow-auto relative;
}

.enhance-table-viewport {
  @apply relative w-full overflow-hidden;
}

.enhance-table-wrapper {
  @apply relative overflow-auto w-full py-0 prose max-w-none prose-neutral;
}

.enhance-table-wrapper table {
  @apply my-0;
}

.enhance-table-viewport:hover .enhance-table-button-container {
  @apply opacity-100;
}

.enhance-table-button-container {
  @apply absolute top-2 right-2 flex gap-2 z-10 opacity-0 transition-opacity duration-200;
}

.enhance-table-modal:hover .enhance-table-button-container {
  @apply opacity-100;
}

.enhance-table-modal .enhance-table-button-container {
  @apply absolute top-2 right-2 z-20 opacity-0;
}

.enhance-table-button {
  @apply material-symbols-outlined border border-default-border p-[0.375rem] rounded-md bg-white cursor-pointer hover:bg-gray-50 transition-colors shadow-sm outline-none;
}
