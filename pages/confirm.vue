<script setup lang="ts">
onMounted(async () => {
  definePageMeta({
    layout: "auth",
  });

  const route = useRoute();
  const code = route.query.code;

  try {
    const { ok } = await $fetch<{ ok: boolean }>("/api/auth/google", {
      query: {
        code,
      },
    });

    if (!ok) {
      await navigateTo("/login");
      return;
    }

    await useUserSession().fetch();
    await navigateTo("/");
  } catch (error) {
    console.error(error);
    await navigateTo("/login");
  }
});
</script>

<template>
  <h1>Waiting for Google to redirect you...</h1>
</template>
