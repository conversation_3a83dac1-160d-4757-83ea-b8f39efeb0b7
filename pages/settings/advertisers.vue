<script setup lang="ts">
const advertiserStore = useAdvertiserStore();
const isAddAdvertiserDrawerVisible = ref(false);
const selectedAdvertiserForSettings = ref<Advertiser>();

const moreHorizontalItems = ref([
  {
    label: "Advertiser Settings",
    icon: "tune",
    onClick: (data: any) => {
      openSettingsDrawer(data as Advertiser);
    },
  },
  {
    label: "Add Teammate",
    icon: "person_add",
    onClick: () => {},
  },
  {
    label: "Delete Advertiser",
    icon: "delete",
    onClick: () => {},
    itemClass: "text-red-500",
  },
]);

function openSettingsDrawer(advertiser: Advertiser) {
  isAddAdvertiserDrawerVisible.value = true;
  selectedAdvertiserForSettings.value = advertiser;
}
</script>

<template>
  <div class="flex flex-1 flex-col items-start justify-start gap-6">
    <h1>Advertisers</h1>

    <Divider class="!my-0" />

    <GTable
      :data="advertiserStore.data"
      data-key="advertiserId"
      class="w-full"
      header
      empty-message="No advertisers found"
    >
      <Column header="Advertiser">
        <template #body="slotProps">
          <div
            class="flex items-center gap-0 cursor-pointer"
            @click="openSettingsDrawer(slotProps.data)"
          >
            <EntityAvatar
              :entity-id="slotProps.data.advertiserId"
              entity-type="ADVERTISER"
              label-class="truncate !font-medium underline-dotted"
            />
          </div>
        </template>
      </Column>
      <Column field="advertiserId" header="Advertiser ID" />
      <Column field="shopUrl" header="URL" />
      <Column field="actions">
        <template #body="slotProps">
          <MoreHorizontal
            :items="moreHorizontalItems"
            :action-data="slotProps.data"
          />
        </template>
      </Column>
    </GTable>

    <Drawer
      v-model:visible="isAddAdvertiserDrawerVisible"
      position="right"
      :dismissable="false"
      style="width: 45rem"
      pt:root:class="rounded-l-2xl px-4"
      :show-close-icon="false"
    >
      <template #header>
        <div class="w-full flex justify-between items-center mb-6">
          <span class="header-1 text-secondary-text">
            Advertiser Settings
          </span>
          <MaterialIcon
            icon="close"
            size="16px"
            class="bg-secondary-background p-2 rounded-full cursor-pointer"
            @click="isAddAdvertiserDrawerVisible = false"
          />
        </div>
      </template>

      <AdvertiserSettings :advertiser="selectedAdvertiserForSettings" />
    </Drawer>
  </div>
</template>
<style scoped>
h1 {
  @apply text-lg font-normal font-literata text-neutral-900;
}
</style>
