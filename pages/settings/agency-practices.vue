<script setup lang="ts">
import { ref, computed, watchEffect } from "vue";
import KnowledgeItem from "~/components/knowledge/KnowledgeItem.vue";
import { KnowledgeType, KnowledgeItemPayloadType } from "~/types/knowledge";
import type {
  AgencyKnowledge,
  NamingSettings,
  LineItemSettings,
} from "~/types/knowledge";

definePageMeta({
  layout: "default",
});

const agencyStore = useAgencyStore();
const agencyKnowledge = ref<AgencyKnowledge[]>([]);

const knowledgeByType = computed(() => {
  const knowledgeMap: Record<string, AgencyKnowledge> = {};
  agencyKnowledge.value?.forEach((item) => {
    knowledgeMap[item.knowledgeType] = item;
  });
  return knowledgeMap;
});

const namingSettings = computed(
  () =>
    knowledgeByType.value[KnowledgeType.NAMING_SETTINGS] as AgencyKnowledge & {
      payload: NamingSettings;
    }
);

const lineItemSettings = computed(
  () =>
    knowledgeByType.value[
      KnowledgeType.LINE_ITEM_SETTINGS
    ] as AgencyKnowledge & { payload: LineItemSettings }
);

const agencyAdditionalInfo = computed(
  () =>
    knowledgeByType.value[
      KnowledgeType.AGENCY_ADDITIONAL_INFO
    ] as AgencyKnowledge & { payload: { info: string } }
);
watchEffect(async () => {
  if (agencyStore.activeAgency) {
    await fetchAgencyKnowledge();
  }
});

async function fetchAgencyKnowledge() {
  if (!agencyStore.activeAgency) return;

  const [newAgencyKnowledge, error] = await useKnowledge().getAgencyKnowledge(
    agencyStore.activeAgency.id
  );
  if (error) {
    console.error("Failed to fetch agency knowledge:", error);
  } else {
    agencyKnowledge.value = newAgencyKnowledge;
  }
}

function handleKnowledgeItemSaved() {
  fetchAgencyKnowledge();
}
</script>

<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="main-container pt-0">
        <section class="section-layout pt-0">
          <div class="flex flex-col gap-4">
            <h1
              class="text-xl font-medium font-literata leading-none tracking-[-0.015em] text-primary-text m-0 -mt-0.5"
            >
              Organization Practices
            </h1>

            <p class="body-1 text-secondary-text flex-1">
              Using these inputs, Gigi will be tailored to your organizations in
              all aspects throughout the app, from campaign creation to deck
              generation.
            </p>
          </div>
        </section>

        <Divider class="my-0" />

        <section class="section-layout">
          <KnowledgeItem
            v-if="namingSettings"
            :item="namingSettings"
            title="Naming Conventions"
            description="Tell Gigi the way you like to breakdown your Order and Line Item names."
            direction="row"
            title-description-gap="large"
            @saved="handleKnowledgeItemSaved"
          />
        </section>

        <Divider class="my-0" />

        <section class="section-layout">
          <KnowledgeItem
            v-if="lineItemSettings"
            :item="lineItemSettings"
            title="Line Item Setup"
            description="Tell Gigi the way you like to split out and run line items for different campaign types."
            direction="row"
            title-description-gap="large"
            @saved="handleKnowledgeItemSaved"
          />
        </section>

        <Divider class="my-0" />

        <section class="section-layout">
          <KnowledgeItem
            v-if="agencyAdditionalInfo"
            :item="agencyAdditionalInfo"
            title="Additional Info"
            description="Give Gigi any additional context to help understand your practices as an organization."
            direction="row"
            title-description-gap="large"
            @saved="handleKnowledgeItemSaved"
          />
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.section-layout {
  @apply w-full;
}

.section-content {
  @apply flex flex-col md:flex-row items-start gap-6 w-full;
}

.section-left-column {
  @apply w-[215px] flex flex-col items-start gap-4;
}

.section-right-column {
  @apply flex-1 flex flex-col items-start;
}

.page-container {
  @apply w-full;
}

.content-wrapper {
  @apply flex justify-center w-full;
}

.main-container {
  @apply w-[900px] flex flex-col items-start gap-10 pr-6 pb-6;
}
</style>
