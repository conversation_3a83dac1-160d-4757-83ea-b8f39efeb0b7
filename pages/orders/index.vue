<script setup lang="ts">
import { ref } from "vue";
import { headerSelectPt } from "~/primevue-custom-pt";

const selectedAdvertiserFilter = ref<string[]>([]);
</script>

<template>
  <PageHeader title="Orders">
    <div class="flex flex-row items-center">
      <AdvertisersSelector
        :pt="headerSelectPt"
        @change="selectedAdvertiserFilter = $event"
      />
    </div>
  </PageHeader>

  <div class="flex flex-col flex-1 p-10 justify-start overflow-hidden">
    <OrdersTable :advertiser-ids="selectedAdvertiserFilter" />
  </div>
</template>
