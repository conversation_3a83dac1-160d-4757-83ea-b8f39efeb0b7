<script setup lang="ts">
import {
  asyncComputed,
  StorageSerializers,
  useLocalStorage,
} from "@vueuse/core";
import { endOfWeek, startOfDay, startOfWeek, subWeeks } from "date-fns";
import {
  DEFAULT_WEEK_RANGE_PRESETS,
  buildPresetDate,
  WEEK_CONFIG,
  type PresetDate,
} from "~/constants/weekRangePickerPresets";
import {
  getCampaignFlights,
  getCurrentFlight,
  getCampaignStartDate,
} from "~/utils/campaignFlights";
import { fetchCampaignFunnel } from "~/utils/advertiser-datasets/metrics/fetch-campaign-funnel";

definePageMeta({
  path: "/orders/:advertiserId/:campaignId",
});

const { params } = useRoute();
const advertiserId = computed(() => params.advertiserId as string);
const campaignId = computed(() => params.campaignId as string);

const campaignName = asyncComputed(
  async () => getCampaignName(campaignId.value),
  "loading..."
);

const campaignFunnel = asyncComputed(
  async () => fetchCampaignFunnel(advertiserId.value, campaignId.value),
  null
);

const flights = asyncComputed(
  async () => getCampaignFlights(campaignId.value, advertiserId.value),
  []
);

const currentFlight = computed(() => getCurrentFlight(flights.value));
const campaignStartDate = computed(() => getCampaignStartDate(flights.value));

const customPresetDates = computed((): PresetDate[] => {
  const presets: PresetDate[] = [];

  if (currentFlight.value?.startDateTime) {
    presets.push(
      buildPresetDate(
        "Current flight",
        startOfWeek(currentFlight.value.startDateTime, WEEK_CONFIG)
      )
    );
  }

  if (campaignStartDate.value) {
    presets.push(
      buildPresetDate(
        "Since campaign start",
        startOfWeek(campaignStartDate.value, WEEK_CONFIG)
      )
    );
  }

  return [...presets, ...DEFAULT_WEEK_RANGE_PRESETS];
});

const dateRange = ref({
  start: subWeeks(startOfWeek(new Date(), WEEK_CONFIG), 2),
  end: startOfDay(endOfWeek(subWeeks(new Date(), 1), WEEK_CONFIG)),
});

watch(
  campaignStartDate,
  (newStartDate) => {
    if (newStartDate) {
      dateRange.value.start = startOfWeek(newStartDate, WEEK_CONFIG);
    }
  },
  { immediate: true }
);

const breadcrumbItems = computed(() => [
  { label: "Orders", to: "/orders" },
  {
    label: campaignName.value,
  },
]);

async function getCampaignName(campaignId: string) {
  const response = await $fetch("/relay/rob/api/adsp/v1/campaigns/list", {
    method: "POST",
    body: {
      maxResults: 1,
      campaignIdFilter: [campaignId],
    },
    headers: {
      "Gigi-AmazonDSPAdvertiserId": advertiserId.value,
    },
  });

  return response.campaigns?.[0]?.name || "Campaign not found";
}

const { user } = useUserSession();
const viewType = useLocalStorage<"day" | "week">(
  await hashKey(`${user.value?.sub}:${campaignId.value}:orders:view-type`),
  "day",
  { serializer: StorageSerializers.object, initOnMounted: true }
);
</script>

<template>
  <PageHeader :breadcrumb-items="breadcrumbItems">
    <div class="flex flex-row justify-end items-center flex-1 gap-4">
      <DayWeekToggle v-model="viewType" />
      <DateRangePicker
        v-model="dateRange"
        :end-offset="0"
        :preset-dates="customPresetDates"
        :selection-mode="viewType"
      />
    </div>
  </PageHeader>

  <div class="flex flex-col p-10 gap-8 overflow-y-auto">
    <div class="flex flex-row items-center gap-4">
      <EntityAvatar
        :entity-id="advertiserId"
        entity-type="ADVERTISER"
        size="normal"
        shape="rounded"
        label-class="hidden"
      />
      <span class="header-1">{{ campaignName }}</span>
    </div>

    <CampaignSpendWidget
      v-if="currentFlight"
      :advertiser-id="advertiserId"
      :campaign-id="campaignId"
    />

    <PerformanceChart
      :advertiser-id="advertiserId"
      :campaign-id="campaignId"
      :funnel="campaignFunnel"
      :start-date="dateRange.start"
      :end-date="dateRange.end"
      :view-type="viewType"
    />

    <Divider />

    <PerformanceTable
      :advertiser-id="advertiserId"
      :campaign-id="campaignId"
      :funnel="campaignFunnel"
      :start-date="dateRange.start"
      :end-date="dateRange.end"
      :view-type="viewType"
    />
  </div>
</template>
