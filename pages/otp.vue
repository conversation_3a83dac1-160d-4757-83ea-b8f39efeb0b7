<script setup lang="ts">
definePageMeta({
  layout: "auth",
});

onMounted(async () => {
  const route = useRoute();
  const token = route.query.token;

  try {
    const { ok } = await $fetch<{ ok: boolean }>("/api/auth/otp", {
      query: {
        token,
      },
    });

    if (!ok) {
      await navigateTo("/login");
      return;
    }

    await useUserSession().fetch();
    await navigateTo("/");
  } catch (error) {
    console.error(error);
    await navigateTo("/login");
  }
});
</script>

<template>
  <h1>Waiting for OTP redirect...</h1>
</template>
