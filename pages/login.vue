<script setup lang="ts">
definePageMeta({
  layout: "auth",
});

const session = useUserSession();
if (session.loggedIn.value) {
  await navigateTo("/");
}
</script>
<template>
  <Card
    pt:root:class="flex flex-col justify-between w-96 bg-[rgba(255,251,246,0.60)]"
    pt:content:class="flex flex-col items-center justify-center p-4 gap-8"
  >
    <template #content>
      <div class="flex flex-col items-center justify-center gap-5">
        <img
          src="/public/images/gigi-avatar.svg"
          alt="Gigi Avatar"
          class="w-14"
        />
        <h1 class="text-3xl font-literata">Welcome</h1>
        <span class="body-2"> Login to continue to Gigi </span>
      </div>

      <div class="flex flex-col gap-6 w-full">
        <OauthButtons />
        <div class="flex items-center justify-between gap-4 w-full">
          <Divider />
          <span class="label-3">or</span>
          <Divider />
        </div>
        <MagicLink />

        <div class="mt-2 text-center">
          <a
            href="https://gigico.ai/"
            class="caption-2 text-secondary-text underline hover:text-primary"
            >Learn more about Gigi
          </a>
        </div>
      </div>
    </template>
  </Card>
</template>
