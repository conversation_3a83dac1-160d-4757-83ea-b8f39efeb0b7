<script setup lang="ts">
const route = useRoute();
const conversationId = route.params.conversationId as string;

const { conversation, initialize, destroy } = useExistingChat(conversationId);

provide("conversationId", conversationId);

const agencyStore = useAgencyStore();
const userSession = useUserSession();

watch(
  [() => agencyStore.activeAgency, () => userSession.user],
  async ([newAgency, newUser]) => {
    if (newAgency && newUser) {
      console.log("Chat: Initializing chat", { conversationId });
      await initialize();
    }
  },
  { immediate: true }
);

onBeforeUnmount(async () => {
  console.log("Chat: Destroying chat", { conversationId });
  await destroy();
});
</script>

<template>
  <PageHeader :title="conversation?.title || 'New Chat'" />

  <ChatArea :conversation-id="conversationId" />

  <ChatModal />
</template>

<style scoped>
.root {
  @apply flex flex-col flex-1 relative h-full justify-end overflow-hidden;
}

.messages-area {
  @apply flex-1;
}

.chat-input {
  @apply max-w-3xl mx-auto mb-6;
}

/* No conversation states */
.no-conversation {
  @apply flex-1 flex items-center justify-center;
}

.loading-state,
.error-state,
.initial-state {
  @apply flex flex-col items-center justify-center text-center p-8 max-w-md;
}

/* Loading state */
.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-200 border-t-neutral-500 rounded-full animate-spin mb-4;
}

.loading-text {
  @apply body-2 text-primary-text;
}

/* Error state */
.error-icon {
  @apply text-red-500;
}

.error-text {
  @apply body-2 text-red-500;
}

.error-subtitle {
  @apply body-1 text-secondary-text;
}

/* Initial state */
.initial-icon {
  @apply mb-4;
}

.initial-text {
  @apply body-2 text-primary-text;
}
</style>
