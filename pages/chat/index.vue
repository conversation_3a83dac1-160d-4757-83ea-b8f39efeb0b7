<script setup lang="ts">
import type { z } from "zod";
import { MessagePayloadSchema } from "~/types/chat/api-schemas";

type MessagePayload = z.infer<typeof MessagePayloadSchema>;

const { isBusy, createConversation } = useNewChat();

const messagePayload = ref<MessagePayload>({
  message: "",
  referencedResources: [],
});

async function handleSendMessage() {
  await createConversation({
    title: `${messagePayload.value.message.slice(0, 20)}...`,
    messagePayload: messagePayload.value,
    shouldNavigate: true,
  });

  messagePayload.value = {
    message: "",
    referencedResources: [],
  };
}
</script>

<template>
  <PageHeader title="New Chat" />

  <div class="root">
    <ChatInput
      class="chat-input"
      v-model="messagePayload"
      :is-busy="isBusy"
      @send-click="handleSendMessage"
    />
  </div>
</template>

<style scoped>
.root {
  @apply flex flex-col flex-1 relative h-full justify-end overflow-hidden;
}

.chat-input {
  @apply max-w-3xl mx-auto mb-6;
}
</style>
