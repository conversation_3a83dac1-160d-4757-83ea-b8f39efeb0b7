<script setup lang="ts">
import { FilePurposeSchema, type FileMetadata } from "~/types/api/storage";
import AdvertisersSelector from "~/components/common/AdvertisersSelector.vue";
import { dateText } from "~/utils/dateTextUtils";
import { headerSelectPt } from "~/primevue-custom-pt";

const { user } = useUserSession();
const robClient = useRob();

const selectedAdvertiserFilter = ref<string[]>([]);

const downloadOptions = [
  // Not implemented yet
  // {
  //   label: "PDF",
  //   icon: "insert_drive_file",
  // },
  {
    label: "ODP (universal)",
    icon: "dvr",
  },
];

const { data: filesMetadata, status: filesMetadataStatus } =
  await robClient.getFilesMetadata(
    FilePurposeSchema.Enum.DECK,
    user,
    selectedAdvertiserFilter
  );

async function downloadFile(file: FileMetadata) {
  const fileDownload = useFileDownload();

  const fileReferenceableId = `FILE_${file.id}`;
  const { blob, name } = await robClient.getReferenceableAsDocument(
    fileReferenceableId
  );

  fileDownload.fromBlob(blob, name ?? file.fileName);
}
</script>

<template>
  <PageHeader title="Decks">
    <AdvertisersSelector
      :pt="headerSelectPt"
      @change="selectedAdvertiserFilter = $event"
    />
  </PageHeader>

  <div class="flex flex-col flex-1 p-10 justify-start overflow-hidden">
    <GTable
      :data="filesMetadata || []"
      data-key="id"
      header
      :key="filesMetadataStatus"
      :loading="filesMetadataStatus === 'pending'"
      class="flex-1 min-h-0 table-fixed"
    >
      <Column header="Name">
        <template #body="{ data }: { data: FileMetadata }">
          <div class="w-full leading-snug line-clamp-2" :title="data.fileName">
            {{ data.fileName }}
          </div>
        </template>
      </Column>

      <Column header="Created For" style="width: 30%">
        <template #body="{ data }: { data: FileMetadata }">
          <EntityAvatar
            :entity-id="data.ownerId"
            :entity-type="data.ownerType"
          />
        </template>
      </Column>

      <Column header="Created At" style="width: 30%">
        <template #body="{ data }">
          <span class="body-1">{{ dateText(data.updatedAt) }}</span>
        </template>
      </Column>

      <Column class="w-16 min-w-16 max-w-16">
        <template #body="{ data }: { data: FileMetadata }">
          <PopoverButton prefix-icon="more_horiz" size="small">
            <div class="flex flex-col">
              <span class="label-3 p-4">Download as...</span>

              <button
                v-for="option in downloadOptions"
                :key="option.label"
                class="px-4 py-2 flex flex-row items-center gap-4 hover:bg-secondary-background cursor-pointer"
                @click="downloadFile(data)"
              >
                <MaterialIcon :icon="option.icon" />
                <span class="body-1">{{ option.label }}</span>
              </button>
            </div>
          </PopoverButton>
        </template>
      </Column>
    </GTable>
  </div>
</template>
