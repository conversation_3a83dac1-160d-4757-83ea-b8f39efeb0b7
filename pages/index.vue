<script setup lang="ts">
import { ref, computed } from "vue";
import { headerSelectPt } from "~/primevue-custom-pt";
import {
  DeliveryItemSortOption,
  DeliveryItemTimeFilter,
} from "~/types/delivery-item/filter";

const { timeFilterConfig } = useDeliveryItemConfig();

const timeFilterOptions = computed(() => {
  return Object.entries(timeFilterConfig.value).map(([key, value]) => ({
    label: value,
    value: key,
  }));
});

const selectedAdvertiserFilter = ref<string[]>([]);
const selectedTimeFilter = ref(DeliveryItemTimeFilter.LAST_7_DAYS.toString());

// 移除了旧的聊天相关代码，现在使用 NewChatInput 组件

const {
  items: feedItems,
  status,
  error,
} = useFeed(
  selectedAdvertiserFilter,
  selectedTimeFilter,
  computed(() => DeliveryItemSortOption.CHRONOLOGICAL)
);

</script>

<template>
  <PageHeader title="Home Feed">
    <div class="flex flex-row items-center">
      <AdvertisersSelector
        :pt="headerSelectPt"
        @change="selectedAdvertiserFilter = $event"
      />

      <Select
        :pt="headerSelectPt"
        v-model="selectedTimeFilter"
        :options="timeFilterOptions"
        option-label="label"
        option-value="value"
      />
    </div>
  </PageHeader>

  <div class="flex w-full overflow-y-auto py-10">
    <div class="flex flex-col w-full max-w-[42rem] mx-auto gap-4">
      <NewChatInput />

      <Divider />

      <div v-if="status === 'error'" class="flex flex-col gap-4">
        <Message
          severity="error"
          :closable="false"
          pt:root:class="inline-flex gap-4 items-center"
        >
          <template #icon>
            <MaterialIcon icon="error" />
          </template>
          <span class="text-sm">
            No content available from any service. Please try again later.
          </span>
        </Message>
      </div>

      <div
        v-else-if="feedItems && feedItems.length === 0 && status === 'success'"
        class="flex flex-col gap-4"
      >
        <Message
          severity="info"
          :closable="false"
          pt:root:class="inline-flex gap-4 items-center"
        >
          <template #icon>
            <MaterialIcon icon="error" />
          </template>
          <span class="text-sm">No feed items found</span>
        </Message>
      </div>

      <div v-else class="flex flex-col gap-4 pb-10">
        <DeliveryItem v-for="item in feedItems || []" :key="item.id" :item="item" />

        <DeliveryItemSkeleton
          v-if="status === 'pending'"
          v-for="i in 3"
          :key="i"
        />
      </div>
    </div>
  </div>
</template>
