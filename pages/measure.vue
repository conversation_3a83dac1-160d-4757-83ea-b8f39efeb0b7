<script setup lang="ts">
import { endOfWeek, startOfWeek, subWeeks, subYears } from "date-fns";
import { DEFAULT_WEEK_RANGE_PRESETS } from "~/constants/weekRangePickerPresets";

import { headerSelectPt } from "~/primevue-custom-pt";

const advertiserStore = useAdvertiserStore();

const selectedAdvertiserFilter = ref<string[]>([]);
const advertiser = computed(() =>
  selectedAdvertiserFilter.value.length === 1
    ? advertiserStore.getAdvertiserById(selectedAdvertiserFilter.value[0])
    : undefined
);

const dateRange = ref({
  start: subWeeks(startOfWeek(new Date(), { weekStartsOn: 1 }), 2),
  end: endOfWeek(subWeeks(new Date(), 1), { weekStartsOn: 1 }),
});
</script>

<template>
  <PageHeader title="Measure">
    <div class="flex flex-row items-center justify-start gap-4 w-full">
      <AdvertisersSelector
        :pt="headerSelectPt"
        single-account
        @change="selectedAdvertiserFilter = $event"
      />

      <DateRangePicker
        class="ml-auto"
        v-model="dateRange"
        :end-offset="0"
        selection-mode="week"
        :min-date="subYears(new Date(), 5)"
        :max-date="endOfWeek(subWeeks(new Date(), 1), { weekStartsOn: 1 })"
      />
    </div>
  </PageHeader>

  <div v-if="!advertiser">
    <div class="flex flex-col items-center justify-center h-full">
      <div class="text-2xl font-bold">No advertiser found</div>
    </div>
  </div>
  <div v-else class="flex flex-1 overflow-auto">
    <div class="p-6 lg:p-10 min-w-[660px] w-full">
      <div class="flex flex-col gap-6 lg:gap-10 w-full">
        <div class="flex flex-col lg:flex-row gap-6 lg:gap-10">
          <FunnelBy
            class="flex-1 min-w-0"
            :advertiserId="advertiser.advertiserId"
            :currencyCode="advertiser.currencyCode"
            :startDate="dateRange.start"
            :endDate="dateRange.end"
          />

          <CACAndLTV
            class="flex-1 min-w-0"
            :advertiserId="advertiser.advertiserId"
            :currencyCode="advertiser.currencyCode"
            :startDate="dateRange.start"
            :endDate="dateRange.end"
          />
        </div>

        <CacSalesNtbAsin
          :advertiserId="advertiser.advertiserId"
          :currencyCode="advertiser.currencyCode"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <CacSalesNtbCampaign
          :advertiserId="advertiser.advertiserId"
          :currencyCode="advertiser.currencyCode"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <PurchaseOverlap
          :advertiserId="advertiser.advertiserId"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <CrossSellAsins
          :advertiserId="advertiser.advertiserId"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <TopSearchTerms
          :advertiserId="advertiser.advertiserId"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <BrandedSearchOverlap
          :advertiserId="advertiser.advertiserId"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <NTBOverlap
          :advertiserId="advertiser.advertiserId"
          :startDate="dateRange.start"
          :endDate="dateRange.end"
        />

        <div class="h-20 w-full"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chart-container {
  @apply flex w-full border rounded flex-grow min-w-96;
}
</style>
