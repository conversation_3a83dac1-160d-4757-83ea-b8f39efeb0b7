# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- **Install dependencies**: `pnpm install`
- **Clean install**: `pnpm run clean-install` (removes .nuxt, .output, and reinstalls)

### Development
- **Start dev server**: `pnpm dev` (runs on http://localhost:3000)
- **Build for production**: `pnpm build`
- **Preview production**: `pnpm preview`
- **Generate static site**: `pnpm generate`

### Testing
- This project currently does not have automated tests configured. When adding tests, check if a test framework is available in package.json or ask the user for the preferred testing approach.

## Architecture Overview

This is a sophisticated advertising analytics and management platform built on Nuxt.js with AI-powered chat functionality. The application follows a microservices architecture with a frontend proxy layer.

### Core Technologies
- **Framework**: Nuxt.js 3.15.4 with Vue 3.5.13
- **State Management**: Pinia with reactive composition API
- **UI Library**: PrimeVue 4.2.5 with TailwindCSS
- **Authentication**: JWT-based with token refresh
- **Real-time**: Server-Sent Events (SSE) for live updates
- **Package Manager**: pnpm (required for this project)

### Key Architectural Patterns

#### 1. Authentication System
- JWT-based authentication with RS256 tokens
- Token refresh with debounced concurrent request handling
- Multiple auth providers (Google OAuth, magic links)
- Session management: server-side JWT tokens, client-side user data
- Global middleware protection with public route exceptions

#### 2. API Architecture
- **Proxy Pattern**: Frontend proxies to microservices via `/relay/{serviceId}/{pathAtService}`
- **Service Registry**: Centralized service configuration and routing
- **Security Layers**: Role-based access control with admin overrides
- **Key Services**:
  - `mms`: User management service
  - `rob`: Core business logic service
  - `linda`: AI chat service

#### 3. Data Layer
- **Dataset Manager**: Sophisticated caching system with dataset abstraction
- **Two-tier Caching**: Dataset-level and requirement-level caching
- **Smart Loading**: Promise deduplication to prevent duplicate requests
- **Scope Management**: Agency/advertiser-aware data fetching
- **Transformation Layer**: Configurable data transformers

#### 4. Real-time Communication
- **Server-Sent Events**: Topic-based subscription system
- **SSE Mediator**: Central event coordination and broadcasting
- **Client Registry**: Connection management with automatic cleanup
- **Pinia Integration**: Reactive updates through store integration

#### 5. Component Organization
- **Feature-based**: Components organized by business domain
- **Major Sections**:
  - `Chat/`: Real-time messaging with AI integration
  - `measurements/`: Analytics and performance metrics
  - `performance/`: Campaign performance dashboards
  - `advertiser-settings/`: Configuration management
  - `delivery-item/`: Action items and notifications
  - `knowledge/`: Knowledge base management
  - `common/`: Reusable UI components

### Multi-tenancy and Scoping
- **Agency → Advertiser Hierarchy**: Data and permissions scoped by agency/advertiser
- **Scope Headers**: Automatic header forwarding for multi-tenant requests
- **Filtered Data**: Stores and composables respect agency/advertiser boundaries

### File Structure Guidelines
- `composables/`: Reusable logic with `use` prefix
- `stores/`: Pinia stores for global state management
- `types/`: TypeScript definitions organized by domain
- `utils/`: Utility functions and helpers
- `server/`: Backend API routes and middleware
- `components/`: UI components organized by feature

### Development Notes
- **Security**: Never commit sensitive data (API keys, tokens) to repository
- **Styling**: Uses TailwindCSS with PrimeVue components and custom design tokens
- **TypeScript**: Strict typing with Zod for runtime validation
- **Environment**: Uses runtime config for environment variables (NUXT_* prefix)

### Data Fetching Patterns
- Use `useAsyncData` for server-side data fetching
- Leverage dataset manager for complex data requirements
- Implement proper error handling with `tryWithToast` composable
- Cache data appropriately to reduce API calls

### Component Development
- Follow composition API patterns with `<script setup>`
- Use PrimeVue components with custom styling
- Implement proper TypeScript typing for props and emits
- Follow existing naming conventions and file organization

### Authentication Integration
- Use `useAuth` composable for authentication logic
- Implement route protection with middleware
- Handle token refresh gracefully
- Respect user permissions and scoping