export const lindaResponses: ChatMessage[] = [
    createMessage('I am fine, thank you!', []),
    createMessage('Here is the report for your request.', [
        { referenceId: '/gigi-deck-2025.pdf', name: 'gigi-deck-2025.pdf' },
        // { referenceId: '2', name: 'report.xlsx' },
        // { referenceId: '3', name: 'report.csv' },
        // { referenceId: '4', name: 'report.docx' },
        // { referenceId: '5', name: 'report.pptx' },
        // { referenceId: '6', name: 'report.txt' },
        // { referenceId: '7', name: 'report.html' },
        // { referenceId: '8', name: 'report.json' },
        // { referenceId: '9', name: 'report.xml' },
        // { referenceId: '10', name: 'report.yaml' },
    ]),
    createMessage(`Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris convallis erat mauris, vitae fringilla justo consectetur eget. Mauris in tincidunt orci. Integer at felis vel dui lobortis maximus ac eu arcu. Nam auctor sed massa vitae venenatis. Vestibulum ultricies lobortis tincidunt. Aenean elementum gravida arcu, nec ullamcorper sapien condimentum id. Proin porttitor tincidunt lacus, eu fermentum massa aliquam nec. Cras vel volutpat nulla. Maecenas consequat rhoncus sem, eu faucibus sem laoreet a. Praesent suscipit dignissim nulla, id mollis leo porttitor eu. In blandit condimentum erat, id elementum nisl pellentesque eu. Proin id volutpat est, nec iaculis orci.`, []),
]

let i = 0;
export function getNextMessage(): ChatMessage {
    const message = { ...lindaResponses[i++ % lindaResponses.length] };
    message.id = crypto.randomUUID();
    message.timestamp = new Date();
    return message;
}

function createMessage(messageContent: string, referencedFiles: AttachmentMetadata[]): ChatMessage {
    return {
        identity: {
            role: ChatParticipant.LINDA,
            model: 'gpt-4o',
        },
        workflow: ChatWorkflow.REPORT_GENERATION,
        conversationId: '1',
        messageContent: messageContent,
        referencedFiles: referencedFiles,
    }
}