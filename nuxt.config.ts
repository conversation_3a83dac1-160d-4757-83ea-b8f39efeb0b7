// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2024-11-01",
  devtools: { enabled: true },

  app: {
    head: {
      link: [
        { rel: "icon", type: "image/svg+xml", href: "/images/gigi-avatar.svg" },
      ],
    },
  },

  css: ["~/assets/css/main.css"],

  components: [{ path: "~/components", pathPrefix: false }],

  modules: [
    "@nuxtjs/tailwindcss",
    "@primevue/nuxt-module",
    "@pinia/nuxt",
    "json-editor-vue/nuxt",
    "nuxt-auth-utils",
    "@nuxtjs/google-fonts",
  ],

  googleFonts: {
    families: {
      Literata: [400, 500, 600],
      Inter: [400, 500, 600],
      "Fragment Mono": [400],
      "Material Symbols Outlined": [300, 400, 500, 600],
    },
  },

  imports: {
    dirs: ["types/*.ts", "composables/**"],
  },

  runtimeConfig: {
    selfAccessToken: process.env.NUXT_SELF_ACCESS_TOKEN,
    lindaApiUrl: process.env.NUXT_LINDA_API_URL,
    lindaApiKey: process.env.NUXT_LINDA_API_KEY,
    robApiUrl: process.env.NUXT_ROB_API_URL,
    robApiKey: process.env.NUXT_ROB_API_KEY,
    mmsApiUrl: process.env.NUXT_MMS_API_URL,
    mmsApiKey: process.env.NUXT_MMS_API_KEY,
    metricsApiUrl: process.env.NUXT_METRICS_API_URL,
    metricsApiKey: process.env.NUXT_METRICS_API_KEY,
    aesKey: process.env.NUXT_AES_KEY,
    aesIv: process.env.NUXT_AES_IV,
    queueWorkerCount: process.env.NUXT_QUEUE_WORKER_COUNT
      ? parseInt(process.env.NUXT_QUEUE_WORKER_COUNT)
      : 5,
    public: {
      googleClientId: process.env.NUXT_PUBLIC_GOOGLE_CLIENT_ID,
    },
  },

  primevue: {
    options: {
      theme: "none",
    },
  },

  pinia: {
    storesDirs: ["./stores/**"],
  },

  pages: true,
});
