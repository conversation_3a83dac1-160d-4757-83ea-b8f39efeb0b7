async function exportTable(table: HTMLElement) {
  console.log("Enhance table: exporting table");

  // 1. Build CSV string
  const rows = Array.from(table.querySelectorAll("tr")).map((tr) => {
    const cells = Array.from(tr.querySelectorAll("th, td")).map((cell) => {
      // trim and escape inner quotes
      const txt = cell.textContent?.trim().replace(/"/g, '""') ?? "";
      return `"${txt}"`;
    });
    return cells.join(",");
  });
  const csvString = rows.join("\r\n");
  const csvBlob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });

  // 2. Let user pick file name & location
  if ("showSaveFilePicker" in window) {
    try {
      const handle = await (window as any).showSaveFilePicker({
        suggestedName: "table.csv",
        types: [
          {
            description: "CSV Files",
            accept: { "text/csv": [".csv"] },
          },
        ],
      });
      const writable = await handle.createWritable();
      await writable.write(csvBlob);
      await writable.close();
      console.log("Table saved via File System API");
    } catch (err: any) {
      if (err.name === "AbortError") {
        console.log("Save canceled by user");
      } else {
        console.error("Error saving file:", err);
        alert("Failed to save file: " + err.message);
      }
    }
  } else {
    // fallback: prompt for filename, then download via anchor
    const filename = prompt("Enter file name", "table.csv");
    if (!filename) {
      console.log("Save canceled (no filename)");
      return;
    }
    const url = URL.createObjectURL(csvBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename.endsWith(".csv") ? filename : filename + ".csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log("Table saved via download link");
  }
}

function makeButtonContainer() {
  const container = document.createElement("div");
  container.classList.add("enhance-table-button-container");
  return container;
}

function makeButton(icon: string) {
  const button = document.createElement("button");
  button.classList.add("enhance-table-button");
  button.style.fontSize = "0.875rem";
  button.innerText = icon;
  return button;
}

function wrapTable(
  table: HTMLElement,
  toggleIcon: string,
  toggleCallback: () => void,
  showButtons: boolean = true
) {
  // Create viewport container (outer container)
  const viewport = document.createElement("div");
  viewport.classList.add("enhance-table-viewport");

  // Create wrapper (inner container for scrolling)
  const wrapper = document.createElement("div");
  wrapper.classList.add("enhance-table-wrapper");

  // Replace table with viewport, then add wrapper and table
  table.parentNode?.replaceChild(viewport, table);
  viewport.appendChild(wrapper);
  wrapper.appendChild(table);

  if (showButtons) {
    const buttonsContainer = makeButtonContainer();

    const toggleButton = makeButton(toggleIcon);
    const exportButton = makeButton("download");

    buttonsContainer.appendChild(exportButton);
    buttonsContainer.appendChild(toggleButton);

    toggleButton.addEventListener("click", toggleCallback);

    exportButton.addEventListener("click", () => {
      console.log("Enhance table: export button clicked");
      exportTable(table);
    });

    viewport.appendChild(buttonsContainer);
  }

  return { viewport, wrapper };
}

function openModal(table: HTMLElement) {
  const backdrop = document.createElement("div");
  backdrop.classList.add("enhance-table-backdrop");

  const modal = document.createElement("div");
  modal.classList.add("enhance-table-modal");
  modal.setAttribute("role", "dialog");

  const tableClone = table.cloneNode(true) as HTMLElement;
  modal.appendChild(tableClone);

  // Wrap the cloned table with collapse functionality
  wrapTable(tableClone, "collapse_content", () => backdrop.remove(), true);

  const closeModal = () => {
    backdrop.remove();
    document.removeEventListener("keydown", closeModal);
  };

  backdrop.addEventListener("click", (e) => {
    if (e.target === backdrop) {
      closeModal();
    }
  });

  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape") {
      closeModal();
    }
  });

  backdrop.appendChild(modal);
  document.body.appendChild(backdrop);
}

function enhanceTable(el: HTMLElement) {
  el.querySelectorAll("table:not([data-enhanced])").forEach((table) => {
    console.log("Enhance table: found table", table.id);
    table.setAttribute("data-enhanced", "true");

    const isInDialog = !!table.closest("div[role='dialog']");

    if (!isInDialog) {
      // Wrap the table with expand functionality
      wrapTable(
        table as HTMLElement,
        "expand_content",
        () => {
          console.log("Enhance table: expand button clicked");
          openModal(table as HTMLElement);
        },
        true
      );
    }
  });
}

export default {
  mounted(el: HTMLElement) {
    console.log("Enhance table: directive mounted");
    enhanceTable(el);
  },
  updated(el: HTMLElement) {
    console.log("Enhance table: directive updated");
    enhanceTable(el);
  },
};
