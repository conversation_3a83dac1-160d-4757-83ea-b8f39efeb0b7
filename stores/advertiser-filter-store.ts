import { StorageSerializers, useSessionStorage } from "@vueuse/core";

interface Option {
  label: string;
  value: string[];
}

export const useAdvertiserFilterStore = defineStore("advertiserFilter", () => {
  const advertiserStore = useAdvertiserStore();

  const persistedValue = useSessionStorage<string[]>("advertiserFilter", [], {
    serializer: StorageSerializers.object,
  });

  const value = ref<string[]>([]);

  const singleAccount = ref(false);

  const options = computed<Option[] | undefined>(() => {
    const advertisers = advertiserStore.data;

    if (!advertisers || advertisers.length === 0) {
      return;
    }

    const individualAdvertiserOptions = advertisers.map((advertiser) => ({
      label: advertiser.amazonDspAdvertiserName,
      value: [advertiser.advertiserId],
    }));

    if (individualAdvertiserOptions.length === 1)
      return individualAdvertiserOptions;

    if (singleAccount.value) {
      return individualAdvertiserOptions;
    }

    const allAccountsValue = advertisers.map((a) => a.advertiserId);
    return [
      {
        label: "All Accounts",
        value: allAccountsValue,
      },
      ...individualAdvertiserOptions,
    ];
  });

  const status = ref<"loading" | "ready">("loading");

  watch([options, persistedValue], ([newOptions, _], [oldOptions, __]) => {
    switch (status.value) {
      case "loading":
        handleLoading();
        break;
      case "ready":
        handleReady(newOptions, oldOptions);
        break;
    }
  });

  watch(value, () => {
    if (status.value === "ready") {
      persistedValue.value = value.value;
    }
  });

  function handleLoading() {
    if (
      advertiserStore.status !== "success" ||
      !options.value ||
      options.value.length === 0
    ) {
      return;
    }

    const matchedOption = options.value?.find(
      (option) =>
        JSON.stringify(option.value) === JSON.stringify(persistedValue.value)
    );
    value.value = matchedOption?.value || getFallbackValue();

    status.value = "ready";
  }

  function handleReady(newOptions?: Option[], oldOptions?: Option[]) {
    if (JSON.stringify(newOptions) === JSON.stringify(oldOptions)) return;

    let newValue = getFallbackValue();
    if (value.value.length === 1) {
      const preservedValue = getPreservedValue(value.value[0], newOptions);
      if (preservedValue) {
        newValue = preservedValue;
      }
    }

    value.value = newValue;
    status.value = "ready";
  }

  function getPreservedValue(currentValue: string, newOptions?: Option[]) {
    return newOptions
      ?.filter((op) => op.value.length === 1)
      .find((op) => op.value.includes(currentValue))?.value;
  }

  function getFallbackValue() {
    return options.value?.[0].value || [];
  }

  function setSingleAccount(newValue: boolean) {
    singleAccount.value = newValue;
  }

  return { value, options, status, singleAccount, setSingleAccount };
});
