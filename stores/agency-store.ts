import { useSessionStorage } from "@vueuse/core";

export const useAgencyStore = defineStore("agency", () => {
  const { loggedIn, user } = useUserSession();
  const persistedActiveAgencyId = useSessionStorage<string | undefined>(
    "activeAgencyId",
    undefined
  );

  async function fetchAgencies(): Promise<Agency[]> {
    if (!loggedIn.value || !user.value) return [];
    return await $fetch<GetAgenciesResponse>(
      "/relay/mms/api/resource/scoped/agencies"
    );
  }

  const activeAgency = ref<Agency | undefined>(undefined);

  const { data, error, status } = useAsyncData<GetAgenciesResponse>(
    "agencies",
    fetchAgencies,
    {
      watch: [loggedIn, user],
      default: () => [],
    }
  );

  watch(data, handleNewAgencies);

  watch(activeAgency, () => {
    if (activeAgency.value) {
      persistedActiveAgencyId.value = activeAgency.value.id;
    }
  });

  function handleNewAgencies(newAgencies: GetAgenciesResponse) {
    activeAgency.value = determineNewActiveAgency(
      newAgencies,
      activeAgency.value,
      persistedActiveAgencyId.value
    );
  }

  function determineNewActiveAgency(
    newAgencies: GetAgenciesResponse,
    currentActiveAgency: Agency | undefined,
    persistedId: string | undefined
  ): Agency | undefined {
    const agency = currentActiveAgency
      ? getAgencyById(currentActiveAgency.id)
      : getAgencyById(persistedId);

    return agency || newAgencies?.[0];
  }

  function getAgencyById(agencyId?: string) {
    return data.value?.find((agency) => agency.id === agencyId);
  }

  function setActiveAgencyById(agencyId?: string) {
    if (!agencyId) {
      activeAgency.value = undefined;
      return;
    }

    activeAgency.value = getAgencyById(agencyId);
  }

  return {
    activeAgency,
    data,
    error,
    status,
    setActiveAgencyById,
    getAgencyById,
  };
});
