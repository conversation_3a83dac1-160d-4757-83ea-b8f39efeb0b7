export const useAdvertiserStore = defineStore("advertiser", () => {
  const agencyStore = useAgencyStore();

  const { data, error, status } = useAsyncData<GetAdvertisersResponse>(
    "advertisers",
    fetchAdvertisers,
    {
      watch: [() => agencyStore.activeAgency],
      default: () => [],
    }
  );

  async function fetchAdvertisers() {
    const agencyId = agencyStore.activeAgency?.id;
    if (!agencyId) return [];

    return await $fetch(`/relay/mms/api/resource/scoped/advertisers`, {
      query: { agencyId },
    });
  }

  function getAdvertiserById(advertiserId?: string) {
    return data.value?.find(
      (advertiser) => advertiser.advertiserId === advertiserId
    );
  }

  return { data, error, status, getAdvertiserById };
});
