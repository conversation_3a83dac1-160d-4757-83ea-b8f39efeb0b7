import type {
  LocalConversation,
  MessageStateExtension,
  StatefulMessage,
} from "@/types/chat/models";

export const useConversationStore = defineStore("conversation-store", () => {
  const conversations = ref<Map<string, LocalConversation>>(new Map());
  const isLoading = ref(false);
  const error = ref<Error | null>(null);

  const sortedConversations = computed(() => {
    return Array.from(conversations.value.values()).sort(
      (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  });

  function getConversationById(id: string) {
    return conversations.value.get(id);
  }

  function addConversation(conversation: LocalConversation) {
    conversations.value.set(conversation.id, conversation);
  }

  function updateConversation(
    conversationId: string,
    updates: Partial<LocalConversation>
  ) {
    const conversation = conversations.value.get(conversationId);
    if (conversation) {
      conversations.value.set(conversationId, {
        ...conversation,
        ...updates,
        updatedAt: new Date(),
      });
    }
  }

  function deleteConversation(id: string) {
    conversations.value.delete(id);
  }

  function updateMessage(
    conversationId: string,
    messageId: string,
    newState: MessageStateExtension
  ) {
    const conversation = conversations.value.get(conversationId);
    if (conversation) {
      const updatedMessages = conversation.messages.map((msg) =>
        msg.id === messageId ? { ...msg, ...newState } : msg
      );
      conversations.value.set(conversationId, {
        ...conversation,
        messages: updatedMessages,
      });
    }
  }

  function updateMessageParts(
    conversationId: string,
    messageId: string,
    parts: any[]
  ) {
    const conversation = conversations.value.get(conversationId);
    if (conversation) {
      const updatedMessages = conversation.messages.map((msg) =>
        msg.id === messageId ? { ...msg, parts } : msg
      );
      conversations.value.set(conversationId, {
        ...conversation,
        messages: updatedMessages,
      });
    }
  }

  function clearAll() {
    conversations.value.clear();
  }

  function setLoading(loading: boolean) {
    isLoading.value = loading;
  }

  function setError(err: Error | null) {
    error.value = err;
  }

  return {
    conversations: sortedConversations,
    isLoading,
    error,
    getConversationById,
    addConversation,
    updateConversation,
    deleteConversation,
    updateMessage,
    updateMessageParts,
    clearAll,
    setLoading,
    setError,
  };
});
