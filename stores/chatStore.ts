import type { ReferencedResource } from "~/types/attachment";
import type { ChatMessagePayload } from "~/types/conversation";

export const useChatStore = defineStore("chatInput", () => {
  // State
  const state = ref<ChatState>(ChatState.IDLE);
  const messagePayload = ref<Required<ChatMessagePayload>>({
    message: "",
    referencedResources: [],
  });
  const error = ref<Error>();

  // Getters
  const isSending = computed(() =>
    [
      ChatState.CREATING_CONVERSATION,
      ChatState.CREATING_USER_MESSAGE,
      ChatState.GENERATING_RESPONSE,
    ].includes(state.value)
  );

  // Methods
  const setInput = (newContent: Partial<ChatMessagePayload>) => {
    messagePayload.value = {
      ...messagePayload.value,
      ...newContent,
    };
  };

  const addAttachment = (attachment: ReferencedResource) => {
    const attachments = [...messagePayload.value.referencedResources];
    attachments.push(attachment);
    setInput({
      referencedResources: attachments,
    });
  };

  const resetInput = () => {
    messagePayload.value = {
      message: "",
      referencedResources: [],
    };
  };

  return {
    messagePayload,
    state,
    error,
    isSending,
    setInput,
    addAttachment,
    resetInput,
  };
});
