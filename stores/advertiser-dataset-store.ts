import { AdvertiserDatasetManager } from "~/utils/advertiser-datasets/advertiser-dataset-manager";
import type { DataRequirement } from "~/utils/advertiser-datasets/data-requirement";

// stores/advertiser-metrics-store.ts
export const useAdvertiserDatasetStore = defineStore(
  "advertiserDataset",
  () => {
    const config = ref({
      cacheTtlMinutes: 15,
      maxAdvertisers: 10,
      maxDatasetsPerAdvertiser: 50,
      maxRequirementsPerAdvertiser: 25,
    });

    const managersCache = new Map<string, AdvertiserDatasetManager>();

    const fulfillRequirement = <T>(
      requirement: DataRequirement<T>
    ): Promise<T> => {
      const manager = getOrCreateManager(requirement.args.advertiserId);
      return manager.fulfillRequirement(requirement);
    };

    const getOrCreateManager = (advertiserId: string) => {
      if (!managersCache.has(advertiserId)) {
        if (managersCache.size >= config.value.maxAdvertisers) {
          cleanOldestAdvertisers();
        }

        managersCache.set(
          advertiserId,
          new AdvertiserDatasetManager(
            advertiserId,
            config.value.cacheTtlMinutes,
            config.value.maxDatasetsPerAdvertiser,
            config.value.maxRequirementsPerAdvertiser
          )
        );
      }

      return managersCache.get(advertiserId)!;
    };

    const cleanOldestAdvertisers = () => {
      // Simple LRU-style cleanup - could be more sophisticated
      const targetSize = Math.floor(config.value.maxAdvertisers * 0.7);
      const toDelete = managersCache.size - targetSize;

      let deleted = 0;
      for (const [advertiserId] of managersCache) {
        if (deleted >= toDelete) break;
        managersCache.delete(advertiserId);
        deleted++;
      }
    };

    return {
      config,
      fulfillRequirement,
      clearCache: () => managersCache.clear(),
      getCacheStats: () => {
        return Array.from(managersCache.entries()).map(
          ([advertiserId, manager]) => manager.getCacheStats()
        );
      },
    };
  }
);
