import type { TopicInfo } from "@/types/server-events";
import {
  SSEMediator,
  type ServerEventName,
  type SSEEventListener,
} from "@/utils/sse";

export const useServerEventsStore = defineStore("serverEvents", () => {
  const mediator = new SSEMediator();

  // Initialize the mediator
  mediator.initialize({
    baseUrl: "", // Will be relative to current domain
  });

  // Expose reactive state from the mediator
  const topics = mediator.activeTopics;
  const status = mediator.connectionStatus;

  function connect() {
    mediator.connect();
  }

  function disconnect() {
    mediator.disconnect();
  }

  function on(eventName: ServerEventName, listener: SSEEventListener) {
    mediator.on(eventName, listener);
  }

  function off(eventName: ServerEventName, listener: SSEEventListener) {
    mediator.off(eventName, listener);
  }

  async function subscribeToTopic(topicsInfo: TopicInfo | TopicInfo[]) {
    await mediator.subscribeToTopic(topicsInfo);
  }

  async function unsubscribeFromTopic(topicsInfo: TopicInfo | TopicInfo[]) {
    await mediator.unsubscribeFromTopic(topicsInfo);
  }

  async function unsubscribeFromAllTopics() {
    await mediator.unsubscribeFromAllTopics();
  }

  // Cleanup on store destruction
  onScopeDispose(() => {
    mediator.destroy();
  });

  return {
    topics,
    status,
    connect,
    disconnect,
    on,
    off,
    subscribeToTopic,
    unsubscribeFromTopic,
    unsubscribeFromAllTopics,
  };
});
