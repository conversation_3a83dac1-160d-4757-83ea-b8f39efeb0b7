<!DOCTYPE html>
<html>
<head>
    <title>Textarea Fix Test</title>
</head>
<body>
    <h1>Organization Practices Textarea Fix Test</h1>
    
    <h2>修复总结</h2>
    <p>修复了 Organization Practices 页面中三个输入字段无法显示文本的问题：</p>
    
    <h3>修复的文件：</h3>
    <ul>
        <li><strong>components/knowledge/NamingSettingsKnowledgeItem.vue</strong> - 修复了两个 Textarea 组件</li>
        <li><strong>components/knowledge/LineItemSettingsKnowledgeItem.vue</strong> - 修复了一个 Textarea 组件</li>
        <li><strong>components/knowledge/AgencyAdditionalInfoKnowledgeItem.vue</strong> - 修复了一个 Textarea 组件</li>
    </ul>
    
    <h3>问题原因：</h3>
    <p>这些组件使用了 <code>v-model</code> 在 <code>v-slot</code> 作用域变量上，但这些变量是只读的。</p>
    
    <h3>解决方案：</h3>
    <p>将 <code>v-model="editableData"</code> 改为 <code>:value="editableData"</code> 并使用 <code>@update:modelValue</code> 事件来更新数据。</p>
    
    <h3>修复前：</h3>
    <pre><code>&lt;Textarea
  v-model="editableData"
  :class="textareaClasses(isDefaultContent)"
  placeholder="..."
  @update:modelValue="(value) => { updateData(value); onInput(); }"
/&gt;</code></pre>
    
    <h3>修复后：</h3>
    <pre><code>&lt;Textarea
  :value="editableData"
  :class="textareaClasses(isDefaultContent)"
  placeholder="..."
  @update:modelValue="(value) => { updateData(value); onInput(); }"
/&gt;</code></pre>
    
    <h3>现在应该能正常工作：</h3>
    <ul>
        <li>✅ 文本内容正确显示</li>
        <li>✅ 用户输入响应正常</li>
        <li>✅ 保存更改功能正常</li>
        <li>✅ 取消更改功能正常</li>
        <li>✅ 没有 Vue 编译错误</li>
    </ul>
</body>
</html>
