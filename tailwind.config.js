/** @type {import('tailwindcss').Config} */
const PrimeUI = require("tailwindcss-primeui");
const typography = require("@tailwindcss/typography");
const plugin = require("tailwindcss/plugin");
const twColors = require("tailwindcss/colors");

export default {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./app.vue",
    "./node_modules/primevue/**/*.{vue,js,ts,jsx,tsx}",
  ],

  darkMode: "class",

  theme: {
    extend: {
      spacing: {
        18: "4.5rem",
      },
      fontFamily: {
        inter: [
          "Inter",
          "ui-sans-serif",
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "sans-serif",
        ],
        literata: ["Literata", "serif"],
        "fragment-mono": [
          "Fragment Mono",
          "ui-monospace",
          "SFMono-Regular",
          "Menlo",
          "Monaco",
          "Consolas",
          "monospace",
        ],
      },

      fontSize: {
        "2xs": ["10px", { lineHeight: "16px" }],
      },

      typography: {
        DEFAULT: {
          css: {
            h1: {
              fontFamily: "Literata, serif",
              fontSize: "24px",
              fontWeight: "400",
              lineHeight: "32px",
              letterSpacing: "-0.02em",
              marginBottom: "16px",
              marginTop: "44px",
            },
            h2: {
              fontFamily: "Literata, serif",
              fontSize: "20px",
              fontWeight: "400",
              lineHeight: "28px",
              letterSpacing: "0",
              marginBottom: "12px",
              marginTop: "32px",
            },
            h3: {
              fontFamily: "Literata, serif",
              fontSize: "16px",
              fontWeight: "400",
              lineHeight: "24px",
              letterSpacing: "0",
              marginBottom: "8px",
              marginTop: "24px",
            },
            h4: {
              fontFamily:
                "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
              fontSize: "14px",
              fontWeight: "600",
              lineHeight: "22px",
              letterSpacing: "0",
              marginBottom: "4px",
              marginTop: "12px",
            },
            p: {
              fontFamily:
                "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
              fontSize: "14px",
              fontWeight: "400",
              lineHeight: "22px",
              letterSpacing: "0",
              marginBottom: "8px",
              marginTop: "8px",
              wordBreak: "break-word",
              overflowWrap: "anywhere",
            },
            strong: {
              fontWeight: "600",
            },
            em: {
              fontStyle: "italic",
            },
            li: {
              fontFamily:
                "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
              fontSize: "14px",
              fontWeight: "400",
              lineHeight: "22px",
              letterSpacing: "0",
            },
            "li::marker": {
              color: twColors.neutral[700],
            },
            pre: {
              fontFamily:
                "Fragment Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace",
              backgroundColor: twColors.neutral[50],
              border: `1px solid ${twColors.neutral[200]}`,
              color: twColors.neutral[700],
            },
            "pre code": {
              fontFamily:
                "Fragment Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace",
              backgroundColor: "transparent",
              color: twColors.neutral[700],
            },
            hr: {
              marginBottom: "40px",
              marginTop: "40px",
              borderColor: twColors.neutral[200],
            },
            table: {
              width: "100%",
              height: "100%",
              tableLayout: "auto",
              borderCollapse: "collapse",
              borderSpacing: "0",
            },
            th: {
              fontFamily:
                "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
              fontSize: "14px",
              fontWeight: "600",
              lineHeight: "22px",
              letterSpacing: "0",
              paddingTop: "10px",
              paddingBottom: "10px",
              paddingRight: "24px",
              paddingLeft: "8px",
              width: "fit-content",
              overflow: "auto",
              minWidth: "175px",
              position: "relative",
              wordBreak: "break-word",
              overflowWrap: "anywhere",
              borderBottom: `1px solid ${twColors.neutral[200]}`,
            },
            "th:first-child": {
              paddingLeft: "0",
              width: "360px",
            },
            "th:last-child": {
              paddingRight: "0",
            },
            td: {
              fontFamily:
                "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
              fontSize: "14px",
              fontWeight: "400",
              lineHeight: "22px",
              letterSpacing: "0",
              paddingTop: "10px",
              paddingBottom: "10px",
              paddingRight: "24px",
              paddingLeft: "8px",
              wordBreak: "break-word",
              overflowWrap: "anywhere",
            },
            "td:first-child": {
              paddingLeft: "0",
            },
            "td:last-child": {
              paddingRight: "0",
            },
            "tr:last-child td": {
              borderBottom: "none",
            },
            "th[align='right'], td[align='right']": {
              textAlign: "right",
              paddingLeft: "24px",
              paddingRight: "8px",
            },
            "th[align='right']:first-child, td[align='right']:first-child": {
              paddingLeft: "24px",
            },
            "th[align='right']:last-child, td[align='right']:last-child": {
              paddingRight: "0",
            },
            "th[align='center'], td[align='center']": {
              textAlign: "center",
            },
          },
        },
      },

      colors: {
        // Primary Colors
        primary: {
          DEFAULT: twColors.neutral[900],
          emphasis: twColors.neutral[600],
          50: twColors.neutral[50],
          100: twColors.neutral[100],
          200: twColors.neutral[200],
          300: twColors.neutral[300],
          400: twColors.neutral[400],
          500: twColors.neutral[500],
          600: twColors.neutral[600],
          700: twColors.neutral[700],
          800: twColors.neutral[800],
          900: twColors.neutral[900],
          950: twColors.neutral[950],
        },
        // Surface Colors
        surface: {
          0: "#ffffff",
          50: "#fafafa",
          100: "#f5f5f5",
          200: "#e5e5e5",
          300: "#d4d4d4",
          400: "#a3a3a3",
          500: "#737373",
          600: "#525252",
          700: "#404040",
          800: "#262626",
          900: "#171717",
          950: "#0a0a0a",
        },

        highlight: {
          DEFAULT: twColors.neutral[300],
          emphasis: twColors.neutral[400],
        },
        // Text Colors
        text: {
          DEFAULT: "var(--surface-900)",
          secondary: "var(--surface-600)",
          disabled: "var(--surface-400)",
        },
        // Border Colors
        border: {
          DEFAULT: "var(--surface-300)",
          hover: "var(--surface-400)",
          emphasis: "var(--surface-700)",
        },
        // Component Colors
        "primary-text": twColors.neutral[900],
        "secondary-text": twColors.neutral[700],
        "disabled-text": twColors.neutral[500],
        "primary-border": twColors.neutral[300],
        "primary-background": twColors.neutral[100],
        "secondary-background": twColors.neutral[50],
        "tertiary-background": twColors.neutral[200],
        "default-border": twColors.neutral[300],
        "focus-border": twColors.neutral[500],
        "brand-primary": "#DD4227",
        "primary-emphasis": "#dc2626",
        "primary-emphasis-alt": "#b91c1c",
        "primary-hover": "#ef4444",
        "primary-active": "#dc2626",
        "primary-contrast": "#ffffff",
        "surface-main": twColors.neutral[50],
      },

      shadow: {
        sm: "0 4px 4px 4px rgba(229, 229, 229, 0.25)",
        md: "0 4px 6px 4px rgba(229, 229, 229, 0.5)",
        lg: "0 4px 4px 4px rgba(229, 229, 229, 1)",
        popover: "0px 12px 24px 0px rgba(30, 41, 59, 0.12)",
      },
    },
  },

  plugins: [
    PrimeUI,
    typography,
    plugin(({ addUtilities, addVariant }) => {
      // Add enabled variant
      addVariant("enabled", "&:enabled");

      addUtilities({
        ".underline-dotted": {
          "text-decoration-line": "underline",
          "text-decoration-style": "dotted",
          "text-decoration-skip-ink": "none",
          "text-decoration-thickness": "auto",
          "text-underline-offset": "auto",
          "text-underline-position": "from-font",
        },
        ".material-symbols-outlined": {
          "font-family": "Material Symbols Outlined",
          "font-size": "1.25rem",
          "font-weight": "normal",
          "font-style": "normal",
          "line-height": "1",
          "letter-spacing": "normal",
          "text-transform": "none",
          "white-space": "nowrap",
          "word-wrap": "normal",
        },
        ".label-1": {
          "@apply text-2xs font-medium font-literata": {},
        },
        ".label-2": {
          "@apply text-xs font-semibold font-inter": {},
        },
        ".label-3": {
          "@apply text-sm font-semibold font-inter": {},
        },
        ".label-4": {
          "@apply text-base font-semibold font-literata": {},
        },
        ".body-1": {
          "@apply text-sm font-normal font-inter": {},
        },
        ".body-2": {
          "@apply text-base font-normal font-inter": {},
        },
        ".body-2-alt": {
          "@apply text-base font-normal font-literata": {},
        },
        ".body-3": {
          "@apply text-xl font-normal font-inter": {},
        },
        ".caption-1": {
          "@apply text-2xs font-normal font-inter": {},
        },
        ".caption-2": {
          "@apply text-xs font-normal font-inter": {},
        },
        ".header-1": {
          "@apply text-xl font-normal font-literata tracking-[-0.015em]": {},
        },
      });
    }),
  ],
};
