{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "clean-install": "rm -rf .nuxt .output && pnpm install"}, "dependencies": {"@date-fns/utc": "^2.1.0", "@nuxtjs/google-fonts": "3.2.0", "@nuxtjs/tailwindcss": "6.13.1", "@pinia/nuxt": "0.10.1", "@primevue/nuxt-module": "4.2.5", "@primevue/themes": "4.2.5", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "13.1.0", "ajv": "8.17.1", "chart.js": "^4.4.9", "chartjs-chart-venn": "^4.3.5", "cryptojs": "2.5.3", "date-fns": "^4.1.0", "esbuild": "0.25.0", "jsnode": "1.0.0", "json-editor-vue": "0.17.3", "jsonwebtoken": "9.0.2", "koa": "2.15.4", "marked": "15.0.8", "nuxi": "3.22.2", "nuxt": "3.15.4", "nuxt-auth-utils": "0.5.16", "path-to-regexp": "^8.2.0", "pinia": "3.0.1", "primeicons": "7.0.0", "primevue": "4.2.5", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "tailwindcss": "3.4.17", "tailwindcss-primeui": "0.5.1", "unified": "^11.0.5", "vue": "3.5.13", "vue-chartjs": "5.3.2", "vue-router": "4.5.0", "zod": "3.24.3"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "esbuild"], "ignoredBuiltDependencies": ["vue-demi", "json-editor-vue"]}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/cryptojs": "3.1.33", "@types/dompurify": "3.2.0", "@types/jsdom": "21.1.7", "@types/jsonwebtoken": "9.0.9", "postcss-import": "16.1.0"}}