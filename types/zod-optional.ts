import {
  z,
  type ZodTypeAny,
  type ZodObject,
  type ZodRawShape,
  type ZodEffects,
} from "zod";

// Improved type for better type safety
type MakeOptionalShape<T extends ZodRawShape, E extends (keyof T)[] = []> = {
  [K in keyof T]: K extends E[number]
    ? T[K]
    : T[K] extends z.ZodOptional<any> | z.ZodDefault<any>
    ? T[K]
    : z.ZodOptional<T[K]>;
};

/**
 * Given a ZodObject schema or ZodEffects wrapping a ZodObject, returns a new ZodObject where each key
 * not in `exclude` is wrapped in `.optional()`.  If `deep` is true,
 * it will also recurse into nested ZodObjects and arrays of ZodObjects.
 *
 * @param schema  – a ZodObject or ZodEffects<ZodObject> whose keys you want to make (deeply) optional
 * @param exclude – an array of keys to leave "as-is"
 * @param deep    – whether nested objects/arrays should also be partialed
 */
export function makeOptionalSchema<
  T extends ZodRawShape,
  <PERSON> extends (keyof T)[] = []
>(
  schema: ZodObject<T> | ZodEffects<ZodObject<T>>,
  exclude: E = [] as unknown as E,
  deep: boolean = false
): ZodObject<MakeOptionalShape<T, E>> {
  // Unwrap ZodEffects if necessary
  const actualSchema =
    schema instanceof z.ZodEffects
      ? (schema._def.schema as ZodObject<T>)
      : schema;

  // ZodObject stores its shape factory on _def.shape()
  const originalShape = (actualSchema._def.shape as () => T)();
  const newShape: Record<string, ZodTypeAny> = {};

  for (const key in originalShape) {
    const fieldSchema = originalShape[key];

    // if it's in our exclude list, keep the original
    if (exclude.includes(key as keyof T)) {
      newShape[key] = fieldSchema;
      continue;
    }

    // Check if already optional/default - avoid double wrapping
    if (
      fieldSchema instanceof z.ZodOptional ||
      fieldSchema instanceof z.ZodDefault
    ) {
      newShape[key] = fieldSchema;
      continue;
    }

    // Handle deep recursion for complex types
    if (deep) {
      newShape[key] = makeFieldOptional(fieldSchema, deep);
    } else {
      // shallow: only top-level optional
      newShape[key] = fieldSchema.optional();
    }
  }

  return z.object(newShape) as ZodObject<MakeOptionalShape<T, E>>;
}

/**
 * Helper function to handle different Zod types when making them optional
 */
function makeFieldOptional(fieldSchema: ZodTypeAny, deep: boolean): ZodTypeAny {
  // Handle ZodEffects wrapping an object
  if (fieldSchema instanceof z.ZodEffects) {
    const innerSchema = fieldSchema._def.schema;
    if (innerSchema instanceof z.ZodObject) {
      const optionalInner = makeOptionalSchema(innerSchema, [], deep);
      // Since we can't directly modify ZodEffects, create a new optional schema
      return optionalInner.optional();
    }
    return fieldSchema.optional();
  }

  // Handle nested objects
  if (fieldSchema instanceof z.ZodObject) {
    return makeOptionalSchema(fieldSchema, [], deep).optional();
  }

  // Handle arrays
  if (fieldSchema instanceof z.ZodArray) {
    const item = fieldSchema._def.type;
    if (item instanceof z.ZodObject) {
      // array of objects → array of partial objects
      return z.array(makeOptionalSchema(item, [], deep)).optional();
    } else {
      // other arrays → optional array
      return fieldSchema.optional();
    }
  }

  // Handle unions (make each branch optional recursively)
  if (fieldSchema instanceof z.ZodUnion) {
    const options = fieldSchema._def.options.map((option: ZodTypeAny) =>
      option instanceof z.ZodObject
        ? makeOptionalSchema(option, [], deep)
        : option
    );
    // Ensure we have at least 2 options for union
    if (options.length >= 2) {
      return z.union([options[0], options[1], ...options.slice(2)]).optional();
    } else if (options.length === 1) {
      return options[0].optional();
    }
    return fieldSchema.optional();
  }

  // Handle intersections
  if (fieldSchema instanceof z.ZodIntersection) {
    const left = fieldSchema._def.left;
    const right = fieldSchema._def.right;
    const newLeft =
      left instanceof z.ZodObject ? makeOptionalSchema(left, [], deep) : left;
    const newRight =
      right instanceof z.ZodObject
        ? makeOptionalSchema(right, [], deep)
        : right;
    return z.intersection(newLeft, newRight).optional();
  }

  // Handle records
  if (fieldSchema instanceof z.ZodRecord) {
    const valueType = fieldSchema._def.valueType;
    const keyType = fieldSchema._def.keyType;
    const newValueType =
      valueType instanceof z.ZodObject
        ? makeOptionalSchema(valueType, [], deep)
        : valueType;

    return keyType
      ? z.record(keyType, newValueType).optional()
      : z.record(newValueType).optional();
  }

  // For all other types, just make them optional
  return fieldSchema.optional();
}
