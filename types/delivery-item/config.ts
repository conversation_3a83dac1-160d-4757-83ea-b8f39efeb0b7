import type { ActionType } from "~/types/actions/action-suggestion";
import type {
  DeliveryItemCategory,
  DeliveryItemType,
  DeliveryItem,
} from "~/types/delivery-item/delivery-item";

interface DeliveryItemCategoryConfig {
  label: string;
  icon: string;
  backgroundColorClass: string;
  iconColorClass: string;
}

interface ActionSuggestionConfig {
  label: string;
  icon: string;
  actionType: ActionType;
  onClick?: (item: DeliveryItem) => Promise<void>;
}

interface DeliveryItemTypeConfig {
  category: DeliveryItemCategory;
  actionSuggestions: ActionSuggestionConfig[];
  allowExpand?: boolean;
  allowReschedule?: boolean;
  acceptedTextCreator?: (actionBy: string, actionAt: Date) => string;
}

export interface DeliveryConfig {
  categories: Record<DeliveryItemCategory, DeliveryItemCategoryConfig>;
  types: Record<DeliveryItemType, DeliveryItemTypeConfig>;
}
