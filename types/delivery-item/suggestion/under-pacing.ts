import { z } from "zod";
import { SuggestionTypeSchema } from "./suggestion-type";
import { SelectableSchema } from "./selectable";

import { GuidanceSchema } from "~/types/amazon/guidance";

const AdGroupUnderPacingSchema = z.object({
  orderId: z.string().optional(),
  adGroupId: z.string(),
  orderName: z.string().optional(),
  adGroupName: z.string().optional(),
  currencyCode: z.string(),
  deliveryRate: z.number().optional(),
  currentMinimumSpend: z.number(),
  recommendedMinimumSpend: z.number(),
});

const TransformedGuidanceSchema = GuidanceSchema.transform((g) => ({
  ...g,
  recommendations: g.recommendations?.map((r) => ({
    ...r,
    quickactionsData: {
      ...r.quickactionsData,
      currentActions: r.quickactionsData?.currentActions?.map((a) => ({
        ...a,
        selected: true,
      })),
    },
  })),
}));

export const UnderPacingSuggestionSchema = z.object({
  actionType: z.literal(SuggestionTypeSchema.Enum.UNDER_PACING),
  payload: z.object({
    advertiserId: z.string(),
    guidance: z.array(TransformedGuidanceSchema).nullish(),
    adGroupsMinimumSpend: z
      .array(SelectableSchema.merge(AdGroupUnderPacingSchema))
      .nullish(),
  }),
});

export type UnderPacingSuggestion = z.infer<typeof UnderPacingSuggestionSchema>;
