import { z } from "zod";

import { BidChangeSuggestionSchema } from "./bid-change";
import { ExtendCampaignSuggestionSchema } from "./extend-campaign";
import { AcknowledgeSuggestionSchema } from "./acknowledge";
import { AudienceSwapSuggestionSchema } from "./audience-swap";
import { UnderPacingSuggestionSchema } from "./under-pacing";
import { ECPMSpikeSuggestionSchema } from "./ecpm-spike";
import { BidModifierSuggestionSchema } from "./bid-modifier";

export const SuggestionSchema = z.discriminatedUnion("actionType", [
  BidChangeSuggestionSchema,
  ExtendCampaignSuggestionSchema,
  AcknowledgeSuggestionSchema,
  AudienceSwapSuggestionSchema,
  UnderPacingSuggestionSchema,
  BidModifierSuggestionSchema,
  ECPMSpikeSuggestionSchema,
]);

export type Suggestion = z.infer<typeof SuggestionSchema>;
