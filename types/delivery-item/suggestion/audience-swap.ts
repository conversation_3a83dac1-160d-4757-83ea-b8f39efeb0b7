import { z } from "zod";

import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";
import { SelectableSchema } from "~/types/delivery-item/suggestion/selectable";

export const AudienceSwapDataSchema = z.object({
  campaignId: z.string(),
  adGroupId: z.string(),
  adGroupName: z.string(),
  groupId: z.string(),
  fromTargetId: z.string(),
  fromScore: z.number(),
  fromAudienceId: z.string(),
  fromAudienceName: z.string(),
  toScore: z.number(),
  toAudienceId: z.string(),
  toAudienceName: z.string(),
});

export const AudienceSwapSuggestionItemSchema = z.object({
  campaignName: z.string(),
  changes: z.array(SelectableSchema.merge(AudienceSwapDataSchema)),
});

export const AudienceSwapSuggestionSchema = z.object({
  actionType: z.literal(SuggestionTypeSchema.Enum.MUTATE_TARGETS),
  payload: z.object({
    advertiserId: z.string(),
    changes: z.array(AudienceSwapSuggestionItemSchema),
  }),
});

export type AudienceSwapSuggestion = z.infer<
  typeof AudienceSwapSuggestionSchema
>;
