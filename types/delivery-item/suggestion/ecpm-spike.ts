import { z } from "zod";
import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";

export const ECPMSpikeRecommendationPayloadSchema = z.object({
    inventoryType: z.string().optional(),
    fees: z.any().nullable().optional(),
    comments: z.string().nullable().optional(),
    creativeRotationType: z.string().nullable().optional(),
    endDateTime: z.string().nullable().optional(),
    advertisedProductCategoryIds: z.array(z.string()).nullable().optional(),
    adGroupId: z.string(),
    budgetCaps: z.any().nullable().optional(),
    startDateTime: z.string().nullable().optional(),
    pacing: z.any().nullable().optional(),
    targetingSettings: z.object({
        videoCompletionTier: z.string().nullable().optional(),
        amazonViewability: z.object({
            viewabilityTier: z.string().optional(),
            includeUnmeasurableImpressions: z.boolean().optional()
        }).nullable().optional(),
        tacticsConvertersExclusionType: z.string().nullable().optional(),
        timeZoneType: z.string().nullable().optional(),
        userLocation: z.any().nullable().optional(),
        defaultAudienceTargetingMatchType: z.string().nullable().optional(),
        userLocationSignal: z.any().nullable().optional(),
        targetedPGDealId: z.string().nullable().optional(),
        enableLanguageTargeting: z.boolean().nullable().optional()
    }).nullable().optional(),
    budgetAmount: z.number().nullable().optional(),
    optimization: z.object({
        bidStrategy: z.string().optional(),
        dailyMinSpendAmount: z.number().nullable().optional(),
        automateBudgetAllocation: z.boolean().optional()
    }).nullable().optional(),
    purchaseOrderNumber: z.string().nullable().optional(),
    name: z.string().nullable().optional(),
    state: z.string().nullable().optional(),
    bid: z.any().nullable().optional(),
    digitalOutOfHomeProductLocations: z.any().nullable().optional(),
    frequencies: z.any().nullable().optional()
});

export const ECPMSpikeRecommendationItemSchema = z.object({
    change: z.string(),
    payload: ECPMSpikeRecommendationPayloadSchema
});

export const ECPMSpikeRecommendationSchema = z.object({
    recommendation_str: z.string(),
    description: z.string().optional(),
    update_payload: z.array(ECPMSpikeRecommendationItemSchema).nullable().optional()
});

export const ECPMSpikeLineItemSchema = z.object({
    spike_date: z.string(),
    prev_date: z.string(),
    line_item_id: z.string(),
    line_item_name: z.string(),
    prev_ecpm: z.number(),
    current_ecpm: z.number(),
    increase_pct: z.number(),
    reason: z.string(),
    recommendation: ECPMSpikeRecommendationSchema
});

export const ECPMSpikeOrderPayloadSchema = z.object({
    campaign_id: z.string(),
    order_name: z.string(),
    splike_lineItems: z.array(ECPMSpikeLineItemSchema)
});

export const ECPMSpikeSuggestionSchema = z.object({
    actionType: z.literal(SuggestionTypeSchema.Enum.ECPM_SPIKE_MONITOR),
    payload: ECPMSpikeOrderPayloadSchema,
});

export type ECPMSpikeSuggestion = z.infer<typeof ECPMSpikeSuggestionSchema>;
export type ECPMSpikeOrderPayload = z.infer<typeof ECPMSpikeOrderPayloadSchema>;
export type ECPMSpikeLineItem = z.infer<typeof ECPMSpikeLineItemSchema>;
export type ECPMSpikeRecommendation = z.infer<typeof ECPMSpikeRecommendationSchema>;
export type ECPMSpikeRecommendationItem = z.infer<typeof ECPMSpikeRecommendationItemSchema>; 