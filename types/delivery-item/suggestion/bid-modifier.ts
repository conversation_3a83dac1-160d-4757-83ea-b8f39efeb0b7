import { z } from "zod";
import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";
import { SelectableSchema } from "~/types/delivery-item/suggestion/selectable";

export const BidModifierTermSchema = SelectableSchema.extend({
    deviceType: z.array(z.string()).nullish(),
    country: z.array(z.string()).nullish(),
    slotSize: z.array(z.string()).nullish(),
    city: z.array(z.string()).nullish(),
    appName: z.array(z.string()).nullish(),
    postalCode: z.array(z.string()).nullish(),
    deviceMake: z.array(z.string()).nullish(),
    operatingSystem: z.array(z.string()).nullish(),
    bidAdjustment: z.number(),
    termId: z.string().nullish(),
    negative: z.boolean().nullish(),
    appId: z.array(z.string()).nullish(),
    browser: z.array(z.string()).nullish(),
    domain: z.array(z.string()).nullish(),
    slotPosition: z.array(z.string()).nullish(),
    dma: z.array(z.string()).nullish(),
    adFormat: z.array(z.string()).nullish(),
    region: z.array(z.string()).nullish(),
    behavioralSegment: z.array(z.string()).nullish(),
});

export const BidModifierContentSchema = z.object({
    ruleExpression: z.object({
        terms: z.array(BidModifierTermSchema),
        onMultipleMatches: z.string().nullish(),
    }),
    ruleDescription: z.string(),
});

export const BidModifierLineItemSchema = z.object({
    line_item_id: z.string(),
    bid_modifier_content: BidModifierContentSchema,
});

export const BidModifierReasoningTermSchema = z.object({
    term: z.object({
        deviceType: z.array(z.string()).nullish(),
        slotSize: z.array(z.string()).nullish(),
        region: z.array(z.string()).nullish(),
        operatingSystem: z.array(z.string()).nullish(),
        domain: z.array(z.string()).nullish(),
        segmentIds: z.array(z.string()).nullish(),
        segmentNames: z.array(z.string()).nullish(),
        bidAdjustment: z.number(),
    }),
    reason: z.string(),
});

export const BidModifierReasoningSchema = z.object({
    line_item_id: z.string(),
    line_item_name: z.string().optional(),
    term_reasoning: z.array(BidModifierReasoningTermSchema),
});

export const BidModifierSuggestionSchema = z.object({
    actionType: z.literal(SuggestionTypeSchema.Enum.BID_MODIFIER),
    payload: z.object({
        order_id: z.string(),
        order_name: z.string().default("Untitled Order"),
        advertiser_id: z.string(),
        description: z.string().optional(),
        line_items: z.array(BidModifierLineItemSchema),
        reason: z.array(BidModifierReasoningSchema).optional(),
    }),
});

export type BidModifierSuggestion = z.infer<typeof BidModifierSuggestionSchema>;
export type BidModifierTerm = z.infer<typeof BidModifierTermSchema>;
export type BidModifierLineItem = z.infer<typeof BidModifierLineItemSchema>;
export type BidModifierReasoning = z.infer<typeof BidModifierReasoningSchema>; 