import { z } from "zod";
import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";
import { FlightSchema } from "~/types/amazon/flight";

export const ExtendCampaignSuggestionSchema = z.object({
  actionType: z.literal(SuggestionTypeSchema.Enum.FLIGHT_MODIFY),
  payload: z.object({
    advertiserId: z.string(),
    changes: z.array(
      z.object({
        campaignId: z.string(),
        campaignName: z.string(),
        flights: z.array(FlightSchema),
      })
    ),
  }),
});

export type ExtendCampaignSuggestion = z.infer<
  typeof ExtendCampaignSuggestionSchema
>;
