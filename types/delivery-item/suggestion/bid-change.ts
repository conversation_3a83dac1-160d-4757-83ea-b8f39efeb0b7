import { z } from "zod";

import { SuggestionTypeSchema } from "~/types/delivery-item/suggestion/suggestion-type";
import { AdGroupSchema } from "~/types/amazon/ad-group";
import { SelectableSchema } from "~/types/delivery-item/suggestion/selectable";

export const BidChangeSuggestionSchema = z.object({
  actionType: z.literal(SuggestionTypeSchema.Enum.PATCH_AD_GROUP),
  payload: z.object({
    advertiserId: z.string(),
    changes: z.array(
      z.object({
        campaignName: z.string(),
        adGroups: z.array(
          SelectableSchema.extend({
            current: AdGroupSchema,
            next: AdGroupSchema,
            cpm: z.number()
          })
        ),
      })
    ),
  }),
});

export type BidChangeSuggestion = z.infer<typeof BidChangeSuggestionSchema>;
