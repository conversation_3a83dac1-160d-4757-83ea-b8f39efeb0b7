import { z } from "zod";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import { AdGroupSchema } from "~/types/amazon/ad-group";

export const PatchAdGroupActionSchema = z.object({
  actionType: z.literal(ActionTypeSchema.Enum.PATCH_AD_GROUP),
  payload: z.object({
    advertiserId: z.string(),
    adGroups: z.array(AdGroupSchema),
  }),
});

export type PatchAdGroupAction = z.infer<typeof PatchAdGroupActionSchema>;
