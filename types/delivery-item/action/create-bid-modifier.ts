import { z } from "zod";
import { ActionTypeSchema } from "./action-type";

export const BidModifierRuleRequestContentSchema = z.object({
    ruleExpression: z.object({
        terms: z.array(z.object({
            deviceType: z.array(z.string()).optional(),
            country: z.array(z.string()).optional(),
            slotSize: z.array(z.string()).optional(),
            city: z.array(z.string()).optional(),
            appName: z.array(z.string()).optional(),
            postalCode: z.array(z.string()).optional(),
            deviceMake: z.array(z.string()).optional(),
            operatingSystem: z.array(z.string()).optional(),
            bidAdjustment: z.number(),
            termId: z.string().optional(),
            negative: z.boolean().optional(),
            appId: z.array(z.string()).optional(),
            browser: z.array(z.string()).optional(),
            domain: z.array(z.string()).optional(),
            slotPosition: z.array(z.string()).optional(),
            dma: z.array(z.string()).optional(),
            adFormat: z.array(z.string()).optional(),
            region: z.array(z.string()).optional(),
            behavioralSegment: z.array(z.string()).optional(),
        })),
        onMultipleMatches: z.string().optional(),
    }),
    ruleDescription: z.string(),
});

export const ModifierRuleSchema = z.object({
    adGroupId: z.string(),
    ruleRequestContent: BidModifierRuleRequestContentSchema,
    reasoning: z.string().optional(),
});

export const CreateBidModifierActionSchema = z.object({
    actionType: z.literal(ActionTypeSchema.Enum.BID_MODIFIER),
    payload: z.object({
        type: z.literal("AMAZON_BID_MODIFIER"),
        advertiserId: z.string(),
        campaignId: z.string(),
        modifierRules: z.array(ModifierRuleSchema),
    }),
});

export type CreateBidModifierAction = z.infer<typeof CreateBidModifierActionSchema>;
export type ModifierRule = z.infer<typeof ModifierRuleSchema>;
export type BidModifierRuleRequestContent = z.infer<typeof BidModifierRuleRequestContentSchema>; 