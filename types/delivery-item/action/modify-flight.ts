import { z } from "zod";

import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import { FlightSchema } from "~/types/amazon/flight";

export const NewFlightSchema = FlightSchema.omit({
  flightId: true,
  currencyCode: true,
}).required();

export const ModifyFlightActionSchema = z.object({
  actionType: z.literal(ActionTypeSchema.Enum.FLIGHT_MODIFY),
  payload: z.object({
    advertiserId: z.string(),
    campaignId: z.string(),
    newFlights: z.array(NewFlightSchema),
  }),
});

export type ModifyFlightAction = z.infer<typeof ModifyFlightActionSchema>;
