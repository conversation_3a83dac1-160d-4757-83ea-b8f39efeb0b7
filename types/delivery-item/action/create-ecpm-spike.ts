import { z } from "zod";
import { ActionTypeSchema } from "./action-type";

export const ECPMSpikeActionPayloadSchema = z.object({
    adGroups: z.array(z.any())
});

export const CreateECPMSpikeActionSchema = z.object({
    actionType: z.literal(ActionTypeSchema.Enum.PATCH_AD_GROUP),
    payload: ECPMSpikeActionPayloadSchema,
});

export type CreateECPMSpikeMonitorAction = z.infer<typeof CreateECPMSpikeActionSchema>;