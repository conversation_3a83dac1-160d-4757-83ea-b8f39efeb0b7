import { z } from "zod";

import { PatchAdGroupActionSchema } from "./patch-ad-group";
import { MutateAdGroupTargetsActionSchema } from "./mutate-ad-group-targets";
import { AcknowledgeActionSchema } from "./acknowledge";
import { ModifyFlightActionSchema } from "./modify-flight";
import { CreateECPMSpikeActionSchema } from "./create-ecpm-spike";
import { CreateBidModifierActionSchema } from "./create-bid-modifier";

export const ActionSchema = z.discriminatedUnion("actionType", [
  PatchAdGroupActionSchema,
  MutateAdGroupTargetsActionSchema,
  AcknowledgeActionSchema,
  ModifyFlightActionSchema,
  CreateECPMSpikeActionSchema,
  CreateBidModifierActionSchema,
]);

export type Action = z.infer<typeof ActionSchema>;
