import { z } from "zod";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import { TargetSchema } from "~/types/amazon/targets";

export const MutateAdGroupTargetsActionSchema = z.object({
  actionType: z.literal(ActionTypeSchema.Enum.MUTATE_TARGETS),
  payload: z.object({
    advertiserId: z.string(),
    createTargets: z.array(TargetSchema).optional(),
    deleteTargetIds: z.array(z.string()).optional(),
  }),
});

export type MutateAdGroupTargetsAction = z.infer<
  typeof MutateAdGroupTargetsActionSchema
>;
