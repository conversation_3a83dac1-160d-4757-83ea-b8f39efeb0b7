import { z } from "zod";

import { OwnableSchema } from "~/types/permissions";
import { SuggestionSchema } from "~/types/delivery-item/suggestion";

export const DeliveryItemTypeSchema = z.enum([
  "BID_CHANGE",
  "EXTEND_CAMPAIGN",
  "BID_MODIFIER",
  "ITEM_FAILED",
  "AUDIENCE_SWAP",
  "UNDER_PACING",
  "GENERIC_UPDATE",
  "GENERIC_ALERT",
  "ECPM_SPIKE_MONITOR",
  "MARKDOWN_CONTENT"
]);
export type DeliveryItemType = z.infer<typeof DeliveryItemTypeSchema>;

export const DeliveryItemCategorySchema = z.enum([
  "ALERT",
  "OPPORTUNITY",
  "UPDATE",
  "BRIEFING",
]);
export type DeliveryItemCategory = z.infer<typeof DeliveryItemCategorySchema>;

export const DeliveryItemUrgencySchema = z.enum(["LOW", "MEDIUM", "HIGH"]);
export type DeliveryItemUrgency = z.infer<typeof DeliveryItemUrgencySchema>;

export const DeliveryItemSchema = OwnableSchema.extend({
  id: z.string(),
  type: DeliveryItemTypeSchema,
  category: DeliveryItemCategorySchema.nullish(),
  /**
   * @deprecated no longer used
   */
  urgency: DeliveryItemUrgencySchema.nullish(),
  summary: z.string(),
  fullMessage: z.string(),
  actionSuggestions: z.array(SuggestionSchema).nullish(),
  hasExecutionError: z.boolean().optional().default(false),
  actionBy: z.string().nullish(),
  actionAt: z.coerce.date().nullish(),
  isRelevant: z.boolean(),
  seenAt: z.coerce.date().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
}).superRefine((data, ctx) => {
  setAcknowledgeDefaultForGenericTypes(data, ctx);
});
export type DeliveryItem = z.infer<typeof DeliveryItemSchema>;

function setAcknowledgeDefaultForGenericTypes(
  data: DeliveryItem,
  ctx: z.RefinementCtx
) {
  const typesForAcknowledgeDefault: DeliveryItemType[] = [
    "GENERIC_UPDATE",
    "GENERIC_ALERT",
  ];

  if (
    typesForAcknowledgeDefault.includes(data.type) &&
    (!data.actionSuggestions || data.actionSuggestions.length === 0)
  ) {
    data.actionSuggestions = [{ actionType: "ACKNOWLEDGE" }];
  }
}

export const DeliveryItemUpdateSchema = z.object({
  id: z.string(),
  hasExecutionError: z.boolean().optional(),
  actionBy: z.string().optional(),
  actionAt: z.coerce.date().optional(),
  isRelevant: z.boolean().optional(),
  seenAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date(),
});
export type DeliveryItemUpdate = z.infer<typeof DeliveryItemUpdateSchema>;
