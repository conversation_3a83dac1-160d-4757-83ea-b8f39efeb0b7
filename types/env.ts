// extend the process.env object with the env variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NUXT_SELF_ACCESS_TOKEN: string;
      NUXT_LINDA_API_URL: string;
      NUXT_LINDA_API_KEY: string;
      NUXT_ROB_API_URL: string;
      NUXT_ROB_API_KEY: string;
      NUXT_MMS_API_URL: string;
      NUXT_MMS_API_KEY: string;
      NUXT_METRICS_API_URL: string;
      NUXT_METRICS_API_KEY: string;
      NUXT_SESSION_PASSWORD: string;
      NUXT_PUBLIC_GOOGLE_CLIENT_ID: string;
      NUXT_AES_KEY: string;
      NUXT_AES_IV: string;
      NUXT_QUEUE_WORKER_COUNT?: string;
    }
  }
}

// extend runtime config with the env variables
declare module "nuxt/schema" {
  interface RuntimeConfig {
    selfAccessToken: string;
    lindaApiUrl: string;
    lindaApiKey: string;
    robApiUrl: string;
    robApiKey: string;
    mmsApiUrl: string;
    mmsApiKey: string;
    metricsApiUrl: string;
    metricsApiKey: string;
    aesKey: string;
    aesIv: string;
    queueWorkerCount: number;
  }
  interface PublicRuntimeConfig {
    googleClientId: string;
  }
}

export {};
