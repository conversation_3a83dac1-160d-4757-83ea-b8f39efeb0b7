export class ApiError extends Error {
  public readonly status: number;

  constructor(message: string, status: number = 500) {
    super(message);
    this.name = "ApiError";
    this.status = status;
  }

  public fromError(error: Error, status: number = 500): ApiError {
    return new ApiError(error.message, status);
  }

  public toResponse(): Response {
    const body = {
      error: this.message,
    };

    return new Response(JSON.stringify(body), {
      status: this.status,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}
