import { z } from "zod";

export const FlightSchema = z.object({
  startDateTime: z.coerce.date().nullish(),
  budgetAmount: z.number().nullish(),
  flightId: z.string().nullish(),
  endDateTime: z.coerce.date().nullish(),
  currencyCode: z.string().nullish(),
});
export type Flight = z.infer<typeof FlightSchema>;

export const DspListCampaignFlightsResponseContentSchema = z.object({
  nextToken: z.string().nullish(),
  flights: z.array(FlightSchema),
});
export type DspListCampaignFlightsResponseContent = z.infer<
  typeof DspListCampaignFlightsResponseContentSchema
>;
