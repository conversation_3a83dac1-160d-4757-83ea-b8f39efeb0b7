// Simple TypeScript types for Amazon Asset API responses

export interface AssetIdentifier {
  assetId?: string;
  version?: string;
}

export interface ProcessedFile {
  profile?: string;
  programs?: string[];
  url?: string;
}

export interface AssetFiles {
  processedFiles?: ProcessedFile[];
  defaultUrl?: string;
}

export interface FileMetadata {
  extension?: string;
  resolutionHeight?: number;
  width?: number;
  aspectRatio?: string;
  resolutionWidth?: number;
  contentType?: string;
  height?: number;
}

export interface StorageLocationUrls {
  processedUrls?: Record<string, string>;
  defaultUrl?: string;
}

export interface AssetVersion {
  lastUpdatedBy?: string;
  creationTime?: number;
  assetIdentifier?: AssetIdentifier;
  assetFiles?: AssetFiles;
  otherMetadata?: Record<string, any>;
  url?: string;
  assetSubTypes?: string[];
  fileMetadata?: FileMetadata;
  createdBy?: string;
  name?: string;
  lastUpdatedTime?: number;
  assetStatus?: string;
  failedSpecChecks?: any[];
  storageLocationUrls?: StorageLocationUrls;
  moderationContentList?: any[];
}

export interface AssetGlobal {
  assetId?: string;
  assetType?: "IMAGE" | "VIDEO";
  accountIds?: string[];
  marketplaceId?: string[];
}

export interface Asset {
  assetVersionList: AssetVersion[];
  assetGlobal: AssetGlobal;
}
