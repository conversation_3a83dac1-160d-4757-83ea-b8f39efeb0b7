import { z } from "zod";

// --- Enums ---

export const AdProductSchema = z
  .enum(["AMAZON_DSP"])
  .describe("The advertising product associated to this target.");
export type AdProduct = z.infer<typeof AdProductSchema>;

export const StateTypeSchema = z
  .enum(["ENABLED"])
  .describe("The current resource state");
export type StateType = z.infer<typeof StateTypeSchema>;

export const InGroupOperatorSchema = z
  .enum(["ANY", "ALL"])
  .describe(
    "The interoperator used among audiences within the same audience group. This is a read-only field."
  );
export type InGroupOperator = z.infer<typeof InGroupOperatorSchema>;

export const AcrossGroupOperatorSchema = z
  .enum(["ANY", "ALL"])
  .describe(
    "The intraoperator used among audiences between audience groups. This is a read-only field."
  );
export type AcrossGroupOperator = z.infer<typeof AcrossGroupOperatorSchema>;

export const AudienceTargetTypeSchema = z.enum(["AUDIENCE"]);
export type AudienceTargetType = z.infer<typeof AudienceTargetTypeSchema>;

export const LocationTargetTypeSchema = z.enum(["LOCATION"]);
export type LocationTargetType = z.infer<typeof LocationTargetTypeSchema>;

export const DomainListTargetTypeSchema = z.enum(["DOMAIN_LIST"]);
export type DomainListTargetType = z.infer<typeof DomainListTargetTypeSchema>;

export const DomainNameTargetTypeSchema = z.enum(["DOMAIN_NAME"]);
export type DomainNameTargetType = z.infer<typeof DomainNameTargetTypeSchema>;

export const AdvertiserDomainListTargetTypeSchema = z.enum([
  "ADVERTISER_DOMAIN_LIST",
]);
export type AdvertiserDomainListTargetType = z.infer<
  typeof AdvertiserDomainListTargetTypeSchema
>;

export const DomainFileTargetTypeSchema = z.enum(["DOMAIN_FILE"]);
export type DomainFileTargetType = z.infer<typeof DomainFileTargetTypeSchema>;

export const DomainTargetTypeSchema = z.enum(["DOMAIN"]);
export type DomainTargetType = z.infer<typeof DomainTargetTypeSchema>;

export const AppTypeSchema = z
  .enum(["MOBILE", "STV", "OTHER"])
  .describe("The type of app being targeted.");
export type AppType = z.infer<typeof AppTypeSchema>;

export const AppTargetTypeSchema = z.enum(["APP"]);
export type AppTargetType = z.infer<typeof AppTargetTypeSchema>;

export const DeviceTypeSchema = z
  .enum(["DESKTOP", "MOBILE", "CONNECTED_TV", "CONNECTED_DEVICE", "OTHER"])
  .describe("Required for all inventory types.");
export type DeviceType = z.infer<typeof DeviceTypeSchema>;

export const MobileEnvironmentTypeSchema = z
  .enum(["WEB", "APP", "OTHER"])
  .describe("The mobile environment targeting type.");
export type MobileEnvironmentType = z.infer<typeof MobileEnvironmentTypeSchema>;

export const MobileDeviceTypeSchema = z
  .enum(["IPHONE", "IPAD", "ANDROID", "KINDLE_FIRE", "KINDLE_FIRE_HD", "OTHER"])
  .describe("The mobile device targeting type.");
export type MobileDeviceType = z.infer<typeof MobileDeviceTypeSchema>;

export const DeviceTargetTypeSchema = z.enum(["DEVICE"]);
export type DeviceTargetType = z.infer<typeof DeviceTargetTypeSchema>;

export const DeviceOrientationTypeSchema = z
  .enum(["PORTRAIT", "LANDSCAPE", "OTHER"])
  .describe("The mobile device orientation type.");
export type DeviceOrientationType = z.infer<typeof DeviceOrientationTypeSchema>;

export const MobileOSTypeSchema = z
  .enum(["IOS", "ANDROID", "OTHER"])
  .describe("The mobile operating system targeting type.");
export type MobileOSType = z.infer<typeof MobileOSTypeSchema>;

export const DayOfWeekSchema = z
  .enum([
    "MONDAY",
    "TUESDAY",
    "WEDNESDAY",
    "THURSDAY",
    "FRIDAY",
    "SATURDAY",
    "SUNDAY",
  ])
  .describe("Day of the week targeted.");
export type DayOfWeek = z.infer<typeof DayOfWeekSchema>;

export const DayPartTargetTypeSchema = z.enum(["DAY_PART"]);
export type DayPartTargetType = z.infer<typeof DayPartTargetTypeSchema>;

export const InventorySourceTypeSchema = z
  .enum([
    "AMAZON",
    "APD",
    "THIRD_PARTY_EXCHANGE",
    "DEAL",
    "INVENTORY_GROUP",
    "OTHER",
  ])
  .describe("The type of inventory source to be targeted.");
export type InventorySourceType = z.infer<typeof InventorySourceTypeSchema>;

export const InventorySourceTargetTypeSchema = z.enum(["INVENTORY_SOURCE"]);
export type InventorySourceTargetType = z.infer<
  typeof InventorySourceTargetTypeSchema
>;

export const ThirdPartyTargetTypeSchema = z.enum(["THIRD_PARTY"]);
export type ThirdPartyTargetType = z.infer<typeof ThirdPartyTargetTypeSchema>;

export const DoubleVerifyFraudInvalidTrafficTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_FRAUD_INVALID_TRAFFIC",
]);
export type DoubleVerifyFraudInvalidTrafficTargetType = z.infer<
  typeof DoubleVerifyFraudInvalidTrafficTargetTypeSchema
>;

export const ExcludeAppsAndSitesTypeSchema = z.enum([
  "ALLOW_ALL",
  "FRAUD_TRAFFIC_LEVEL_GTE_100",
  "FRAUD_TRAFFIC_LEVEL_GTE_50",
  "FRAUD_TRAFFIC_LEVEL_GTE_25",
  "FRAUD_TRAFFIC_LEVEL_GTE_10",
  "FRAUD_TRAFFIC_LEVEL_GTE_08",
  "FRAUD_TRAFFIC_LEVEL_GTE_06",
  "FRAUD_TRAFFIC_LEVEL_GTE_04",
  "FRAUD_TRAFFIC_LEVEL_GTE_02",
]);
export type ExcludeAppsAndSitesType = z.infer<
  typeof ExcludeAppsAndSitesTypeSchema
>;

export const IASFraudInvalidTrafficTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_FRAUD_INVALID_TRAFFIC",
]);
export type IASFraudInvalidTrafficTargetType = z.infer<
  typeof IASFraudInvalidTrafficTargetTypeSchema
>;

export const IASFraudInvalidTrafficTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "FRAUD_INVALID_TRAFFIC_EXCLUDE_HIGH_RISK",
    "FRAUD_INVALID_TRAFFIC_EXCLUDE_HIGH_MODERATE_RISK",
  ])
  .describe("The type of fraud invalid traffic.");
export type IASFraudInvalidTrafficType = z.infer<
  typeof IASFraudInvalidTrafficTypeSchema
>;

export const PixalateFraudInvalidTrafficTargetTypeSchema = z.enum([
  "PIXALATE_FRAUD_INVALID_TRAFFIC",
]);
export type PixalateFraudInvalidTrafficTargetType = z.infer<
  typeof PixalateFraudInvalidTrafficTargetTypeSchema
>;

export const DVBrandSafetyContentCategoryTypeSchema = z.enum([
  "AD_SERVER",
  "CELEBRITY_GOSSIP",
  "CULTS_SURVIVALISM",
  "GAMBLING",
  "INCENTIVIZED_MALWARE_CLUTTER",
  "INFLAMMATORY_POLITICS_NEWS",
  "NEGATIVE_NEWS_FINANCIAL",
  "NEGATIVE_NEWS_PHARMACEUTICAL",
  "NON_STANDARD_CONTENT_NON_ENGLISH",
  "NON_STANDARD_CONTENT_PARKING_PAGE",
  "OCCULT",
  "PIRACY_COPYRIGHT_INFRINGEMENT",
  "UNMODERATED_UGC_FORUMS_IMAGES_VIDEO",
  "EXTREME_GRAPHIC",
]);
export type DVBrandSafetyContentCategoryType = z.infer<
  typeof DVBrandSafetyContentCategoryTypeSchema
>;

export const BrandSuitabilityRiskLevelTypeSchema = z
  .enum(["ALLOW_ALL", "HIGH", "HIGH_MEDIUM", "HIGH_MEDIUM_LOW"])
  .describe("The Double Verify brand suitability risk level.");
export type BrandSuitabilityRiskLevelType = z.infer<
  typeof BrandSuitabilityRiskLevelTypeSchema
>;

export const DVStandardDisplayBrandSafetyTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_STANDARD_DISPLAY_BRAND_SAFETY",
]);
export type DVStandardDisplayBrandSafetyTargetType = z.infer<
  typeof DVStandardDisplayBrandSafetyTargetTypeSchema
>;

export const DVBrandSafetyAppStarRatingTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "APP_STAR_RATING_LT_1_POINT_5_STARS",
    "APP_STAR_RATING_LT_2_STARS",
    "APP_STAR_RATING_LT_2_POINT_5_STARS",
    "APP_STAR_RATING_LT_3_STARS",
    "APP_STAR_RATING_LT_3_POINT_5_STARS",
    "APP_STAR_RATING_LT_4_STARS",
    "APP_STAR_RATING_LT_4_POINT_5_STARS",
  ])
  .describe("App star rating to be used for excluding apps.");
export type DVBrandSafetyAppStarRatingType = z.infer<
  typeof DVBrandSafetyAppStarRatingTypeSchema
>;

export const DVBrandSafetyAppAgeRatingTypeSchema = z.enum([
  "EVERYONE_4_PLUS",
  "TWEENS_9_PLUS",
  "TEENS_12_PLUS",
  "MATURE_17_PLUS",
  "ADULTS_ONLY_18_PLUS",
  "UNKNOWN",
]);
export type DVBrandSafetyAppAgeRatingType = z.infer<
  typeof DVBrandSafetyAppAgeRatingTypeSchema
>;

export const DoubleVerifyBrandSafetyTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_BRAND_SAFETY",
]);
export type DoubleVerifyBrandSafetyTargetType = z.infer<
  typeof DoubleVerifyBrandSafetyTargetTypeSchema
>;

export const IASBrandSafetyLevelTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "BRAND_SAFETY_EXCLUDE_HIGH_RISK",
    "BRAND_SAFETY_EXCLUDE_HIGH_AND_MODERATE_RISK",
  ])
  .describe("The IAS brand safety risk level.");
export type IASBrandSafetyLevelType = z.infer<
  typeof IASBrandSafetyLevelTypeSchema
>;

export const IASBrandSafetyTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_BRAND_SAFETY",
]);
export type IASBrandSafetyTargetType = z.infer<
  typeof IASBrandSafetyTargetTypeSchema
>;

export const AverageCompletionAndFullyViewableRateTargetingTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_10",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_20",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_25",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_30",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_35",
    "AVG_COMPLETION_FULLY_VIEWABLE_GTE_40",
  ])
  .describe(
    "The type of average completion and fully viewable rate targeting."
  );
export type AverageCompletionAndFullyViewableRateTargetingType = z.infer<
  typeof AverageCompletionAndFullyViewableRateTargetingTypeSchema
>;

export const BrandExposureViewabilityTargetingTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "BRAND_EXPOSURE_VIEWABILITY_GTE_15_SEC_AVG_DURATION",
    "BRAND_EXPOSURE_VIEWABILITY_GTE_10_SEC_AVG_DURATION",
    "BRAND_EXPOSURE_VIEWABILITY_GTE_5_SEC_AVG_DURATION",
  ])
  .describe("The type of brand exposure viewability targeting.");
export type BrandExposureViewabilityTargetingType = z.infer<
  typeof BrandExposureViewabilityTargetingTypeSchema
>;

export const MrcViewabilityTargetingTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "MRC_VIEWABILITY_GTE_80",
    "MRC_VIEWABILITY_GTE_75",
    "MRC_VIEWABILITY_GTE_70",
    "MRC_VIEWABILITY_GTE_65",
    "MRC_VIEWABILITY_GTE_60",
    "MRC_VIEWABILITY_GTE_55",
    "MRC_VIEWABILITY_GTE_50",
    "MRC_VIEWABILITY_GTE_40",
    "MRC_VIEWABILITY_GTE_30",
  ])
  .describe("The type of MRC viewability targeting.");
export type MrcViewabilityTargetingType = z.infer<
  typeof MrcViewabilityTargetingTypeSchema
>;

export const DoubleVerifyViewabilityTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_VIEWABILITY",
]);
export type DoubleVerifyViewabilityTargetType = z.infer<
  typeof DoubleVerifyViewabilityTargetTypeSchema
>;

export const IASViewabilityStandardTypeSchema = z
  .enum(["NONE", "MRC", "GROUPM", "PUBLICIS"])
  .describe("The viewability standard.");
export type IASViewabilityStandardType = z.infer<
  typeof IASViewabilityStandardTypeSchema
>;

export const ViewabilityTierTypeSchema = z
  .enum([
    "ALLOW_ALL",
    "VIEWABILITY_TIER_GT_70",
    "VIEWABILITY_TIER_GT_60",
    "VIEWABILITY_TIER_GT_50",
    "VIEWABILITY_TIER_GT_40",
    "VIEWABILITY_TIER_LT_40",
  ])
  .describe("The type of viewability tier.");
export type ViewabilityTierType = z.infer<typeof ViewabilityTierTypeSchema>;

export const IASViewabilityTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_VIEWABILITY",
]);
export type IASViewabilityTargetType = z.infer<
  typeof IASViewabilityTargetTypeSchema
>;

export const DoubleVerifyAuthenticBrandSafetyTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_AUTHENTIC_BRAND_SAFETY",
]);
export type DoubleVerifyAuthenticBrandSafetyTargetType = z.infer<
  typeof DoubleVerifyAuthenticBrandSafetyTargetTypeSchema
>;

export const DVCustomContextualSegmentIdTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_CUSTOM_CONTEXTUAL_SEGMENT_ID",
]);
export type DVCustomContextualSegmentIdTargetType = z.infer<
  typeof DVCustomContextualSegmentIdTargetTypeSchema
>;

export const IASContextualTargetingTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_CONTEXTUAL_TARGETING",
]);
export type IASContextualTargetingTargetType = z.infer<
  typeof IASContextualTargetingTargetTypeSchema
>;

export const IASContextualAvoidanceTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_CONTEXTUAL_AVOIDANCE",
]);
export type IASContextualAvoidanceTargetType = z.infer<
  typeof IASContextualAvoidanceTargetTypeSchema
>;

export const DVAuthenticAttentionTargetTypeSchema = z.enum([
  "DOUBLE_VERIFY_AUTHENTIC_ATTENTION",
]);
export type DVAuthenticAttentionTargetType = z.infer<
  typeof DVAuthenticAttentionTargetTypeSchema
>;

export const IASQualitySyncTargetTypeSchema = z.enum([
  "INTEGRAL_AD_SCIENCE_QUALITY_SYNC",
]);
export type IASQualitySyncTargetType = z.infer<
  typeof IASQualitySyncTargetTypeSchema
>;

export const NewsGuardBrandGuardTrustedNewsTargetingEnumSchema = z
  .enum([
    "MAX_INCLUDE",
    "HIGH_INCLUDE",
    "BASIC_INCLUDE",
    "LOCAL_INCLUDE",
    "POLITICS_INCLUDE",
    "HEALTH_INCLUDE",
    "BUSINESS_INCLUDE",
    "COMMUNITY_INCLUDE",
    "LIFESTYLE_INCLUDE",
    "TECH_INCLUDE",
  ])
  .describe(
    "NewsGuard BrandGuard trusted news targeting categories to include in targeting (Web inventory only)."
  );
export type NewsGuardBrandGuardTrustedNewsTargetingEnum = z.infer<
  typeof NewsGuardBrandGuardTrustedNewsTargetingEnumSchema
>;

export const NewsGuardBrandGuardTrustedNewsTargetingTargetTypeSchema = z.enum([
  "NEWS_GUARD_BRAND_GUARD_TRUSTED_NEWS_TARGETING",
]);
export type NewsGuardBrandGuardTrustedNewsTargetingTargetType = z.infer<
  typeof NewsGuardBrandGuardTrustedNewsTargetingTargetTypeSchema
>;

export const NewsGuardBrandGuardMisinformationSafetyEnumSchema = z
  .enum([
    "MAX_EXCLUDE",
    "HIGH_EXCLUDE",
    "BASIC_EXCLUDE",
    "OPINIONATED_NEWS",
    "MISINFORMATION_SITES",
    "ELECTION_MISINFORMATION",
    "HEALTH_MISINFORMATION",
    "VACCINE_MISINFORMATION",
    "COVID_MISINFORMATION",
    "QANON_MISINFORMATION",
    "CLIMATE_MISINFORMATION",
    "UKRAINE_MISINFORMATION",
    "ISRAEL_HAMAS_MISINFORMATION",
    "AI_GENERATED_MFA",
  ])
  .describe(
    "NewsGuard BrandGuard Misinformation safety categories to exclude from targeting (Web inventory only)."
  );
export type NewsGuardBrandGuardMisinformationSafetyEnum = z.infer<
  typeof NewsGuardBrandGuardMisinformationSafetyEnumSchema
>;

export const NewsGuardBrandGuardMisinformationSafetyTargetTypeSchema = z.enum([
  "NEWS_GUARD_BRAND_GUARD_MISINFORMATION_SAFETY",
]);
export type NewsGuardBrandGuardMisinformationSafetyTargetType = z.infer<
  typeof NewsGuardBrandGuardMisinformationSafetyTargetTypeSchema
>;

export const IABCategoryTargetTypeSchema = z.enum(["IAB_CATEGORY"]);
export type IABCategoryTargetType = z.infer<typeof IABCategoryTargetTypeSchema>;

export const AdPlayerSizeSchema = z
  .enum(["SMALL", "MEDIUM", "LARGE", "UNKNOWN", "OTHER"])
  .describe(
    "Target the size of video players the ads in this line item can run on."
  );
export type AdPlayerSize = z.infer<typeof AdPlayerSizeSchema>;

export const AdPlayerSizeTargetTypeSchema = z.enum(["AD_PLAYER_SIZE"]);
export type AdPlayerSizeTargetType = z.infer<
  typeof AdPlayerSizeTargetTypeSchema
>;

export const VideoInitiationTypeSchema = z
  .enum(["USER_INITIATED", "AUTOPLAY", "UNKNOWN", "OTHER"])
  .describe("Target video inventory by how the video will be started.");
export type VideoInitiationType = z.infer<typeof VideoInitiationTypeSchema>;

export const AdInitiationTargetTypeSchema = z.enum(["AD_INITIATION"]);
export type AdInitiationTargetType = z.infer<
  typeof AdInitiationTargetTypeSchema
>;

export const VideoAdFormatSchema = z
  .enum(["INSTREAM", "FULL_EPISODE_PLAYER", "OUTSTREAM", "OTHER"])
  .describe(
    "Target a specific type of ad slot that will be used to serve the ad."
  );
export type VideoAdFormat = z.infer<typeof VideoAdFormatSchema>;

export const VideoAdFormatTargetTypeSchema = z.enum(["VIDEO_AD_FORMAT"]);
export type VideoAdFormatTargetType = z.infer<
  typeof VideoAdFormatTargetTypeSchema
>;

export const ContentGenreSchema = z
  .enum([
    "ACTION",
    "ADVENTURE",
    "ANIMATION",
    "BIOGRAPHY",
    "COMEDY",
    "CRIME",
    "DOCUMENTARY",
    "DRAMA",
    "FAMILY",
    "FANTASY",
    "FILM_NOIR",
    "GAME_SHOW",
    "HISTORY",
    "HORROR",
    "MUSICAL",
    "MYSTERY",
    "NEWS",
    "REALITY_TV",
    "ROMANCE",
    "SCIENCE_FICTION",
    "SHORT",
    "SPORT",
    "SUPER_HERO",
    "TALK_SHOW",
    "THRILLER",
    "WAR",
    "WESTERN",
    "GENRE_NOT_AVAILABLE",
    "OTHER",
  ])
  .describe("Exclude content based on genre.");
export type ContentGenre = z.infer<typeof ContentGenreSchema>;

export const ContentGenreTargetTypeSchema = z.enum(["CONTENT_GENRE"]);
export type ContentGenreTargetType = z.infer<
  typeof ContentGenreTargetTypeSchema
>;

export const ContentRatingTargetTypeSchema = z.enum(["CONTENT_RATING"]);
export type ContentRatingTargetType = z.infer<
  typeof ContentRatingTargetTypeSchema
>;

export const TwitchContentRatingTypeSchema = z.enum(["TWITCH_CONTENT_RATING"]);
export type TwitchContentRatingType = z.infer<
  typeof TwitchContentRatingTypeSchema
>;

export const TwitchContentRatingSchema = z
  .enum(["TWITCH_MODERATE", "TWITCH_RESTRICTIVE"])
  .describe(
    "Exclude Twitch content based on available content rating options."
  );
export type TwitchContentRating = z.infer<typeof TwitchContentRatingSchema>;

export const DspContentRatingTypeSchema = z.enum(["DSP_CONTENT_RATING"]);
export type DspContentRatingType = z.infer<typeof DspContentRatingTypeSchema>;

export const DspContentRatingSchema = z
  .enum([
    "SUITABLE_FOR_ALL_AUDIENCES",
    "SUITABLE_FOR_MOST_AUDIENCES_WITH_PARENTAL_GUIDANCE",
    "SUITABLE_FOR_TEEN_AND_OLDER_AUDIENCES",
    "SUITABLE_FOR_MATURE_AUDIENCES",
    "SUITABLE_FOR_ADULTS",
    "RATING_NOT_AVAILABLE",
    "OTHER",
  ])
  .describe("Exclude content based on available content rating options.");
export type DspContentRating = z.infer<typeof DspContentRatingSchema>;

export const ProductTargetMatchTypeSchema = z
  .enum(["PRODUCT_EXACT", "OTHER"])
  .describe("The product target match type.");
export type ProductTargetMatchType = z.infer<
  typeof ProductTargetMatchTypeSchema
>;

export const ProductTargetTypeSchema = z.enum(["PRODUCT"]);
export type ProductTargetType = z.infer<typeof ProductTargetTypeSchema>;

export const ProductCategoryTargetTypeSchema = z.enum(["PRODUCT_CATEGORY"]);
export type ProductCategoryTargetType = z.infer<
  typeof ProductCategoryTargetTypeSchema
>;

export const AutoTargetMatchTypeSchema = z
  .enum(["ASIN_RELATED", "OTHER"])
  .describe("The auto target match type.");
export type AutoTargetMatchType = z.infer<typeof AutoTargetMatchTypeSchema>;

export const AutoTargetTargetTypeSchema = z.enum(["AUTO"]);
export type AutoTargetTargetType = z.infer<typeof AutoTargetTargetTypeSchema>;

export const KeywordTargetMatchTypeSchema = z.enum(["BROAD"]);
export type KeywordTargetMatchType = z.infer<
  typeof KeywordTargetMatchTypeSchema
>;

export const KeywordTargetTypeSchema = z.enum(["KEYWORD"]);
export type KeywordTargetType = z.infer<typeof KeywordTargetTypeSchema>;

export const OnScreenFoldPositionSchema = z
  .enum(["ABOVE_THE_FOLD", "BELOW_THE_FOLD", "UNKNOWN", "OTHER"])
  .describe("Target a specific fold position on the screen.");
export type OnScreenFoldPosition = z.infer<typeof OnScreenFoldPositionSchema>;

export const OnScreenPositionTargetTypeSchema = z.enum(["ON_SCREEN_POSITION"]);
export type OnScreenPositionTargetType = z.infer<
  typeof OnScreenPositionTargetTypeSchema
>;

export const ContentInstreamPositionSchema = z
  .enum(["PRE_ROLL", "MID_ROLL", "POST_ROLL", "UNKNOWN", "OTHER"])
  .describe("Target a specific instream position.");
export type ContentInstreamPosition = z.infer<
  typeof ContentInstreamPositionSchema
>;

export const ContentInstreamPositionTargetTypeSchema = z.enum([
  "CONTENT_INSTREAM_POSITION",
]);
export type ContentInstreamPositionTargetType = z.infer<
  typeof ContentInstreamPositionTargetTypeSchema
>;

export const ContentOutstreamPositionSchema = z
  .enum([
    "STANDALONE",
    "ACCOMPANYING_CONTENT",
    "INTERSTITIAL",
    "UNKNOWN",
    "OTHER",
  ])
  .describe("Target a specific outstream position.");
export type ContentOutstreamPosition = z.infer<
  typeof ContentOutstreamPositionSchema
>;

export const ContentOutstreamPositionTargetTypeSchema = z.enum([
  "CONTENT_OUTSTREAM_POSITION",
]);
export type ContentOutstreamPositionTargetType = z.infer<
  typeof ContentOutstreamPositionTargetTypeSchema
>;

export const ContentDurationSchema = z
  .enum(["SHORT", "MEDIUM", "LONG", "EXTENDED", "UNKNOWN", "OTHER"])
  .describe("Target a specific content duration.");
export type ContentDuration = z.infer<typeof ContentDurationSchema>;

export const ContentDurationTargetTypeSchema = z.enum(["CONTENT_DURATION"]);
export type ContentDurationTargetType = z.infer<
  typeof ContentDurationTargetTypeSchema
>;

export const NativeContentPositionSchema = z
  .enum([
    "IN_ARTICLE",
    "IN_FEED",
    "PERIPHERAL",
    "RECOMMENDATION",
    "UNKNOWN",
    "OTHER",
  ])
  .describe("Target a specific native content position.");
export type NativeContentPosition = z.infer<typeof NativeContentPositionSchema>;

export const NativeContentPositionTargetTypeSchema = z.enum([
  "NATIVE_CONTENT_POSITION",
]);
export type NativeContentPositionTargetType = z.infer<
  typeof NativeContentPositionTargetTypeSchema
>;

export const TargetTypeSchema = z.enum([
  "AUDIENCE",
  "LOCATION",
  "DOMAIN",
  "APP",
  "DEVICE",
  "DAY_PART",
  "INVENTORY_SOURCE",
  "THIRD_PARTY",
  "IAB_CATEGORY",
  "AD_PLAYER_SIZE",
  "AD_INITIATION",
  "VIDEO_AD_FORMAT",
  "CONTENT_RATING",
  "CONTENT_GENRE",
  "PRODUCT",
  "PRODUCT_CATEGORY",
  "AUTO",
  "KEYWORD",
  "ON_SCREEN_POSITION",
  "CONTENT_INSTREAM_POSITION",
  "CONTENT_OUTSTREAM_POSITION",
  "CONTENT_DURATION",
  "NATIVE_CONTENT_POSITION",
]);
export type TargetType = z.infer<typeof TargetTypeSchema>;

// --- Schemas ---

// Leaf Schemas (mostly for oneOf structures)

export const AudienceTargetProxySchema = z.object({
  inGroupOperator: InGroupOperatorSchema.optional(),
  acrossGroupOperator: AcrossGroupOperatorSchema.optional(),
  groupId: z
    .string()
    .describe("The string identifying a group of audiences.")
    .optional(),
  targetType: AudienceTargetTypeSchema,
  audienceId: z.string().describe("The unique identifier for the audience."),
});
export type AudienceTargetProxy = z.infer<typeof AudienceTargetProxySchema>;

export const LocationTargetProxySchema = z.object({
  geoLocation: z.string().describe("The unique identifier for the location."),
  targetType: LocationTargetTypeSchema,
});
export type LocationTargetProxy = z.infer<typeof LocationTargetProxySchema>;

export const DomainListTargetSchema = z.object({
  domainListId: z
    .string()
    .describe("An existing list of domains stored at the entity-level."),
  domainTargetType: DomainListTargetTypeSchema,
});
export type DomainListTarget = z.infer<typeof DomainListTargetSchema>;

export const DomainNameTargetSchema = z.object({
  domainName: z.string().describe("A single domain name."),
  domainTargetType: DomainNameTargetTypeSchema,
});
export type DomainNameTarget = z.infer<typeof DomainNameTargetSchema>;

export const AdvertiserDomainListTargetSchema = z.object({
  inheritFromAdvertiser: z
    .boolean()
    .describe("Set to true to enable domain inheritance from advertiser."),
  domainTargetType: AdvertiserDomainListTargetTypeSchema,
});
export type AdvertiserDomainListTarget = z.infer<
  typeof AdvertiserDomainListTargetSchema
>;

export const DomainFileCreateTargetSchema = z.object({
  domainFileKey: z.string().describe("The S3 key of the uploaded file."),
  domainTargetType: DomainFileTargetTypeSchema,
  domainFileName: z
    .string()
    .max(255)
    .describe("A user-specified name of the file."),
});
export type DomainFileCreateTarget = z.infer<
  typeof DomainFileCreateTargetSchema
>;

export const DomainFileDeleteTargetSchema = z.object({
  domainTargetType: DomainFileTargetTypeSchema,
  domainFileId: z.string().describe("The domain file unique identifier."),
  domainFileName: z
    .string()
    .max(255)
    .describe("A user-specified name of the file."),
});
export type DomainFileDeleteTarget = z.infer<
  typeof DomainFileDeleteTargetSchema
>;

export const DomainFileListTargetSchema = z.object({
  domainFileUrl: z
    .string()
    .describe("The file containing the domains uploaded."),
  domainTargetType: DomainFileTargetTypeSchema,
  domainFileId: z.string().describe("The domain file unique identifier."),
  domainFileName: z
    .string()
    .max(255)
    .describe("A user-specified name of the file."),
});
export type DomainFileListTarget = z.infer<typeof DomainFileListTargetSchema>;

export const DomainFileTargetDetailsSchema = z.union([
  z.object({ domainFileCreate: DomainFileCreateTargetSchema }),
  z.object({ domainFileDelete: DomainFileDeleteTargetSchema }),
  z.object({ domainFileList: DomainFileListTargetSchema }),
]);
export type DomainFileTargetDetails = z.infer<
  typeof DomainFileTargetDetailsSchema
>;

export const DomainTargetDetailsSchema = z.union([
  z.object({ domainList: DomainListTargetSchema }),
  z.object({ domainName: DomainNameTargetSchema }),
  z.object({ advertiserDomainList: AdvertiserDomainListTargetSchema }),
  z.object({ domainFile: DomainFileTargetDetailsSchema }),
]);
export type DomainTargetDetails = z.infer<typeof DomainTargetDetailsSchema>;

export const DomainTargetProxySchema = z.object({
  domainTargetDetails: DomainTargetDetailsSchema,
  targetType: DomainTargetTypeSchema,
});
export type DomainTargetProxy = z.infer<typeof DomainTargetProxySchema>;

export const AppTargetProxySchema = z.object({
  appType: AppTypeSchema,
  appId: z.string().describe("The unique identifier for the app."),
  targetType: AppTargetTypeSchema,
});
export type AppTargetProxy = z.infer<typeof AppTargetProxySchema>;

export const DeviceTargetProxySchema = z.object({
  deviceType: DeviceTypeSchema,
  mobileEnvironment: MobileEnvironmentTypeSchema.optional(),
  mobileDevice: MobileDeviceTypeSchema.optional(),
  targetType: DeviceTargetTypeSchema,
  deviceOrientation: DeviceOrientationTypeSchema.optional(),
  mobileOS: MobileOSTypeSchema.optional(),
});
export type DeviceTargetProxy = z.infer<typeof DeviceTargetProxySchema>;

export const DayPartTargetProxySchema = z.object({
  hourSlot: z
    .number()
    .int()
    .min(0)
    .max(23)
    .describe("The hour slot in which the ad is to be served."),
  dayOfWeek: DayOfWeekSchema,
  targetType: DayPartTargetTypeSchema.optional(), // Optional in spec example? Re-check required if needed
});
export type DayPartTargetProxy = z.infer<typeof DayPartTargetProxySchema>;

export const InventorySourceTargetProxySchema = z.object({
  inventorySourceId: z.string().describe("The inventory source identifier."),
  inventorySourceType: InventorySourceTypeSchema,
  targetType: InventorySourceTargetTypeSchema,
});
export type InventorySourceTargetProxy = z.infer<
  typeof InventorySourceTargetProxySchema
>;

// --- Third Party Schemas Start ---
export const DoubleVerifyFraudInvalidTrafficSchema = z
  .object({
    excludeImpressions: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to exclude impressions delivered to devices identified to be fraudulent or invalid."
      ),
    thirdPartyTargetType: DoubleVerifyFraudInvalidTrafficTargetTypeSchema,
    excludeAppsAndSites: ExcludeAppsAndSitesTypeSchema.optional(),
    blockAppAndSites: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to block applications and sites with insufficient historical fraud and invalid traffic statistics."
      ),
  })
  .describe(
    "Supported InventoryTypes['STANDARD_DISPLAY', 'MOBILE_DISPLAY', AAP_MOBILE_APP', 'VIDEO']."
  );
export type DoubleVerifyFraudInvalidTraffic = z.infer<
  typeof DoubleVerifyFraudInvalidTrafficSchema
>;

export const IASFraudInvalidTrafficSchema = z
  .object({
    thirdPartyTargetType: IASFraudInvalidTrafficTargetTypeSchema,
    targetSetting: IASFraudInvalidTrafficTypeSchema.optional(),
  })
  .describe(
    "Supported InventoryTypes['STANDARD_DISPLAY', 'MOBILE_DISPLAY', AAP_MOBILE_APP', 'VIDEO']."
  );
export type IASFraudInvalidTraffic = z.infer<
  typeof IASFraudInvalidTrafficSchema
>;

export const PixalateFraudInvalidTrafficSchema = z
  .object({
    excludeIpAddressAndUserAgents: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to exclude traffic from IPV4 and IPV6 addresses and user agents identified to be fraudulent or invalid."
      ),
    excludeOttAndMobileDevices: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to exclude traffic from OTT and Mobile devices identified to be fraudulent or invalid."
      ),
    excludeRemovedAppsFromAppStores: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to exlude traffic from Apps that have been removed from the google play and apple app stores in the last 6 months."
      ),
    excludeAppsAndDomains: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to exclude traffic from Apps and Domains identified to be fraudulent or invalid."
      ),
    thirdPartyTargetType: PixalateFraudInvalidTrafficTargetTypeSchema,
  })
  .describe(
    "Supported InventoryTypes['STANDARD_DISPLAY', 'MOBILE_DISPLAY', AAP_MOBILE_APP', 'VIDEO']."
  );
export type PixalateFraudInvalidTraffic = z.infer<
  typeof PixalateFraudInvalidTrafficSchema
>;

export const DVBrandSafetyContentCategoriesWithRiskMapSchema = z
  .record(BrandSuitabilityRiskLevelTypeSchema)
  .describe(
    "A map from content categories to risk level to exclude from targeting."
  );
export type DVBrandSafetyContentCategoriesWithRiskMap = z.infer<
  typeof DVBrandSafetyContentCategoriesWithRiskMapSchema
>;

const UniqueArray = <T extends z.ZodTypeAny>(schema: T) =>
  z.array(schema).refine((items) => new Set(items).size === items.length, {
    message: "Array must contain unique items",
  });

export const DVStandardDisplayBrandSafetySchema = z.object({
  contentCategories: z
    .array(DVBrandSafetyContentCategoryTypeSchema)
    .max(50)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe("A list of content categories to exclude from targeting."),
  contentCategoriesWithRisk:
    DVBrandSafetyContentCategoriesWithRiskMapSchema.optional(),
  unknownContent: z
    .boolean()
    .optional()
    .describe("Set to `true` to exclude unknown content."),
  thirdPartyTargetType: DVStandardDisplayBrandSafetyTargetTypeSchema,
});
export type DVStandardDisplayBrandSafety = z.infer<
  typeof DVStandardDisplayBrandSafetySchema
>;

export const DoubleVerifyBrandSafetySchema = z.object({
  appStarRating: DVBrandSafetyAppStarRatingTypeSchema.optional(),
  contentCategories: z
    .array(DVBrandSafetyContentCategoryTypeSchema)
    .max(50)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe("A list of content categories to exclude from targeting."),
  appAgeRating: z
    .array(DVBrandSafetyAppAgeRatingTypeSchema)
    .max(50)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe("A list of app age ratings to be used for excluding apps."),
  excludeAppsWithInsufficientRating: z
    .boolean()
    .optional()
    .describe(
      "Set to `true` to exclude unofficial apps or apps with insufficient user ratings (<100 lifetime)."
    ),
  contentCategoriesWithRisk:
    DVBrandSafetyContentCategoriesWithRiskMapSchema.optional(),
  unknownContent: z
    .boolean()
    .optional()
    .describe("Set to `true` to exclude unknown content."),
  thirdPartyTargetType: DoubleVerifyBrandSafetyTargetTypeSchema,
});
export type DoubleVerifyBrandSafety = z.infer<
  typeof DoubleVerifyBrandSafetySchema
>;

export const IASBrandSafetySchema = z.object({
  iasBrandSafetyOffensiveLanguage: IASBrandSafetyLevelTypeSchema.optional(),
  excludeContent: z
    .boolean()
    .optional()
    .describe(
      "Set to `true` to exclude content that Integral Ad Science is not able to rate."
    ),
  iasBrandSafetyAlcohol: IASBrandSafetyLevelTypeSchema.optional(),
  iasBrandSafetyIllegalDownloads: IASBrandSafetyLevelTypeSchema.optional(),
  thirdPartyTargetType: IASBrandSafetyTargetTypeSchema,
  iasBrandSafetyHateSpeech: IASBrandSafetyLevelTypeSchema.optional(),
  iasBrandSafetyIllegalDrugs: IASBrandSafetyLevelTypeSchema.optional(),
  iasBrandSafetyAdult: IASBrandSafetyLevelTypeSchema.optional(),
  iasBrandSafetyViolence: IASBrandSafetyLevelTypeSchema.optional(),
  iasBrandSafetyGambling: IASBrandSafetyLevelTypeSchema.optional(),
});
export type IASBrandSafety = z.infer<typeof IASBrandSafetySchema>;

export const DoubleVerifyViewabilitySchema = z
  .object({
    averageCompletionAndFullyViewableRateTargeting:
      AverageCompletionAndFullyViewableRateTargetingTypeSchema.optional(),
    brandExposureViewabilityTargeting:
      BrandExposureViewabilityTargetingTypeSchema.optional(),
    mrcViewabilityTargeting: MrcViewabilityTargetingTypeSchema.optional(),
    thirdPartyTargetType: DoubleVerifyViewabilityTargetTypeSchema,
    includeUnmeasurableImpressions: z
      .boolean()
      .optional()
      .describe(
        "Set to `true` to include impressions where impressions can't be measured."
      ),
  })
  .describe(
    "Supported InventoryTypes['STANDARD_DISPLAY', 'AAP_MOBILE_APP', 'VIDEO']."
  );
export type DoubleVerifyViewability = z.infer<
  typeof DoubleVerifyViewabilitySchema
>;

export const IASViewabilitySchema = z.object({
  standard: IASViewabilityStandardTypeSchema,
  viewabilityTargeting: ViewabilityTierTypeSchema.optional(),
  thirdPartyTargetType: IASViewabilityTargetTypeSchema,
});
export type IASViewability = z.infer<typeof IASViewabilitySchema>;

export const DoubleVerifyAuthenticBrandSafetySchema = z.object({
  doubleVerifySegmentId: z
    .string()
    .regex(/^51[0-9]{6}$/)
    .optional()
    .describe("The segment identifier."),
  thirdPartyTargetType: DoubleVerifyAuthenticBrandSafetyTargetTypeSchema,
});
export type DoubleVerifyAuthenticBrandSafety = z.infer<
  typeof DoubleVerifyAuthenticBrandSafetySchema
>;

export const DVCustomContextualSegmentIdSchema = z.object({
  segmentId: z
    .string()
    .regex(/^52[0-9]{6}$/)
    .optional()
    .describe("The custom segment identifier."),
  thirdPartyTargetType: DVCustomContextualSegmentIdTargetTypeSchema,
});
export type DVCustomContextualSegmentId = z.infer<
  typeof DVCustomContextualSegmentIdSchema
>;

export const IASContextualTargetingSchema = z.object({
  topicalSegments: z
    .array(z.string())
    .max(200)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe(
      "The unique identifier of the IAS contextual topical targeting segment"
    ),
  thirdPartyTargetType: IASContextualTargetingTargetTypeSchema,
  verticalSegments: z
    .array(z.string())
    .max(200)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe(
      "The unique identifier of the IAS contextual vertical targeting segment"
    ),
});
export type IASContextualTargeting = z.infer<
  typeof IASContextualTargetingSchema
>;

export const IASContextualAvoidanceSchema = z.object({
  avoidanceSegments: z
    .array(z.string())
    .max(200)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional()
    .describe("The unique identifier of the IAS contextual avoidance segment"),
  thirdPartyTargetType: IASContextualAvoidanceTargetTypeSchema,
});
export type IASContextualAvoidance = z.infer<
  typeof IASContextualAvoidanceSchema
>;

export const DVAuthenticAttentionSchema = z.object({
  universalAttention: z
    .boolean()
    .describe(
      "One omni-channel segment that is informed by data from all DV campaigns."
    ),
  thirdPartyTargetType: DVAuthenticAttentionTargetTypeSchema,
});
export type DVAuthenticAttention = z.infer<typeof DVAuthenticAttentionSchema>;

export const IASQualitySyncSchema = z.object({
  segmentId: z
    .string()
    .regex(/^4[0-9]{6}$/)
    .optional()
    .describe("The unique identifier of the IAS quality sync segment"),
  thirdPartyTargetType: IASQualitySyncTargetTypeSchema,
});
export type IASQualitySync = z.infer<typeof IASQualitySyncSchema>;

export const NewsGuardBrandGuardTrustedNewsTargetingSchema = z.object({
  targetingList: z
    .array(NewsGuardBrandGuardTrustedNewsTargetingEnumSchema)
    .max(15)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional(),
  thirdPartyTargetType: NewsGuardBrandGuardTrustedNewsTargetingTargetTypeSchema,
});
export type NewsGuardBrandGuardTrustedNewsTargeting = z.infer<
  typeof NewsGuardBrandGuardTrustedNewsTargetingSchema
>;

export const NewsGuardBrandGuardMisinformationSafetySchema = z.object({
  thirdPartyTargetType: NewsGuardBrandGuardMisinformationSafetyTargetTypeSchema,
  avoidanceList: z
    .array(NewsGuardBrandGuardMisinformationSafetyEnumSchema)
    .max(20)
    .refine((items) => new Set(items).size === items.length, {
      message: "Array must contain unique items",
    })
    .optional(),
});
export type NewsGuardBrandGuardMisinformationSafety = z.infer<
  typeof NewsGuardBrandGuardMisinformationSafetySchema
>;

export const ThirdPartyTargetDetailsSchema = z.union([
  z.object({
    doubleVerifyFraudInvalidTraffic: DoubleVerifyFraudInvalidTrafficSchema,
  }),
  z.object({
    integralAdScienceFraudInvalidTraffic: IASFraudInvalidTrafficSchema,
  }),
  z.object({ pixalateFraudInvalidTraffic: PixalateFraudInvalidTrafficSchema }),
  z.object({
    doubleVerifyStandardDisplayBrandSafety: DVStandardDisplayBrandSafetySchema,
  }),
  z.object({ doubleVerifyBrandSafety: DoubleVerifyBrandSafetySchema }),
  z.object({ integralAdScienceBrandSafety: IASBrandSafetySchema }),
  z.object({ doubleVerifyViewability: DoubleVerifyViewabilitySchema }),
  z.object({ integralAdScienceViewability: IASViewabilitySchema }),
  z.object({
    doubleVerifyAuthenticBrandSafety: DoubleVerifyAuthenticBrandSafetySchema,
  }),
  z.object({
    doubleVerifyCustomContextualSegmentId: DVCustomContextualSegmentIdSchema,
  }),
  z.object({
    integralAdScienceContextualTargeting: IASContextualTargetingSchema,
  }),
  z.object({
    integralAdScienceContextualAvoidance: IASContextualAvoidanceSchema,
  }),
  z.object({ doubleVerifyAuthenticAttention: DVAuthenticAttentionSchema }),
  z.object({ integralAdScienceQualitySync: IASQualitySyncSchema }),
  z.object({
    newsGuardBrandGuardTrustedNewsTargeting:
      NewsGuardBrandGuardTrustedNewsTargetingSchema,
  }),
  z.object({
    newsGuardBrandGuardMisinformationSafety:
      NewsGuardBrandGuardMisinformationSafetySchema,
  }),
]);
export type ThirdPartyTargetDetails = z.infer<
  typeof ThirdPartyTargetDetailsSchema
>;

export const ThirdPartyTargetProxySchema = z.object({
  targetType: ThirdPartyTargetTypeSchema,
  thirdPartyTargetDetails: ThirdPartyTargetDetailsSchema,
});
export type ThirdPartyTargetProxy = z.infer<typeof ThirdPartyTargetProxySchema>;
// --- Third Party Schemas End ---

export const IABCategoryTargetProxySchema = z.object({
  iabContentCategory: z
    .string()
    .describe("The IAB Content Category to target."),
  targetType: IABCategoryTargetTypeSchema,
});
export type IABCategoryTargetProxy = z.infer<
  typeof IABCategoryTargetProxySchema
>;

export const AdPlayerSizeTargetProxySchema = z.object({
  adPlayerSize: AdPlayerSizeSchema,
  targetType: AdPlayerSizeTargetTypeSchema,
});
export type AdPlayerSizeTargetProxy = z.infer<
  typeof AdPlayerSizeTargetProxySchema
>;

export const AdInitiationTargetProxySchema = z.object({
  videoInitiationType: VideoInitiationTypeSchema,
  targetType: AdInitiationTargetTypeSchema,
});
export type AdInitiationTargetProxy = z.infer<
  typeof AdInitiationTargetProxySchema
>;

export const VideoAdFormatTargetProxySchema = z.object({
  videoAdFormat: VideoAdFormatSchema,
  targetType: VideoAdFormatTargetTypeSchema,
});
export type VideoAdFormatTargetProxy = z.infer<
  typeof VideoAdFormatTargetProxySchema
>;

export const ContentGenreTargetProxySchema = z.object({
  contentGenre: ContentGenreSchema,
  targetType: ContentGenreTargetTypeSchema,
});
export type ContentGenreTargetProxy = z.infer<
  typeof ContentGenreTargetProxySchema
>;

export const TwitchContentRatingTargetSchema = z.object({
  contentRatingType: TwitchContentRatingTypeSchema,
  twitchContentRating: TwitchContentRatingSchema,
});
export type TwitchContentRatingTarget = z.infer<
  typeof TwitchContentRatingTargetSchema
>;

export const DspContentRatingTargetSchema = z.object({
  contentRatingType: DspContentRatingTypeSchema,
  dspContentRating: DspContentRatingSchema,
});
export type DspContentRatingTarget = z.infer<
  typeof DspContentRatingTargetSchema
>;

export const ContentRatingTypeDetailsSchema = z.union([
  z.object({ twitchContentRatingTarget: TwitchContentRatingTargetSchema }),
  z.object({ dspContentRatingTarget: DspContentRatingTargetSchema }),
]);
export type ContentRatingTypeDetails = z.infer<
  typeof ContentRatingTypeDetailsSchema
>;

export const ContentRatingTargetProxySchema = z.object({
  targetType: ContentRatingTargetTypeSchema,
  contentRatingTypeDetails: ContentRatingTypeDetailsSchema,
});
export type ContentRatingTargetProxy = z.infer<
  typeof ContentRatingTargetProxySchema
>;

export const ProductTargetProxySchema = z.object({
  matchType: ProductTargetMatchTypeSchema,
  targetType: ProductTargetTypeSchema,
  asin: z.string().describe("The product asin to target"),
});
export type ProductTargetProxy = z.infer<typeof ProductTargetProxySchema>;

export const ProductCategoryTargetProxySchema = z.object({
  asinCategory: z.string().describe("The product category to target."),
  targetType: ProductCategoryTargetTypeSchema,
});
export type ProductCategoryTargetProxy = z.infer<
  typeof ProductCategoryTargetProxySchema
>;

export const AutoTargetProxySchema = z.object({
  matchType: AutoTargetMatchTypeSchema,
  targetType: AutoTargetTargetTypeSchema,
});
export type AutoTargetProxy = z.infer<typeof AutoTargetProxySchema>;

export const KeywordTargetProxySchema = z.object({
  matchType: KeywordTargetMatchTypeSchema,
  targetType: KeywordTargetTypeSchema,
  keyword: z.string().describe("The keyword text to target"),
});
export type KeywordTargetProxy = z.infer<typeof KeywordTargetProxySchema>;

export const OnScreenPositionTargetProxySchema = z.object({
  targetType: OnScreenPositionTargetTypeSchema,
  foldPosition: OnScreenFoldPositionSchema,
});
export type OnScreenPositionTargetProxy = z.infer<
  typeof OnScreenPositionTargetProxySchema
>;

export const ContentInstreamPositionTargetProxySchema = z.object({
  instreamPosition: ContentInstreamPositionSchema,
  targetType: ContentInstreamPositionTargetTypeSchema,
});
export type ContentInstreamPositionTargetProxy = z.infer<
  typeof ContentInstreamPositionTargetProxySchema
>;

export const ContentOutstreamPositionTargetProxySchema = z.object({
  targetType: ContentOutstreamPositionTargetTypeSchema,
  outstreamPosition: ContentOutstreamPositionSchema,
});
export type ContentOutstreamPositionTargetProxy = z.infer<
  typeof ContentOutstreamPositionTargetProxySchema
>;

export const ContentDurationTargetProxySchema = z.object({
  duration: ContentDurationSchema,
  targetType: ContentDurationTargetTypeSchema,
});
export type ContentDurationTargetProxy = z.infer<
  typeof ContentDurationTargetProxySchema
>;

export const NativeContentPositionTargetProxySchema = z.object({
  nativePosition: NativeContentPositionSchema,
  targetType: NativeContentPositionTargetTypeSchema,
});
export type NativeContentPositionTargetProxy = z.infer<
  typeof NativeContentPositionTargetProxySchema
>;

// Target Details Union
export const TargetDetailsSchema = z.union([
  z.object({ audienceTarget: AudienceTargetProxySchema }),
  z.object({ locationTarget: LocationTargetProxySchema }),
  z.object({ domainTarget: DomainTargetProxySchema }),
  z.object({ appTarget: AppTargetProxySchema }),
  z.object({ deviceTarget: DeviceTargetProxySchema }),
  z.object({ dayPartTarget: DayPartTargetProxySchema }),
  z.object({ inventorySourceTarget: InventorySourceTargetProxySchema }),
  z.object({ thirdPartyTarget: ThirdPartyTargetProxySchema }),
  z.object({ iabCategoryTarget: IABCategoryTargetProxySchema }),
  z.object({ adPlayerSizeTarget: AdPlayerSizeTargetProxySchema }),
  z.object({ adInitiationTarget: AdInitiationTargetProxySchema }),
  z.object({ videoAdFormatTarget: VideoAdFormatTargetProxySchema }),
  z.object({ contentGenreTarget: ContentGenreTargetProxySchema }),
  z.object({ contentRatingTarget: ContentRatingTargetProxySchema }),
  z.object({ productTarget: ProductTargetProxySchema }),
  z.object({ productCategoryTarget: ProductCategoryTargetProxySchema }),
  z.object({ autoTarget: AutoTargetProxySchema }),
  z.object({ keywordTarget: KeywordTargetProxySchema }),
  z.object({ onScreenPositionTarget: OnScreenPositionTargetProxySchema }),
  z.object({
    contentInstreamPositionTarget: ContentInstreamPositionTargetProxySchema,
  }),
  z.object({
    contentOutstreamPositionTarget: ContentOutstreamPositionTargetProxySchema,
  }),
  z.object({ contentDurationTarget: ContentDurationTargetProxySchema }),
  z.object({
    nativeContentPositionTarget: NativeContentPositionTargetProxySchema,
  }),
]);
export type TargetDetails = z.infer<typeof TargetDetailsSchema>;

// Main Target Schema
export const TargetSchema = z.object({
  negative: z
    .boolean()
    .default(false)
    .describe("Whether to include (false) or exclude (true) users."),
  targetId: z.string().optional().describe("The identifier of the target"),
  adProduct: AdProductSchema.default(AdProductSchema.Enum.AMAZON_DSP),
  state: StateTypeSchema.default(StateTypeSchema.Enum.ENABLED),
  lastUpdatedDateTime: z
    .string()
    .datetime({ precision: 3, offset: true })
    .optional()
    .describe("Read-only timestamp"), // ISO 8601 format
  targetDetails: TargetDetailsSchema,
  adGroupId: z
    .string()
    .regex(/^\d+$/)
    .optional()
    .describe("The identifier of an existing ad group."),
  creationDateTime: z
    .string()
    .datetime({ precision: 3, offset: true })
    .optional()
    .describe("Read-only timestamp"), // ISO 8601 format
});
export type Target = z.infer<typeof TargetSchema>;
