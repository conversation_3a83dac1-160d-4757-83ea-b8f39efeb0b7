import { z } from "zod";

const DateTimeInstantSchema = z.coerce.date();
const DateTimeSchema = z.coerce.date();
const CanonicalIdSchema = z.string();
const MarketplaceIdSchema = z.string();
const GuidanceNameSchema = z.enum([
  "UNDERPACING_ORDER_GUIDANCE",
  "NO_SPEND_ORDER_GUIDANCE",
  "CREATIVE_REJECTED_ORDER_GUIDANCE",
  "UNDERPACING_LINE_ITEM_GUIDANCE",
  "LINE_ITEM_DEFAULT_GUIDANCE",
  "INTERNAL_DEFECT_GUIDANCE",
  "OUT_OF_STOCK_GUIDANCE",
  "ZERO_KPI_GUIDANCE",
  "AMAZON_RECOMMENDED_TACTICS_GUIDANCE",
  "PRE_FLIGHT_OPTIMIZATIONS_ORDER_GUIDANCE",
  "PRE_FLIGHT_OPTIMIZATIONS_LINE_ITEM_GUIDANCE",
  "PERFORMANCE_AT_RISK_GUIDANCE",
  "OVERDELIVERING_ORDER_GUIDANCE",
  "AMAZON_RECOMMENDED_DEALS_GUIDANCE",
  "AGGREGATED_UNDERPACING_ORDER_GUIDANCE",
  "AGGREGATED_PRE_FLIGHT_OPTIMIZATIONS_GUIDANCE",
  "AGGREGATED_PRE_FLIGHT_DEALS_GUIDANCE",
  "NO_TRAFFIC_DEAL_GUIDANCE",
]);
const GuidanceTypeSchema = z.enum(["ALERT", "OPPORTUNITY"]);

const GuidanceCallToActionSchema = z
  .object({
    link: z.string().optional().describe("Optional link for call-to-action."),
    label: z
      .string()
      .optional()
      .describe("Button or link label for call-to-action."),
  })
  .describe("Schema for the call to action in guidance.");

const GuidanceTargetSchema = z
  .object({
    name: z
      .string()
      .optional()
      .describe("Optional display name of the guidance target."),
  })
  .describe("An object targeted by guidance.");

const EntityIdSchema = z.string().describe("An identifier for an entity.");

const GuidanceMarketplaceTargetSchema = GuidanceTargetSchema.extend({
  marketplaceId: MarketplaceIdSchema.optional(),
}).describe("A marketplace targeted by guidance.");

const GuidanceEntityTargetSchema = GuidanceTargetSchema.extend({
  entityId: EntityIdSchema.optional(),
}).describe("An entity targeted by guidance.");

const GuidanceAdvertiserTargetSchema = GuidanceTargetSchema.extend({
  advertiserId: CanonicalIdSchema.optional(),
}).describe("An advertiser targeted by guidance.");

const GuidanceOrderTargetSchema = GuidanceTargetSchema.extend({
  orderId: CanonicalIdSchema.optional(),
}).describe("An order targeted by guidance.");

const GuidanceLineItemTargetSchema = GuidanceTargetSchema.extend({
  lineItemId: CanonicalIdSchema.optional(),
}).describe("A line item targeted by guidance.");

const GuidanceTargetUnionSchema = z.union([
  GuidanceMarketplaceTargetSchema,
  GuidanceEntityTargetSchema,
  GuidanceAdvertiserTargetSchema,
  GuidanceOrderTargetSchema,
  GuidanceLineItemTargetSchema,
]);

const DspObjectSchema = z
  .object({
    name: z
      .string()
      .optional()
      .describe(
        "Name of the DSP object that is set by user during initialisation step."
      ),
    id: CanonicalIdSchema.optional(),
  })
  .describe("The DSP object to which the recommendation is related.");

const UserStatusSchema = z
  .enum(["DISMISSED", "DEFERRED"])
  .describe("Status for the recommendation set by user.");

const RecommendationTextSchema = z
  .string()
  .describe("Description text for the recommendation.");

const TypeSchema = z
  .string()
  .describe(
    "Describes the purpose for the recommendation, for example DEPRECATED_AUDIENCE_REMOVAL"
  );

const TargetSchema = z
  .object({
    displayName: z
      .string()
      .optional()
      .describe("Optional target display name, e.g. the Rodeo order name."),
    id: z
      .string()
      .optional()
      .describe("Target identifier, e.g. Rodeo orderCfid."),
    type: z
      .enum([
        "MARKETPLACE_ID",
        "RODEO_ENTITY",
        "RODEO_ADVERTISER",
        "RODEO_PROPOSAL",
        "RODEO_ORDER",
        "RODEO_LINEITEM",
        "RODEO_CREATIVE",
        "RODEO_DEAL",
        "SPOT_ID",
        "RODEO_LINEITEM_OBJECT",
        "SESSION_ID",
      ])
      .optional()
      .describe("Type of the target, e.g. RODEO_ORDER."),
  })
  .describe("An object targeted by a recommendation, e.g a Rodeo order.");

const TableColumnSchema = z
  .object({
    width: z
      .number()
      .int()
      .min(1)
      .describe(
        "Column width ratio expressed as whole integer - does not necessarily add up to 100.\nRatio must be positive.\n\nE.g. four columns with widths [20, 15, 50, 15] to be rendered as [20/100, 15/100, 50/100, 15/100] width\nrespectively or [2, 5, 2, 3] to be rendered as [2/12, 5/12, 2/12, 3/12]."
      ),
    header: z.string().describe("Column header"),
    columnType: z.string().optional().describe("Column type"),
    value: z.object({}).passthrough().optional(), // Using passthrough as value can be any object
  })
  .describe("A single column to be rendered in the front-end.");

const CategorySchema = z
  .enum([
    "REPAIR",
    "DELIVERY",
    "PERFORMANCE",
    "BUDGET",
    "RETAIL",
    "TARGETING",
    "BIDDING",
  ])
  .describe(
    "Category for the recommendation whether it is to improve delivery, performance ...etc:\n * `REPAIR` - Category for recommendations to suggest how to fix a defect identified on a certain line or order\n * `DELIVERY` - Category for recommendations to improve the delivery of a certain line or order\n * `PERFORMANCE` - Category for recommendations to improve the performance of a certain line or order\n * `BUDGET` - Category for recommendations to optimise the budget allocated on a certain line or order\n * `RETAIL` - Category for recommendations generated based on retail data\n * `TARGETING` - Category for recommendations to change the audiences targeted for a certain line or order\n * `BIDDING` - Category for recommendations to change bid values for a certain line or order\n"
  );

const RecommendationIdSchema = z
  .string()
  .min(50)
  .max(500)
  .describe("A unique identifier for recommendation.");

export const QuickActionSchema = z
  .object({
    actionType: z
      .enum([
        "AUDIENCEREMOVAL",
        "AUDIENCEREPLACEMENT",
        "HIBOUENROLLMENT",
        "VIEWABILITYUPDATE",
        "BASEBIDUPDATE",
        "MOVEBUDGET",
        "ORDERBASEBIDUPDATE",
        "ORDERVIEWABILITYUPDATE",
        "MOVEBUDGETBETA",
        "ORDERMAXBIDUPDATE",
        "MAXBIDUPDATE",
        "FREQUENCYCAPUPDATE",
      ])
      .optional()
      .describe("String identifying the type of suggested action."),
    actionId: CanonicalIdSchema,
    description: z
      .string()
      .describe("Description for the recommendation action."),
    currentValue: z
      .string()
      .optional()
      .describe("Current setting value that triggered the recommendation."),
    recommendedValue: z
      .string()
      .optional()
      .describe("Recommended setting value to be applied by the Quick Action."),
  })
  .describe(
    "An object describing the action associated with this recommendation."
  );

const ExecutionSchema = z
  .object({
    executionId: CanonicalIdSchema.optional(),
    messageType: z
      .enum([
        "WORLD_STATE_CHANGED_EXCEPTION",
        "MOVE_BUDGET_WORLD_STATE_CHANGED_EXCEPTION",
        "PREVIEW",
        "IN_PROGRESS",
        "COMPLETED",
        "FAILED_GENERIC_MESSAGE",
      ])
      .optional()
      .describe(
        "Type of message to be used for errors or omissions, which will be mapped to localised strings at the client side."
      ),
    lastUpdateDate: DateTimeSchema.optional(),
    executionStatus: z
      .enum([
        "CREATED",
        "IN_PROGRESS",
        "FAILED",
        "COMPLETED",
        "EXPECTED_FAILURE",
      ])
      .optional()
      .describe("Status of the execution."),
    actionId: CanonicalIdSchema.optional(),
    message: z
      .string()
      .optional()
      .describe("Generic message to be used for errors or omissions."),
    creationDate: DateTimeSchema.optional(),
  })
  .describe("The execution done by QuickActions on the recommendation.");

const QuickactionsDataSchema = z
  .object({
    executionHistory: z
      .array(ExecutionSchema)
      .max(5)
      .optional()
      .describe(
        "The history of existing QuickAction executions on this recommendation."
      ),
    currentActions: z
      .array(QuickActionSchema)
      .max(5)
      .optional()
      .describe("Current active recommended actions."),
  })
  .readonly()
  .describe(
    "The automated QuickAction object associated with the recommendation."
  );

const RecommendationSchema = z
  .object({
    advertiser: DspObjectSchema.optional(),
    userStatus: UserStatusSchema.optional(),
    lastUpdateDate: DateTimeSchema.optional(),
    deferredUntil: DateTimeSchema.optional(),
    description: RecommendationTextSchema.optional(),
    aggregatedItems: z.record(z.string(), z.unknown()).optional(),
    expectedChanges: z
      .string()
      .describe(
        "Expected changes are represented as a json array of arrays and are interpreted to be in a Disjunctive normal form (DNF)."
      )
      .optional(),
    entityId: EntityIdSchema.optional(),
    recommendationId: RecommendationIdSchema.optional(),
    explanation: RecommendationTextSchema.optional(),
    title: RecommendationTextSchema.optional(),
    type: TypeSchema.optional(),
    targets: z
      .array(z.array(TargetSchema).max(50))
      .max(10)
      .optional()
      .describe(
        "Nested list of lists used to group related objects targeted by this recommendation."
      ),
    quickactionsData: QuickactionsDataSchema.optional(),
    marketplaceId: MarketplaceIdSchema.optional(),
    guidanceType: GuidanceTypeSchema.optional(),
    lineItem: DspObjectSchema.optional(),
    category: CategorySchema.optional(),
    recommendationType: z.string().optional(),
    table: z
      .array(TableColumnSchema)
      .max(10)
      .optional()
      .describe("List of columns to display"),
    order: DspObjectSchema.optional(),
  })
  .describe(
    "An object describing the DSP recommendation generated to improve the campaign performance for display advertising."
  );

export const GuidanceSchema = z
  .object({
    totalRecommendationCount: z
      .number()
      .int()
      .describe(
        "Total count of recommendations used to generate this guidance."
      ),
    groupRecommendationsBy: z
      .enum([
        "MARKETPLACE_ID",
        "RODEO_ENTITY",
        "RODEO_ADVERTISER",
        "RODEO_ORDER",
        "RODEO_LINEITEM",
        "RODEO_CREATIVE",
      ])
      .optional()
      .describe("Group recommendations according to this target in the UI"),
    description: z.string().describe("Description text of the guidance."),
    title: z.string().describe("Title text of the guidance."),
    explanation: z
      .string()
      .optional()
      .describe("Explanation text of the guidance."),
    targets: z
      .array(GuidanceTargetUnionSchema)
      .max(10)
      .describe(
        "Objects targeted by this guidance depending on the aggregation level, including `ENTITY`, `ADVERTISER` and `ORDER`."
      ),
    recommendations: z
      .array(RecommendationSchema)
      .max(100)
      .optional()
      .describe(
        "List of dynamically aggregated recommendations used to generate this guidance."
      ),
    callToAction: GuidanceCallToActionSchema.optional(),
    marketplaceId: MarketplaceIdSchema.describe(
      "The identifier of the marketplace to which the guidance is associated with."
    ),
    prioritisationScore: z
      .number()
      .describe(
        "Guidance prioritisation score based on aggregated recommendations to determine ranking of multiple guidance items among each other."
      ),
    guidanceType: GuidanceTypeSchema.describe(
      "Guidance type for the guidance whether it is an Alert or Opportunity:\n * `ALERT` - Guidance type for recommendations which are Alerts\n * `OPPORTUNITY` - Guidance type for recommendations which are Opportunities\n"
    ),
    lastUpdatedDate: DateTimeInstantSchema.describe(
      "A string representation of this instant formatted as ISO-8601 cast to an Instant datatype."
    ),
    generatedDate: DateTimeInstantSchema.describe(
      "A string representation of this instant formatted as ISO-8601 cast to an Instant datatype."
    ),
    guidanceName: GuidanceNameSchema.describe(
      "Identifier for a piece of guidance."
    ),
  })
  .describe(
    "An object describing dynamically created guidance derived by the rule-based aggregation of individual recommendations."
  );

export type DateTimeInstant = z.infer<typeof DateTimeInstantSchema>;
export type DateTime = z.infer<typeof DateTimeSchema>;
export type CanonicalId = z.infer<typeof CanonicalIdSchema>;
export type MarketplaceId = z.infer<typeof MarketplaceIdSchema>;
export type GuidanceName = z.infer<typeof GuidanceNameSchema>;
export type GuidanceType = z.infer<typeof GuidanceTypeSchema>;
export type GuidanceCallToAction = z.infer<typeof GuidanceCallToActionSchema>;
export type GuidanceTarget = z.infer<typeof GuidanceTargetSchema>;
export type EntityId = z.infer<typeof EntityIdSchema>;
export type GuidanceMarketplaceTarget = z.infer<
  typeof GuidanceMarketplaceTargetSchema
>;
export type GuidanceEntityTarget = z.infer<typeof GuidanceEntityTargetSchema>;
export type GuidanceAdvertiserTarget = z.infer<
  typeof GuidanceAdvertiserTargetSchema
>;
export type GuidanceOrderTarget = z.infer<typeof GuidanceOrderTargetSchema>;
export type GuidanceLineItemTarget = z.infer<
  typeof GuidanceLineItemTargetSchema
>;
export type GuidanceTargetUnion = z.infer<typeof GuidanceTargetUnionSchema>;
export type DspObject = z.infer<typeof DspObjectSchema>;
export type UserStatus = z.infer<typeof UserStatusSchema>;
export type RecommendationText = z.infer<typeof RecommendationTextSchema>;
export type Type = z.infer<typeof TypeSchema>;
export type Target = z.infer<typeof TargetSchema>;
export type TableColumn = z.infer<typeof TableColumnSchema>;
export type Category = z.infer<typeof CategorySchema>;
export type RecommendationId = z.infer<typeof RecommendationIdSchema>;
export type QuickAction = z.infer<typeof QuickActionSchema>;
export type Execution = z.infer<typeof ExecutionSchema>;
export type QuickactionsData = z.infer<typeof QuickactionsDataSchema>;
export type Recommendation = z.infer<typeof RecommendationSchema>;
export type Guidance = z.infer<typeof GuidanceSchema>;
