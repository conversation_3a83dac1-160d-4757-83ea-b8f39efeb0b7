import { z } from "zod";

export const FeeSchema = z.object({
  feeValue: z.string(),
  feeType: z.string(),
  feeValueType: z.string(),
  currencyCode: z.string().nullish(),
});
export type Fee = z.infer<typeof FeeSchema>;

export const BudgetCapSchema = z.object({
  amount: z.number(),
  recurrenceTimePeriod: z.string(),
  currencyCode: z.string().nullish(),
});
export type BudgetCap = z.infer<typeof BudgetCapSchema>;

export const PacingSchema = z.object({
  deliveryProfile: z.string(),
  catchUpBoostPercentage: z.number(),
});
export type Pacing = z.infer<typeof PacingSchema>;

export const AmazonViewabilitySchema = z.object({
  viewabilityTier: z.string(),
  includeUnmeasurableImpressions: z.boolean(),
});
export type AmazonViewability = z.infer<typeof AmazonViewabilitySchema>;

export const TargetingSettingsSchema = z.object({
  videoCompletionTier: z.string(),
  amazonViewability: AmazonViewabilitySchema,
  timeZoneType: z.string(),
  userLocation: z.string(),
  defaultAudienceTargetingMatchType: z.string(),
  userLocationSignal: z.string(),
  enableLanguageTargeting: z.boolean(),
});
export type TargetingSettings = z.infer<typeof TargetingSettingsSchema>;

export const OptimizationSchema = z
  .object({
    bidStrategy: z.string(),
    dailyMinSpendAmount: z.number(),
    automateBudgetAllocation: z.boolean(),
  })
  .partial();
export type Optimization = z.infer<typeof OptimizationSchema>;

export const BidSchema = z
  .object({
    baseBid: z.number(),
    maxAverageCPM: z.number(),
    currencyCode: z.string(),
  })
  .partial();
export type Bid = z.infer<typeof BidSchema>;

export const FrequencySchema = z.object({
  maxImpressions: z.number(),
  levelType: z.string(),
  frequencyType: z.string(),
  timeUnitCount: z.number(),
});
export type Frequency = z.infer<typeof FrequencySchema>;

export const AdGroupSchema = z.object({
  inventoryType: z.string().nullish(),
  fees: z.array(FeeSchema).nullish(),
  comments: z.string().nullish(),
  creativeRotationType: z.string().nullish(),
  endDateTime: z.coerce.date().nullish(),
  advertisedProductCategoryIds: z.array(z.string()).nullish(),
  adGroupId: z.string(),
  budgetCaps: z.array(BudgetCapSchema).nullish(),
  startDateTime: z.coerce.date().nullish(),
  pacing: PacingSchema.nullish(),
  targetingSettings: TargetingSettingsSchema.nullish(),
  budgetAmount: z.number().nullish(),
  optimization: OptimizationSchema.nullish(),
  purchaseOrderNumber: z.string().nullish(),
  name: z.string().nullish(),
  state: z.string().nullish(),
  bid: BidSchema.nullish(),
  digitalOutOfHomeProductLocations: z.array(z.any()).nullish(),
  frequencies: z.array(FrequencySchema).nullish(),
  campaignId: z.string().nullish(),
  lastUpdatedDateTime: z.coerce.date().nullish(),
  deliveryStatus: z.string().nullish(),
  creationDateTime: z.coerce.date().nullish(),
});
export type AdGroup = z.infer<typeof AdGroupSchema>;
