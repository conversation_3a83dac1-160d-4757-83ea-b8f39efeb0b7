export interface Advertiser {
  id: string;
  agencyId: string;
  agencyName: string;
  advertiserId: string;
  amazonAmcAccountId: string;
  amazonDspAccountId: string;
  amazonDspAdvertiserName: string;
  amazonDspProfileId: string;
  affinitySeedAudienceId: string;
  amcUploadAwsId: string;
  amcInstanceId: string;
  cleanroomResultBucket: string;
  amazonAdsApiUrl: string;
  currencyCode: string;
  countryCode: string;
  shopUrl?: string | null;
  shopifyDataSource: boolean;
  adTagDataSource: boolean;
  billingMode: string;
  amazonBillingBaseRateInCents: number;
  amazonBillingAudienceRateInCents: number;
  enabled: boolean;
  enabledForCampaignCreation: boolean;
  timezone: string;
}

export interface Agency {
  id: string;
  name: string;
  stripeId?: string | null;
}
