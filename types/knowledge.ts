export interface KnowledgeItemComponentProps<T extends KnowledgeType> {
  item: KnowledgeItem<T>;
  disabled?: boolean;
}

export interface KnowledgeItemComponentEmits<T extends KnowledgeType> {
  (e: "save", data: KnowledgeItem<T>): void;
  (e: "cancel", data: KnowledgeItem<T>): void;
}

export enum KnowledgeType {
  /// Agency Knowledge
  NAMING_SETTINGS = "NAMING_SETTINGS",
  LINE_ITEM_SETTINGS = "LINE_ITEM_SETTINGS",
  AGENCY_ADDITIONAL_INFO = "AGENCY_ADDITIONAL_INFO",
  /// Advertiser Knowledge
  KPI_SETTINGS = "KPI_SETTINGS",
  CURRENT_OBJECTIVES_SETTINGS = "CURRENT_OBJECTIVES_SETTINGS",
  TARGETING_SETTINGS = "TARGETING_SETTINGS",
  DSP_STRATEGY_SETTINGS = "DSP_STRATEGY_SETTINGS",
  ASIN_GROUPING_SETTINGS = "ASIN_GROUPING_SETTINGS",
  COMPETITORS_ASINS_SETTINGS = "COMPETITORS_ASINS_SETTINGS",
  BRAND_SAFETY_SETTINGS = "BRAND_SAFETY_SETTINGS",
  /// Global Knowledge
  FILE = "FILE",
}

export type AgencyKnowledge =
  | KnowledgeItem<KnowledgeType.NAMING_SETTINGS>
  | KnowledgeItem<KnowledgeType.TARGETING_SETTINGS>
  | KnowledgeItem<KnowledgeType.AGENCY_ADDITIONAL_INFO>
  | KnowledgeItem<KnowledgeType.LINE_ITEM_SETTINGS>;

export type AdvertiserKnowledge =
  | KnowledgeItem<KnowledgeType.KPI_SETTINGS>
  | KnowledgeItem<KnowledgeType.CURRENT_OBJECTIVES_SETTINGS>
  | KnowledgeItem<KnowledgeType.TARGETING_SETTINGS>
  | KnowledgeItem<KnowledgeType.DSP_STRATEGY_SETTINGS>
  | KnowledgeItem<KnowledgeType.ASIN_GROUPING_SETTINGS>
  | KnowledgeItem<KnowledgeType.COMPETITORS_ASINS_SETTINGS>
  | KnowledgeItem<KnowledgeType.BRAND_SAFETY_SETTINGS>;

export type ExtractKnowledgeType<T> = T extends KnowledgeItem<infer K>
  ? K
  : never;

export type AdvertiserKnowledgeTypes =
  ExtractKnowledgeType<AdvertiserKnowledge>;
export type AgencyKnowledgeTypes = ExtractKnowledgeType<AgencyKnowledge>;

export enum KnowledgeItemPayloadType {
  JSON = "JSON",
  FILE = "FILE",
}

export interface KnowledgeItem<T extends KnowledgeType> {
  id?: string;
  knowledgeType: T;
  description?: string;
  payload: KnowledgeItemPayload[T];
  displayName: string;
  vectorize?: boolean;
  updatedAt?: Date;
  ownerType: string;
  ownerId: string;
}

export interface KnowledgeItemPayload {
  [KnowledgeType.KPI_SETTINGS]: KpiSettings;
  [KnowledgeType.NAMING_SETTINGS]: NamingSettings;
  [KnowledgeType.TARGETING_SETTINGS]: TargetingSettings;
  [KnowledgeType.LINE_ITEM_SETTINGS]: LineItemSettings;
  [KnowledgeType.AGENCY_ADDITIONAL_INFO]: AgencyAdditionalInfo;
  [KnowledgeType.CURRENT_OBJECTIVES_SETTINGS]: CurrentObjectivesSettings;
  [KnowledgeType.DSP_STRATEGY_SETTINGS]: DspStrategySettings;
  [KnowledgeType.ASIN_GROUPING_SETTINGS]: AsinGroupingSettings;
  [KnowledgeType.COMPETITORS_ASINS_SETTINGS]: CompetitorsAsinsSettings;
  [KnowledgeType.BRAND_SAFETY_SETTINGS]: BrandSafetySettings;
  [KnowledgeType.FILE]: File;
}

interface KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType;
}

export interface KpiSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  funnelKpis: FunnelKpiKnowledge[];
}

export interface NamingSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  lineItemNameTemplate: string;
  orderNameTemplate: string;
}

export interface TargetingSettings extends KnowledgeItemPayloadBase {
  remarketing: string;
  retargeting: string;
  NTB: string;
}

export interface LineItemSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  configuration: string;
}

export interface AgencyAdditionalInfo extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  info: string;
}

export interface CurrentObjectivesSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  objectives: string;
}

export interface DspStrategySettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  strategy: string;
}

export interface AsinGroupingSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  groups: AsinGroup[];
}

export interface CompetitorsAsinsSettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  competitors: string[];
  groups: AsinGroup[];
}

export interface BrandSafetySettings extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.JSON;
  excludedDomains: string[];
}

export interface File extends KnowledgeItemPayloadBase {
  _type: KnowledgeItemPayloadType.FILE;
  fileId: string;
}

export interface FunnelKpiKnowledge {
  funnelName: string;
  kpis: KpiKnowledge[];
}

export interface KpiKnowledge {
  kpiName: string;
  importance: number;
}

export interface AsinGroup {
  name: string;
  asins: string[];
}
