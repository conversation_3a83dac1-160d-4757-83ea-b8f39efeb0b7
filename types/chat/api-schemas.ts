import { z } from "zod";

import { ChatParticipant } from "./common";
import { OwnableSchema } from "~/types/permissions";

// Core schemas

export const ReferencedResourceSchema = z.object({
  key: z.string(),
  displayName: z.string(),
  referenceId: z.string(),
});

export const MessagePayloadSchema = z.object({
  message: z.string(),
  referencedResources: z.array(ReferencedResourceSchema).default([]),
  additionalPayload: z
    .object({
      formConfig: z
        .object({
          fields: z.record(z.string(), z.any()),
        })
        .optional(),
    })
    .optional(),
});

export const MarkdownNodeSchema = z.object({
  kind: z.literal("markdown"),
  content: z.string(),
});

export const ContentNodeSchema = z.discriminatedUnion("kind", [
  MarkdownNodeSchema,
]);

export const PageNodeSchema = z.object({
  kind: z.literal("page"),
  title: z.string(),
  children: z.array(ContentNodeSchema).default([]),
});

export const ContainerNodeSchema = z.discriminatedUnion("kind", [
  PageNodeSchema,
]);

export const DocumentNodeSchema = z.union([
  ContainerNodeSchema,
  ContentNodeSchema,
]);

export const DocumentSchema = z.object({
  id: z.string(),
  instance: z.number(),
  version: z.number(),
  title: z.string().optional(),
  nodes: z.array(DocumentNodeSchema),
});

export const ConversationSchema = OwnableSchema.extend({
  id: z.string(),
  title: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  tags: z.array(z.string()).default([]),
});

// Linda API schemas

function transformParticipantToLinda(val: ChatParticipant) {
  return val === ChatParticipant.AI ? "LINDA" : "USER";
}

function transformParticipantToUser(val: string) {
  return val === "LINDA" ? ChatParticipant.AI : ChatParticipant.USER;
}

export const GenerateResponseRequestSchema = z.object({
  clientId: z.string(),
  conversationId: z.string(),
  messageId: z.string(),
  participant: z
    .nativeEnum(ChatParticipant)
    .transform(transformParticipantToLinda),
  payload: MessagePayloadSchema,
  checkpointId: z.string().optional(),
  timestamp: z.date().transform((val) => val.toISOString()),
});

export const GenerateStreamResponseResponseSchema = z.object({
  status: z.string(),
});

export const GenerateResponseResponseSchema = z.object({
  conversationId: z.string(),
  participant: z.string().transform(transformParticipantToUser),
  clientId: z.string(),
  payload: MessagePayloadSchema,
  timestamp: z.coerce.date(),
});

// Event schemas

export const TextPartEventSchema = z.object({
  type: z.literal("textPart"),
  content: z.string(),
});

export const DocumentPartEventSchema = z.object({
  type: z.literal("documentPart"),
  instance: z.number(),
  version: z.number(),
});

export const FilePartEventSchema = z.object({
  type: z.literal("filePart"),
  fileName: z.string(),
  fileSize: z.number(),
  fileType: z.string(),
  referenceableId: z.string(),
});

export const FormPartEventSchema = z.object({
  type: z.literal("formPart"),
  formId: z.string(),
  formType: z.string(),
  defaultValues: z.record(z.string(), z.any()),
});

export const ThoughtStepEventSchema = z.object({
  type: z.literal("thoughtStep"),
  title: z.string(),
  content: z.string().optional(),
});

export const ThoughtTextEventSchema = z.object({
  type: z.literal("thoughtText"),
  content: z.string(),
});

export const ThinkingDoneEventSchema = z.object({
  type: z.literal("thinkingDone"),
  thoughtFor: z.number(),
});

export const DocumentCreatedEventSchema = z.object({
  type: z.literal("documentCreated"),
  document: DocumentSchema,
});

export const MessageEndEventSchema = z.object({
  type: z.literal("messageEnd"),
});

export const PartEventSchema = z.discriminatedUnion("type", [
  TextPartEventSchema,
  DocumentPartEventSchema,
  FilePartEventSchema,
  FormPartEventSchema,
  ThoughtStepEventSchema,
  ThoughtTextEventSchema,
  ThinkingDoneEventSchema,
  DocumentCreatedEventSchema,
  MessageEndEventSchema,
]);

// Rob schemas

export const CreateConversationRequestSchema = OwnableSchema.extend({
  title: z.string().default("New Conversation"),
  tags: z.array(z.string()).default([]),
});

export const CreateConversationResponseSchema = ConversationSchema;

export const GetConversationByIdResponseSchema = ConversationSchema;

export const GetConversationsResponseSchema = z.array(ConversationSchema);

export const FormConfigSchema = z.object({
  formId: z.string(),
  formType: z.string(),
  formData: z.record(z.string(), z.any()),
  state: z.enum(["pending", "submitted"]).default("submitted"),
});

export const MessagePartSchema = z.discriminatedUnion("kind", [
  z.object({ kind: z.literal("text"), content: z.string() }),
  z.object({
    kind: z.literal("file"),
    fileName: z.string(),
    fileType: z.string(),
    referenceableId: z.string(),
  }),
  z.object({
    kind: z.literal("form"),
    formConfig: FormConfigSchema,
  }),
  z.object({
    kind: z.literal("document"),
    instance: z.number(),
    version: z.number(),
  }),
]);

export const ThoughtStepSchema = z.object({
  title: z.string().optional(),
  content: z.string().optional(),
});

export const TrinityMessageDataSchema = z.object({
  type: z.literal(ChatParticipant.AI).default(ChatParticipant.AI),
  parts: z.array(MessagePartSchema),
  thoughtSteps: z.array(ThoughtStepSchema),
});

export const NeoMessageDataSchema = z.object({
  type: z.literal(ChatParticipant.USER).default(ChatParticipant.USER),
  content: z.string(),
  formData: z.record(z.string(), z.any()),
  checkpointId: z.string().optional(),
});

export const MessageDataSchema = z.discriminatedUnion("type", [
  NeoMessageDataSchema,
  TrinityMessageDataSchema,
]);

export const MessageDtoSchema = z.object({
  id: z.string(),
  conversationId: z.string(),
  messageData: MessageDataSchema,
  timestamp: z.coerce.date(),
});

export const GetMessagesForConversationPagedResponseSchema = z.object({
  content: z.array(MessageDtoSchema),
  page: z.object({
    size: z.number(),
    number: z.number(),
    totalElements: z.number(),
    totalPages: z.number(),
  }),
});

export const AddMessagesToConversationRequestSchema =
  z.array(MessageDataSchema);

export const AddMessagesToConversationResponseSchema = z.array(
  z.object({
    messageId: z.string(),
  })
);
