import { z } from "zod";

import type {
  ConversationSchema,
  DocumentSchema,
  MessagePayloadSchema,
  NeoMessageDataSchema,
  TrinityMessageDataSchema,
} from "./api-schemas";

export type MessageStateExtension =
  | { state: "pending"; id: string }
  | { state: "persisted"; id: string }
  | { state: "error"; id: string; error?: { code: string; message: string } };

export type StatefulAiMessage = z.infer<typeof TrinityMessageDataSchema> &
  MessageStateExtension;

export type StatefulUserMessage = z.infer<typeof NeoMessageDataSchema> &
  MessageStateExtension;

export type StatefulMessage = StatefulUserMessage | StatefulAiMessage;

export type LocalConversation = z.infer<typeof ConversationSchema> & {
  messages: StatefulMessage[];
  documents: z.infer<typeof DocumentSchema>[] | undefined;
  totalMessages: number;
  pendingMessage?: z.infer<typeof MessagePayloadSchema>;
  isInitialized?: boolean;
  isNew?: boolean;
  activeCanvasComponentType?: Component;
  activeCanvasComponentProps?: Record<string, any>;
  activeCanvasComponentId?: string;
};
