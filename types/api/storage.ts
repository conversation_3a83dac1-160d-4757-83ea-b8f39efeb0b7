import { z } from "zod";
import { OwnableSchema } from "~/types/permissions";

export const FilePurposeSchema = z.enum([
  "GENERAL_PURPOSE",
  "DECK_TEMPLATE",
  "FUNNEL_CAMPAIGN_NAMING",
  "DECK",
]);

export const FileMetadataSchema = OwnableSchema.extend({
  id: z.string().uuid(),
  filePath: z.string(),
  fileName: z.string(),
  contentType: z.string(),
  fileSize: z.number(),
  purpose: FilePurposeSchema,
  updatedAt: z.coerce.date(),
});

export const GetFilesMetadataResponseSchema = z.array(FileMetadataSchema);

export type FilePurpose = z.infer<typeof FilePurposeSchema>;
export type FileMetadata = z.infer<typeof FileMetadataSchema>;
export type GetFilesMetadataResponse = z.infer<
  typeof GetFilesMetadataResponseSchema
>;
