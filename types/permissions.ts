import { z } from "zod";
import type { User } from "#auth-utils";
import type { Advertiser } from "~/types/advertising-entities";

export const RoleScopeSchema = z.enum(["PLATFORM", "AGENCY", "ADVERTISER", "USER"]);
export type RoleScope = z.infer<typeof RoleScopeSchema>;

export const RoleSchema = z.enum(["ADMIN", "EDITOR", "VIEWER"]);
export type Role = z.infer<typeof RoleSchema>;

export const OwnableSchema = z.object({
  ownerId: z.string(),
  ownerType: RoleScopeSchema,
});
export type Ownable = z.infer<typeof OwnableSchema>;

export type PageRoute = {
  path: string;
  query?: Record<string, any>;
};

export type ApiRequest = {
  path: string;
  method: string;
  event: any;
};

export type Permissions = {
  advertisers: {
    dataType: Advertiser;
    action: "list";
  };
  pages: {
    dataType: PageRoute;
    action: "view";
  };
  apis: {
    dataType: ApiRequest;
    action: "call";
  };
};

export type AuthenticatedPermissionCheck<Key extends keyof Permissions> =
  | boolean
  | ((
      scopeIds: string[],
      user: User,
      data?: Permissions[Key]["dataType"]
    ) => boolean);

export type AnonymousPermissionCheck<Key extends keyof Permissions> =
  | boolean
  | ((data?: Permissions[Key]["dataType"]) => boolean);

export type AuthenticatedRules = {
  [S in RoleScope]: Partial<{
    [R in Role]: Partial<{
      [Key in keyof Permissions]: Partial<{
        [Action in Permissions[Key]["action"]]: AuthenticatedPermissionCheck<Key>;
      }>;
    }>;
  }>;
};

export type AnonymousRules = Partial<{
  [Key in keyof Permissions]: Partial<{
    [Action in Permissions[Key]["action"]]: AnonymousPermissionCheck<Key>;
  }>;
}>;

export type AuthenticatedRulesParameter<
  RESOURCE_TYPE extends keyof Permissions
> = {
  [S in RoleScope]: Partial<{
    [R in Role]: Partial<{
      [Action in Permissions[RESOURCE_TYPE]["action"]]: AuthenticatedPermissionCheck<RESOURCE_TYPE>;
    }>;
  }>;
};

export type AnonymousRulesParameter<RESOURCE_TYPE extends keyof Permissions> =
  Partial<{
    [Action in Permissions[RESOURCE_TYPE]["action"]]: AnonymousPermissionCheck<RESOURCE_TYPE>;
  }>;
