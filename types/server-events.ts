import { z } from "zod";

export const ServerEventRequestSchema = z.object({
  topic: z.string(),
  eventName: z.string(),
  data: z.any(),
});

export const TopicSubscriptionSchema = z.object({
  topicLevel: z.enum(["USER", "ADVERTISER", "AGENCY"]),
  topicIdentifier: z.string(),
  clientId: z.string(),
});

export const TopicSubscriptionRequestSchema = z.array(TopicSubscriptionSchema);

export type TopicInfo = Omit<TopicSubscription, "clientId">;
export type ServerEventRequest = z.infer<typeof ServerEventRequestSchema>;
export type TopicSubscription = z.infer<typeof TopicSubscriptionSchema>;
export type TopicSubscriptionRequest = z.infer<
  typeof TopicSubscriptionRequestSchema
>;
