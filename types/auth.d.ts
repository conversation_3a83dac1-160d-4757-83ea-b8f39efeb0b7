import type { RoleScope } from "~/types/permissions";

// auth.d.ts
declare module "#auth-utils" {
  interface User {
    email: string;
    fullName: string;
    sub: string;
    roles: Record<RoleScope, Record<Role, string[]>>;
    exp: number;
    isGigiAdmin: boolean;
    platform: string;
  }

  interface SecureSessionData {
    accessToken: string;
    refreshToken: string;
    accessTokenExpiresAt: number;
  }
}

export {};
