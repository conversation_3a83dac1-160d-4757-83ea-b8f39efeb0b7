import {
  startOfWeek,
  subWeeks,
  subMonths,
  subYears,
  endOfWeek,
  startOfMonth,
  startOfYear,
} from "date-fns";

export const WEEK_CONFIG = {
  weekStartsOn: 1 as const,
};

export type PresetDate = {
  label: string;
  value: [Date, Date];
  style: {
    color: string;
    "font-family": string;
    "font-size": string;
    "font-style": string;
    "font-weight": string;
    "line-height": string;
    padding: string;
  };
};

export function buildPresetDate(
  label: string,
  start: Date,
  endDate: Date = new Date()
): PresetDate {
  const currentEnd = endOfWeek(subWeeks(endDate, 1), { weekStartsOn: 1 });
  return {
    label,
    value: [start, currentEnd],
    style: {
      color: "var(--primary-text, #171717)",
      "font-family": "Inter",
      "font-size": "0.875rem",
      "font-style": "normal",
      "font-weight": "400",
      "line-height": "150%",
      padding: "0.5rem 1rem",
    },
  };
}

export const DEFAULT_WEEK_RANGE_PRESETS: PresetDate[] = [
  buildPresetDate("Last week", subWeeks(new Date(), 1)),
  buildPresetDate("Last 2 weeks", subWeeks(new Date(), 2)),
  buildPresetDate("Last month", startOfMonth(subMonths(new Date(), 1))),
  buildPresetDate("Last 3 months", startOfMonth(subMonths(new Date(), 3))),
  buildPresetDate("Last 6 months", startOfMonth(subMonths(new Date(), 6))),
  buildPresetDate(
    "Last year",
    startOfWeek(startOfYear(subYears(new Date(), 1)), WEEK_CONFIG)
  ),
];
