// Campaign status mappings
export const CAMPAIGN_STATUS_MAPPING: Record<string, string> = {
  'DELIVERING': 'Delivering',
  'ADGROUPS_NOT_RUNNING': 'Upcoming', 
  'INACTIVE': 'Paused',
  'ENDED': 'Ended'
};

// Status options for dropdowns/filters
export const CAMPAIGN_STATUS_OPTIONS = [
  { 
    label: 'Delivering', 
    value: 'DELIVERING', 
    description: 'Campaigns that are currently active and delivering' 
  },
  { 
    label: 'Upcoming', 
    value: 'ADGROUPS_NOT_RUNNING', 
    description: 'Campaigns scheduled to start in the future' 
  },
  { 
    label: 'Paused', 
    value: 'INACTIVE', 
    description: 'Campaigns that have been temporarily stopped' 
  },
  { 
    label: 'Ended', 
    value: 'ENDED', 
    description: 'Campaigns that have completed their run' 
  }
];

export const FUNNEL_TACTIC_OPTIONS = [
  { label: 'STV', value: 'STV', description: 'Streaming TV campaigns' },
  { label: 'TOF', value: 'TOF', description: 'Top of Funnel campaigns' },
  { label: 'MOF', value: 'MOF', description: 'Middle of Funnel campaigns' },
  { label: 'BOF', value: 'BOF', description: 'Bottom of Funnel campaigns' }
];

// Helper function to convert amazon status to user friendly status
export const getStatusText = (status: string): string => {
  return CAMPAIGN_STATUS_MAPPING[status.toUpperCase()] || 
         status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
}; 