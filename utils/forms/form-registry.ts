import type { FormConfiguration } from "./form-configuration";

export class FormRegistry {
  private static registry: Map<string, FormConfiguration> = new Map();

  static register(formConfiguration: FormConfiguration) {
    this.registry.set(formConfiguration.formId, formConfiguration);
  }

  static get(formId: string): FormConfiguration | undefined {
    return this.registry.get(formId);
  }

  static getAll(): FormConfiguration[] {
    return Array.from(this.registry.values());
  }
}
