import type { z } from "zod";
import type { FormConfigSchema } from "~/types/chat/api-schemas";

export class FormConfiguration<LocalFormData = Record<string, any>> {
  constructor(
    public readonly formId: string,
    public readonly title: string,
    public readonly icon: string,
    public readonly submitMessage: string,
    public readonly component: Component
  ) {}

  toLocal(formConfig: z.infer<typeof FormConfigSchema>): LocalFormData {
    throw new Error("Not implemented");
  }

  fromLocal(
    formConfig: z.infer<typeof FormConfigSchema>,
    data: LocalFormData
  ): z.infer<typeof FormConfigSchema>["formData"] {
    throw new Error("Not implemented");
  }
}
