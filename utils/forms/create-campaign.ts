import type { z } from "zod";
import { addMonths, endOfMonth, format, startOfMonth } from "date-fns";

import type { FormConfigSchema } from "~/types/chat/api-schemas";
import { FormConfiguration } from "./form-configuration";
import CreateCampaignForm from "~/components/forms/CreateCampaignForm.vue";
import type { Flight } from "~/types/amazon";

export type CreateCampaignFormData = {
  campaignType: string;
  goal: string;
  kpi: string;
  kpiTarget: string;
  flights: {
    budgetAmount: number;
    startDateTime: string;
    endDateTime: string;
  }[];
  additionalContext: string;
};

export class CreateCampaignFormConfiguration extends FormConfiguration<CreateCampaignFormData> {
  constructor() {
    super(
      "campaign_order_form",
      "New Order Form",
      "content_copy",
      "Approve and build preview",
      CreateCampaignForm
    );
  }

  override toLocal(formConfig: z.infer<typeof FormConfigSchema>) {
    const formData = formConfig.formData;

    const makeFlight = (budgetAmount: number, monthOffset: number) => ({
      budgetAmount: budgetAmount,
      startDateTime: format(
        startOfMonth(addMonths(new Date(), monthOffset)),
        "yyyy-MM-dd"
      ),
      endDateTime: format(
        endOfMonth(addMonths(new Date(), monthOffset)),
        "yyyy-MM-dd"
      ),
    });

    const flights = formData.fields.flights.flight_details.map((f: any) => ({
      budgetAmount: f.budget,
      startDateTime: f.start_date,
      endDateTime: f.end_date,
    }));

    return {
      campaignType: formData?.fields?.campaign_type || "",
      goal: formData?.fields?.goal || "",
      kpi: formData?.fields?.kpi || "",
      kpiTarget: formData?.fields?.kpi_target || "",
      flights: flights,
      additionalContext: formData?.fields?.additional_context || "",
    };
  }

  override fromLocal(
    formConfig: z.infer<typeof FormConfigSchema>,
    data: CreateCampaignFormData
  ) {
    return {
      ...formConfig.formData,
      fields: {
        campaign_type: data.campaignType,
        goal: data.goal,
        kpi: data.kpi,
        kpi_target: data.kpiTarget,
        flights: {
          flight_details: data.flights.map((f) => ({
            budget: f.budgetAmount,
            start_date: f.startDateTime,
            end_date: f.endDateTime,
          })),
        },
        additional_context: data.additionalContext,
      },
    };
  }
}
