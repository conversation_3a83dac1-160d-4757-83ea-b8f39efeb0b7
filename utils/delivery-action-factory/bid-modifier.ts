import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";
import { BidModifierSuggestionSchema } from "~/types/delivery-item/suggestion/bid-modifier";
import { CreateBidModifierActionSchema } from "~/types/delivery-item/action/create-bid-modifier";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import type { DeliveryItemActionExecutionRequest } from "~/types/api/delivery-item";

const factory: ActionFactoryFunction = (item) => {
    console.info(`Preparing bid modifier action for item ${item.id}`);

    const payload = getSuggestionPayload(item, BidModifierSuggestionSchema);

    const modifierRules = payload.line_items
        ?.map(lineItem => {
            const selectedTerms = lineItem.bid_modifier_content.ruleExpression.terms.filter(term => {
                return term.selected;
            });

            if (selectedTerms.length === 0) {
                return null;
            }

            const reasoning = payload.reason?.find(r => r.line_item_id === lineItem.line_item_id);

            const selectedTermsForRule = selectedTerms.map(term => ({
                deviceType: term.deviceType || undefined,
                operatingSystem: term.operatingSystem || undefined,
                region: term.region || undefined,
                domain: term.domain || undefined,
                bidAdjustment: term.bidAdjustment,
                behavioralSegment: term.behavioralSegment || undefined,
                adFormat: term.adFormat || ["DISPLAY"],
                ...Object.fromEntries(
                    Object.entries(term).filter(([key]) =>
                        !['deviceType', 'operatingSystem', 'region', 'domain', 'bidAdjustment', 'behavioralSegment', 'adFormat', 'selected'].includes(key)
                    )
                )
            }));

            let reasoningText = '';
            if (reasoning?.term_reasoning) {
                reasoningText = selectedTerms.map(selectedTerm => {
                    const termReasoning = reasoning.term_reasoning.find(tr => {
                        return tr.term.bidAdjustment === selectedTerm.bidAdjustment &&
                            JSON.stringify(tr.term.deviceType) === JSON.stringify(selectedTerm.deviceType) &&
                            JSON.stringify(tr.term.operatingSystem) === JSON.stringify(selectedTerm.operatingSystem) &&
                            JSON.stringify(tr.term.region) === JSON.stringify(selectedTerm.region);
                    });

                    const dimensions = formatDimensions(selectedTerm);
                    const reason = termReasoning?.reason || 'Performance-based adjustment';
                    return `${dimensions}: ${reason}`;
                }).join('\n\n');
            } else {
                reasoningText = `${selectedTerms.length} bid modifier terms selected for optimization`;
            }

            return {
                adGroupId: lineItem.line_item_id,
                ruleRequestContent: {
                    ruleExpression: {
                        terms: selectedTermsForRule,
                        onMultipleMatches: lineItem.bid_modifier_content.ruleExpression.onMultipleMatches
                    },
                    ruleDescription: lineItem.bid_modifier_content.ruleDescription || 'rule based on performance data'
                },
                reasoning: reasoningText,
            };
        })
        .filter(rule => rule !== null) || [];

    if (modifierRules.length === 0) {
        throw new Error("No bid modifier terms selected");
    }

    const action = CreateBidModifierActionSchema.parse({
        actionType: ActionTypeSchema.Enum.BID_MODIFIER,
        payload: {
            type: "AMAZON_BID_MODIFIER",
            advertiserId: payload.advertiser_id,
            campaignId: payload.order_id,
            modifierRules,
        },
    });

    return [action] as unknown as DeliveryItemActionExecutionRequest;
};

function formatDimensions(term: any): string {
    const dimensions: string[] = [];

    if (term.deviceType?.length) dimensions.push(`Device: ${term.deviceType.join(', ')}`);
    if (term.operatingSystem?.length) dimensions.push(`OS: ${term.operatingSystem.join(', ')}`);
    if (term.region?.length) dimensions.push(`Region: ${term.region.join(', ')}`);
    if (term.domain) dimensions.push(`Domain: ${term.domain}`);
    if (term.behavioralSegment?.length) dimensions.push(`Segments: ${term.behavioralSegment.join(', ')}`);

    return dimensions.join(', ') || 'Multiple dimensions';
}

export default factory; 