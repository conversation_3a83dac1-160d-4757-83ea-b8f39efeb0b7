import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import type { DeliveryItemActionExecutionRequest } from "~/types/api/delivery-item";

const factory: ActionFactoryFunction = (item) => {
    const selectedIds: string[] = (item as any).selectedRecommendationIds || [];
    const adGroups: any[] = [];
    const advertiserId =
        (item as any).advertiserId ||
        (item as any).ownerId;

    if (!advertiserId) {
        throw new Error("advertiserId is missing in ECPM spike monitor action payload");
    }

    (item.actionSuggestions || []).forEach((suggestion) => {
        const payload = suggestion.payload as any;
        if (!payload) return;
        const orderName = payload.order_name || 'Untitled Order';
        const splikeLineItems = payload.splike_lineItems || [];
        splikeLineItems.forEach((lineItem: any, lineIdx: number) => {
            const updatePayload = lineItem.recommendation?.update_payload || [];
            updatePayload.forEach((recommendation: any, recIdx: number) => {
                const uniqueId = `${orderName}__${lineItem.line_item_id}__${lineIdx}__${recIdx}`;
                if (selectedIds.length === 0 || selectedIds.includes(uniqueId)) {
                    adGroups.push(recommendation.payload);
                }
            });
        });
    });

    if (adGroups.length === 0) {
        throw new Error("No adGroups selected for ECPM spike monitor action");
    }
    const result = {
        actionType: ActionTypeSchema.Enum.PATCH_AD_GROUP,
        payload: {
            adGroups,
            advertiserId
        }
    };
    console.log('ecpm-spike action payload:', result);
    return [result] as DeliveryItemActionExecutionRequest;
};

export default factory; 