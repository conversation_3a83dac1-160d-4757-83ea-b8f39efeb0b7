import { AcknowledgeActionSchema } from "~/types/delivery-item/action/acknowledge";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";

const factory: ActionFactoryFunction = () => {
  const action = AcknowledgeActionSchema.parse({
    actionType: ActionTypeSchema.Enum.ACKNOWLEDGE,
  });

  return [action];
};

export default factory;
