import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";

import {
  UnderPacingSuggestionSchema,
  type UnderPacingSuggestion,
} from "~/types/delivery-item/suggestion/under-pacing";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import type { AmazonQuickAction } from "~/types/delivery-item/action/amazon-quick-action";
import type { PatchAdGroupAction } from "~/types/delivery-item/action/patch-ad-group";
import type { DeliveryItemActionExecutionRequest } from "~/types/api/delivery-item";

const factory: ActionFactoryFunction = (item) => {
  console.info(`Preparing under-pacing action for item ${item.id}`);

  let payload = getSuggestionPayload(item, UnderPacingSuggestionSchema);

  const actions = [];
  if (payload.adGroupsMinimumSpend && payload.adGroupsMinimumSpend.length > 0) {
    actions.push(createPatchAdGroupRequest(payload, item.id));
  }

  if (payload.guidance && payload.guidance.length > 0) {
    actions.push(createAmazonQuickActionRequest(payload, item.id));
  }

  return actions as DeliveryItemActionExecutionRequest;
};

type MarketplaceActionIdsTuple = [
  marketplaceId: string,
  actions: { selected: boolean; actionId: string }[]
];

function createAmazonQuickActionRequest(
  payload: UnderPacingSuggestion["payload"],
  itemId: string
): AmazonQuickAction {
  const marketplaceActionIds = defaultDict<string[]>(() => []);

  let totalActions = 0;
  let totalActionsSelected = 0;

  payload.guidance
    ?.flatMap((g) => g.recommendations)
    .filter((r) => r !== undefined)
    .map(
      (r) =>
        [
          r.marketplaceId,
          r.quickactionsData.currentActions,
        ] as MarketplaceActionIdsTuple
    )
    .forEach(([marketplaceId, actions]) => {
      totalActions += actions.length;

      actions
        .filter((a) => a.selected)
        .map((a) => a.actionId)
        .forEach((actionId) => {
          // marketplaces might be shared between different recommendations so pushing instead of setting with a new array
          marketplaceActionIds[marketplaceId].push(actionId);
          totalActionsSelected++;
        });
    });

  if (
    Object.keys(marketplaceActionIds).length === 0 ||
    Object.values(marketplaceActionIds).every((a) => a.length === 0)
  ) {
    throw new Error("No action IDs found in payload");
  }

  console.info(
    `A total of ${totalActionsSelected} out of ${totalActions} Amazon quick actions will be submitted for item ${itemId}`
  );

  return {
    actionType: ActionTypeSchema.Enum.AMAZON_QUICK_ACTION,
    payload: {
      advertiserId: payload.advertiserId,
      marketplaceActionIds,
    },
  };
}

function createPatchAdGroupRequest(
  payload: UnderPacingSuggestion["payload"],
  itemId: string
): PatchAdGroupAction {
  const adGroups = payload.adGroupsMinimumSpend
    ?.filter((a) => a.selected)
    .map((a) => ({
      adGroupId: a.adGroupId,
      optimization: {
        dailyMinSpendAmount: a.recommendedMinimumSpend,
      },
    }));

  if (!adGroups || adGroups.length === 0) {
    throw new Error("No ad groups found in payload");
  }

  console.info(
    `A total of ${adGroups.length} out of ${payload.adGroupsMinimumSpend?.length} ad groups will be patched for item ${itemId}`
  );

  return {
    actionType: ActionTypeSchema.Enum.PATCH_AD_GROUP,
    payload: {
      advertiserId: payload.advertiserId,
      adGroups,
    },
  };
}
export default factory;
