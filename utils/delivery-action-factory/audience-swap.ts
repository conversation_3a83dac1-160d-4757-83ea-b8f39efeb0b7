import { z } from "zod";

import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import {
  AudienceSwapSuggestionSchema,
  AudienceSwapDataSchema,
} from "~/types/delivery-item/suggestion/audience-swap";
import { MutateAdGroupTargetsActionSchema } from "~/types/delivery-item/action/mutate-ad-group-targets";
import {
  AudienceTargetTypeSchema,
  TargetSchema,
  type Target,
} from "~/types/amazon/targets";

const factory: ActionFactoryFunction = (item) => {
  console.info(`Preparing audience swap action for item ${item.id}`);

  const payload = getSuggestionPayload(item, AudienceSwapSuggestionSchema);

  const createTargets = [] as Target[];
  const deleteTargetIds = [] as string[];

  payload.changes
    .flatMap((change) => change.changes)
    .filter((change) => change.selected)
    .forEach((change) => {
      createTargets.push(buildCreateTarget(change));
      deleteTargetIds.push(change.fromTargetId);
    });

  console.info(
    `A total of ${createTargets.length} out of ${payload.changes.length} changes were selected for item ${item.id}`
  );

  const action = MutateAdGroupTargetsActionSchema.parse({
    actionType: ActionTypeSchema.Enum.MUTATE_TARGETS,
    payload: {
      advertiserId: item.ownerId,
      createTargets,
      deleteTargetIds,
    },
  });

  return [action];
};

function buildCreateTarget(
  change: z.infer<typeof AudienceSwapDataSchema>
): Target {
  const result = TargetSchema.safeParse({
    adGroupId: change.adGroupId,
    targetDetails: {
      audienceTarget: {
        targetType: AudienceTargetTypeSchema.Enum.AUDIENCE,
        audienceId: change.toAudienceId,
        groupId: change.groupId,
      },
    },
  });

  if (!result.success) {
    console.error(result.error);
    throw new Error("Invalid target, please contact support.");
  }

  return result.data;
}

export default factory;
