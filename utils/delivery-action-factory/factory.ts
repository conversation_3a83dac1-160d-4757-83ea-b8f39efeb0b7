import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import {
  SuggestionTypeSchema,
  type SuggestionType,
} from "~/types/delivery-item/suggestion/suggestion-type";

import bidChangeFactory from "~/utils/delivery-action-factory/bid-change";
import bidModifierFactory from "~/utils/delivery-action-factory/bid-modifier";
import extendCampaignFactory from "~/utils/delivery-action-factory/extend-campaign";
import audienceSwapFactory from "~/utils/delivery-action-factory/audience-swap";
import underPacingFactory from "~/utils/delivery-action-factory/under-pacing";
import acknowledgeFactory from "~/utils/delivery-action-factory/acknowledge";
import ecpmSpikeFactory from "~/utils/delivery-action-factory/ecpm-spike";

import type { DeliveryItemActionExecutionRequest } from "~/types/api/delivery-item";

type RegistryKey = keyof typeof SuggestionTypeSchema.Enum;
export type ActionFactoryFunction = (
  item: DeliveryItem
) => DeliveryItemActionExecutionRequest;

const factoryRegistry: Partial<Record<RegistryKey, ActionFactoryFunction>> = {};

function registerFactory(
  suggestionType: SuggestionType,
  factory: ActionFactoryFunction
) {
  const key = suggestionType;
  if (factoryRegistry[key]) {
    console.warn(`Factory for key "${key}" already registered. Overwriting.`);
  }
  factoryRegistry[key] = factory;
  console.log(`Registered factory for: ${key}`);
}

export function createActionPayload(
  item: DeliveryItem,
  suggestionType: SuggestionType
) {
  const key = suggestionType;
  const factory = factoryRegistry[key];
  if (!factory) {
    throw new Error(`No factory found for key "${key}"`);
  }

  return factory(item);
}

registerFactory(SuggestionTypeSchema.Enum.PATCH_AD_GROUP, bidChangeFactory);
registerFactory(SuggestionTypeSchema.Enum.FLIGHT_MODIFY, extendCampaignFactory);
registerFactory(SuggestionTypeSchema.Enum.MUTATE_TARGETS, audienceSwapFactory);
registerFactory(SuggestionTypeSchema.Enum.UNDER_PACING, underPacingFactory);
registerFactory(SuggestionTypeSchema.Enum.ACKNOWLEDGE, acknowledgeFactory);
registerFactory(SuggestionTypeSchema.Enum.ECPM_SPIKE_MONITOR, ecpmSpikeFactory);
registerFactory(SuggestionTypeSchema.Enum.BID_MODIFIER, bidModifierFactory);
