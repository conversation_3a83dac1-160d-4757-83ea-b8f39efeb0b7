import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";
import { BidChangeSuggestionSchema } from "~/types/delivery-item/suggestion/bid-change";
import { PatchAdGroupActionSchema } from "~/types/delivery-item/action/patch-ad-group";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";

const factory: ActionFactoryFunction = (item) => {
  console.info(`Preparing bid change action for item ${item.id}`);

  const payload = getSuggestionPayload(item, BidChangeSuggestionSchema);

  const adGroups = payload.changes
    .flatMap((change) => change.adGroups)
    .filter((adGroup) => adGroup.selected)
    .map((adGroup) => adGroup.next);

  console.info(
    `A total of ${adGroups.length} out of ${payload.changes.length} changes were selected for item ${item.id}`
  );

  if (adGroups.length === 0) {
    throw new Error("No ad groups selected");
  }

  const action = PatchAdGroupActionSchema.parse({
    actionType: ActionTypeSchema.Enum.PATCH_AD_GROUP,
    payload: {
      advertiserId: item.ownerId,
      adGroups,
    },
  });

  return [action];
};

export default factory;
