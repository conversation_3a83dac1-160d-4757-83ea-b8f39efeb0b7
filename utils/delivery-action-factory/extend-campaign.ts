import type { Flight } from "~/types/amazon/flight";
import { ActionTypeSchema } from "~/types/delivery-item/action/action-type";
import { ExtendCampaignSuggestionSchema } from "~/types/delivery-item/suggestion/extend-campaign";
import { ModifyFlightActionSchema } from "~/types/delivery-item/action/modify-flight";
import type { ActionFactoryFunction } from "~/utils/delivery-action-factory/factory";

const factory: ActionFactoryFunction = (item) => {
  console.info(`Preparing extend campaign action for item ${item.id}`);

  const payload = getSuggestionPayload(item, ExtendCampaignSuggestionSchema);

  const campaignId = payload.changes[0].campaignId;
  const newFlights = payload.changes
    .flatMap((change) => change.flights)
    .filter((flight) => !flight.flightId)
    .map(
      (flight) =>
        ({
          startDateTime: flight.startDateTime,
          endDateTime: flight.endDateTime,
          budgetAmount: flight.budgetAmount,
        } as Required<Omit<Flight, "flightId" | "currencyCode">>)
    )
    .sort((a, b) => a.startDateTime!.getTime() - b.startDateTime!.getTime()); // payload must be sorted

  if (!newFlights.length) {
    throw new Error("No flights to create");
  }

  const flightsHaveRequiredFields = newFlights.every(
    (flight) =>
      flight.startDateTime && flight.endDateTime && flight.budgetAmount
  );

  if (!flightsHaveRequiredFields) {
    throw new Error("Flight data missing required fields");
  }

  const noOverlappingFlights = newFlights.every((flight, index) => {
    const nextFlight = newFlights[index + 1];
    return (
      !nextFlight ||
      flight.endDateTime!.getTime() <= nextFlight.startDateTime!.getTime()
    );
  });

  if (!noOverlappingFlights) {
    throw new Error("Flights have overlapping dates");
  }

  console.info(
    `A total of ${newFlights.length} new flights will be created for item ${item.id}`
  );

  const action = ModifyFlightActionSchema.parse({
    actionType: ActionTypeSchema.Enum.FLIGHT_MODIFY,
    payload: {
      advertiserId: item.ownerId,
      campaignId,
      newFlights,
    },
  });

  return [action];
};

export default factory;
