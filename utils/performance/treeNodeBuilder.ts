import type { TreeNode } from "primevue/treenode";
import type { CreativeData, LineItemData } from "./dataTransformer";
import { MetricFormatter } from "./formatters";

export class TreeNodeBuilder {
  static createCreativeTreeNode(
    creative: CreativeData,
    lineItemId: string,
    creativeNames: Record<string, string>,
    currencyCode?: string
  ): TreeNode {
    const creativeMetrics = MetricFormatter.formatMetricsRecord(
      creative.metrics,
      currencyCode
    );

    return {
      key: `${lineItemId}-${creative.creativeId}`,
      label: creativeNames[creative.creativeId],
      data: {
        name: creativeNames[creative.creativeId],
        ...creativeMetrics,
      },
    };
  }

  static createLineItemTreeNode(
    lineItemId: string,
    creatives: CreativeData[],
    lineItemMetrics: Record<string, any>,
    lineItemNames: Record<string, string>,
    creativeNames: Record<string, string>,
    currencyCode?: string
  ): TreeNode {
    const formattedMetrics = MetricFormatter.formatMetricsRecord(
      lineItemMetrics,
      currencyCode
    );

    // Filter out unknown creatives
    const knownCreatives = creatives.filter(
      (creative) => creativeNames[creative.creativeId]
    );

    return {
      key: lineItemId,
      label: lineItemNames[lineItemId],
      data: {
        name: lineItemNames[lineItemId],
        ...formattedMetrics,
      },
      children: knownCreatives.map((creative) =>
        this.createCreativeTreeNode(
          creative,
          lineItemId,
          creativeNames,
          currencyCode
        )
      ),
    };
  }

  static createLineItemOnlyNodes(
    lineItemMetricsData: Map<string, LineItemData>,
    lineItemNames: Record<string, string>,
    currencyCode?: string
  ): TreeNode[] {
    return Array.from(lineItemMetricsData.entries())
      .filter(([lineItemId]) => lineItemNames[lineItemId])
      .map(([lineItemId, lineItemData]) => {
        const formattedMetrics = MetricFormatter.formatMetricsRecord(
          lineItemData.metrics,
          currencyCode
        );

        return {
          key: lineItemId,
          label: lineItemNames[lineItemId],
          data: {
            name: lineItemNames[lineItemId],
            ...formattedMetrics,
          },
        };
      });
  }

  static createCreativeOnlyNodes(
    creativesByLineItem: Map<string, CreativeData[]>,
    creativeNames: Record<string, string>,
    currencyCode?: string
  ): TreeNode[] {
    const allCreatives: TreeNode[] = [];

    creativesByLineItem.forEach((creatives, lineItemId) => {
      creatives
        .filter((creative) => creativeNames[creative.creativeId])
        .forEach((creative) => {
          const creativeMetrics = MetricFormatter.formatMetricsRecord(
            creative.metrics,
            currencyCode
          );

          allCreatives.push({
            key: creative.creativeId,
            label: creativeNames[creative.creativeId],
            data: {
              name: creativeNames[creative.creativeId],
              ...creativeMetrics,
            },
          });
        });
    });

    return allCreatives;
  }

  static createHierarchicalNodes(
    creativesByLineItem: Map<string, CreativeData[]>,
    lineItemMetricsData: Map<string, LineItemData>,
    lineItemNames: Record<string, string>,
    creativeNames: Record<string, string>,
    currencyCode?: string
  ): TreeNode[] {
    return Array.from(creativesByLineItem.entries())
      .filter(([lineItemId]) => lineItemNames[lineItemId])
      .map(([lineItemId, creatives]) => {
        const knownCreatives = creatives.filter(
          (creative) => creativeNames[creative.creativeId]
        );

        const lineItemData = lineItemMetricsData.get(lineItemId);
        if (knownCreatives.length === 0 && !lineItemData) {
          return null;
        }

        return this.createLineItemTreeNode(
          lineItemId,
          knownCreatives,
          lineItemData!.metrics,
          lineItemNames,
          creativeNames,
          currencyCode
        );
      })
      .filter((node): node is TreeNode => node !== null);
  }
}
