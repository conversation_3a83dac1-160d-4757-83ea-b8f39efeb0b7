import { ADSP_CAMPAIGN_METRIC_DEFINITIONS } from "~/utils/advertiser-datasets/metrics/constants";
import { formatCurrency } from "~/utils/currencyUtils";

export class MetricFormatter {
  static formatValue(
    metricName: string,
    value: any,
    currencyCode?: string,
    compact?: boolean
  ): string {
    const metricDefinition =
      ADSP_CAMPAIGN_METRIC_DEFINITIONS[
        metricName as keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS
      ];
    const valueType = metricDefinition?.valueType || "numeric";

    if (value === undefined || value === null || value === "") {
      return "N/A";
    }

    const numericValue = typeof value === "number" ? value : parseFloat(value);

    if (isNaN(numericValue)) {
      return "N/A";
    }

    switch (valueType) {
      case "currency":
        return this.formatCurrencyValue(numericValue, currencyCode, compact);
      case "percentage":
        return this.formatPercentageValue(numericValue, compact);
      case "numeric":
      default:
        return this.formatNumericValue(numericValue, compact);
    }
  }

  static formatCurrencyValue(
    value: number,
    currencyCode?: string,
    compact?: boolean
  ): string {
    return formatCurrency(value, currencyCode, { compact }) || "N/A";
  }

  static formatPercentageValue(value: number, compact?: boolean): string {
    return new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 2,
      maximumFractionDigits: 4,
      notation: compact ? "compact" : "standard",
    }).format(value / 100);
  }

  static formatNumericValue(value: number, compact?: boolean): string {
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 4,
      notation: compact ? "compact" : "standard",
    }).format(value);
  }

  static formatMetricsRecord(
    metrics: Record<string, any>,
    currencyCode?: string
  ): Record<string, string> {
    const formattedMetrics: Record<string, string> = {};
    Object.entries(metrics).forEach(([key, value]) => {
      formattedMetrics[key] = this.formatValue(key, value, currencyCode);
    });
    return formattedMetrics;
  }
}
