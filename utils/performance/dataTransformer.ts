import type {
  PerLineItemCreativeMetrics,
  PerLineItemMetrics,
} from "~/utils/advertiser-datasets/metrics/types";

export type CreativeData = {
  creativeId: string;
  metrics: Record<string, any>;
};

export type LineItemData = {
  lineItemId: string;
  metrics: Record<string, any>;
};

export class PerformanceDataTransformer {
  static collectCreativesByLineItem(
    metricsData: Record<string, PerLineItemCreativeMetrics | null>
  ): Map<string, CreativeData[]> {
    const lineItemsMap = new Map<string, CreativeData[]>();

    Object.entries(metricsData).forEach(([metricName, metricData]) => {
      if (!metricData) return;

      Object.entries(metricData).forEach(([campaignId, campaignData]) => {
        Object.entries(campaignData)
          .filter(([lineItemId]) => lineItemId.length > 16)
          .forEach(([lineItemId, lineItemData]) => {
            if (!lineItemsMap.has(lineItemId)) {
              lineItemsMap.set(lineItemId, []);
            }

            Object.entries(lineItemData).forEach(
              ([creativeId, creativeData]) => {
                const creatives = lineItemsMap.get(lineItemId)!;
                let creative = creatives.find(
                  (c) => c.creativeId === creativeId
                );

                if (!creative) {
                  creative = {
                    creativeId,
                    metrics: {},
                  };
                  creatives.push(creative);
                }

                // Add all metrics from this creative
                Object.entries(creativeData).forEach(([metric, value]) => {
                  creative!.metrics[metric] = value;
                });
              }
            );
          });
      });
    });

    return lineItemsMap;
  }

  static collectLineItemMetrics(
    metricsData: Record<string, PerLineItemMetrics | null>
  ): Map<string, LineItemData> {
    const lineItemsMap = new Map<string, LineItemData>();

    Object.entries(metricsData).forEach(([requestedMetricName, metricData]) => {
      if (!metricData) return;

      Object.entries(metricData).forEach(([campaignId, campaignData]) => {
        Object.entries(campaignData).forEach(
          ([lineItemId, lineItemMetricValues]) => {
            let lineItem = lineItemsMap.get(lineItemId);

            if (!lineItem) {
              lineItem = {
                lineItemId,
                metrics: {},
              };
              lineItemsMap.set(lineItemId, lineItem);
            }

            Object.entries(lineItemMetricValues).forEach(([metric, value]) => {
              lineItem!.metrics[metric] = value;
            });
          }
        );
      });
    });

    return lineItemsMap;
  }

  static aggregateLineItemMetrics(
    creatives: CreativeData[],
    selectedMetrics: string[]
  ): Record<string, string> {
    const aggregatedMetrics: Record<string, string> = {};

    selectedMetrics.forEach((metricName) => {
      const values = creatives
        .map((c) => c.metrics[metricName])
        .filter((val) => typeof val === "number" && !isNaN(val));

      if (values.length > 0) {
        const sum = values.reduce((sum, val) => sum + val, 0);
        aggregatedMetrics[metricName] = sum.toString();
      } else {
        aggregatedMetrics[metricName] = "0";
      }
    });

    return aggregatedMetrics;
  }
}
