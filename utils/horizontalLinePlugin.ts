import { Chart } from "chart.js";

export interface HorizontalLineConfig {
  value: number;
  color?: string;
  label?: string;
  lineWidth?: number;
  lineDash?: number[];
  labelBackgroundColor?: string;
  labelBorderColor?: string;
  labelTextColor?: string;
  labelFont?: string;
  labelPadding?: number;
  formatValue?: (value: number) => string;
  autoAdjustScale?: boolean;
  scalePadding?: number;
}

export function createHorizontalLinePlugin(config: HorizontalLineConfig) {
  const {
    value,
    color = "rgba(54, 162, 235, 0.8)",
    label,
    lineWidth = 2,
    lineDash = [5, 5],
    labelBackgroundColor = "rgba(255, 255, 255, 0.9)",
    labelBorderColor = "rgba(54, 162, 235, 0.3)",
    labelTextColor = "rgba(54, 162, 235, 0.9)",
    labelFont = "bold 12px Inter",
    labelPadding = 8,
    formatValue = (val: number) => val.toString(),
    autoAdjustScale = true,
    scalePadding = 0.1, // 10% padding by default
  } = config;

  return {
    id: `horizontalLine-${Math.random().toString(36).substr(2, 9)}`, // Unique ID

    afterDataLimits: (chart: any) => {
      if (!autoAdjustScale) return;

      // Get all y-axis scales
      const yScales = Object.values(chart.scales).filter(
        (scale: any) => scale.axis === "y"
      );

      for (const yScale of yScales) {
        const scale = yScale as any;

        // After Chart.js calculates data limits, ensure our horizontal line value is included
        const currentMin = scale.min;
        const currentMax = scale.max;

        const paddingAmount = Math.abs(value * scalePadding); // Calculate absolute padding

        // Extend the scale to include our horizontal line value with padding
        if (value < currentMin) {
          scale.min = value - paddingAmount;
        }
        if (value > currentMax) {
          scale.max = value + paddingAmount;
        }
      }
    },

    afterDraw: (chart: any) => {
      const { ctx, chartArea, scales } = chart;
      const { top, bottom, left, right } = chartArea;

      // Get the pixel position for the value on the y-axis
      const yValue = scales.y.getPixelForValue(value);

      // Only draw if the line would be visible within the chart area
      if (yValue >= top && yValue <= bottom) {
        ctx.save();

        // Draw the horizontal line
        ctx.beginPath();
        ctx.moveTo(left, yValue);
        ctx.lineTo(right, yValue);
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.setLineDash(lineDash);
        ctx.stroke();

        // Add label if provided
        if (label) {
          ctx.fillStyle = labelTextColor;
          ctx.font = labelFont;
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";

          const labelText = `${label}: ${formatValue(value)}`;
          const labelY = yValue - labelPadding;

          // Draw a small background for the label for better readability
          const textMetrics = ctx.measureText(labelText);
          const labelWidth = textMetrics.width + 16; // Extra padding for centered text
          const labelHeight = 20; // Slightly taller for better vertical centering

          const labelX = left + 4;

          ctx.fillStyle = labelBackgroundColor;
          ctx.fillRect(
            labelX,
            labelY - labelHeight + 4,
            labelWidth,
            labelHeight
          );

          // Draw border around label background
          ctx.strokeStyle = labelBorderColor;
          ctx.lineWidth = 1;
          ctx.setLineDash([]);
          ctx.strokeRect(
            labelX,
            labelY - labelHeight + 4,
            labelWidth,
            labelHeight
          );

          // Draw the text centered in the box
          ctx.fillStyle = labelTextColor;
          const textX = labelX + labelWidth / 2;
          const textY = labelY - labelHeight / 2 + 4;
          ctx.fillText(labelText, textX, textY);
        }

        ctx.restore();
      }
    },
  };
}

// Helper function to register multiple horizontal lines at once
export function registerHorizontalLinePlugin(
  chart: typeof Chart,
  config: HorizontalLineConfig
) {
  const plugin = createHorizontalLinePlugin(config);
  Chart.register(plugin);
  return plugin;
}
