import type { z } from "zod";

import type {
  ConversationSchema,
  MessageDataSchema,
  MessagePayloadSchema,
} from "~/types/chat/api-schemas";
import { ChatParticipant } from "~/types/chat/common";
import type {
  LocalConversation,
  MessageStateExtension,
  StatefulAiMessage,
  StatefulMessage,
  StatefulUserMessage,
} from "~/types/chat/models";

export function createLocalConversation(
  conversation: z.infer<typeof ConversationSchema>
): LocalConversation {
  return {
    ...conversation,
    messages: [],
    documents: [],
    totalMessages: 0,
    activeCanvasComponentType: undefined,
    activeCanvasComponentProps: undefined,
    activeCanvasComponentId: undefined,
  };
}

export function generateTempId(): string {
  return `temp-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
}

export function createUserMessage(
  payload: z.infer<typeof MessagePayloadSchema>,
  tempId: string
): StatefulUserMessage {
  return {
    type: ChatParticipant.USER,
    content: payload.message,
    formData: payload.additionalPayload?.formConfig?.fields || {},
    checkpointId: undefined,
    state: "pending",
    id: tempId,
  };
}

export function createAiMessage(tempId: string): StatefulAiMessage {
  return {
    type: ChatParticipant.AI,
    parts: [],
    thoughtSteps: [],
    state: "pending",
    id: tempId,
  };
}

export function isMessagePersisted(
  message: StatefulMessage
): message is StatefulMessage & { state: "persisted"; id: string } {
  return message.state === "persisted";
}

export function transformMessage(
  message: z.infer<typeof MessageDataSchema>,
  messageState: MessageStateExtension
): StatefulMessage {
  return {
    ...message,
    ...messageState,
  };
}
