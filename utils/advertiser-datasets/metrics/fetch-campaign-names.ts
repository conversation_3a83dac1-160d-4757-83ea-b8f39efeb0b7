import type { CampaignNamesData } from "./types";
import type { SimpleArgs } from "../datasets";

interface ListCampaignsResponse {
  campaigns: {
    campaignId: string;
    name: string;
  }[];
  nextToken?: string;
}

export async function fetchCampaignNames(
  args: SimpleArgs
): Promise<CampaignNamesData> {
  const campaignNames: Record<string, string> = {};
  let nextToken: string | undefined;

  do {
    const campaignList = await doFetchCampaignNames(
      args.advertiserId,
      nextToken
    );
    campaignList.campaigns.forEach((campaign) => {
      campaignNames[campaign.campaignId] = campaign.name;
    });

    nextToken = campaignList.nextToken;
  } while (nextToken);

  return campaignNames;
}

function doFetchCampaignNames(advertiserId: string, nextToken?: string) {
  const body: Record<string, any> = {
    maxResults: 100,
  };

  if (nextToken) {
    body["nextToken"] = nextToken;
  }

  return $fetch<ListCampaignsResponse>(
    "/relay/rob/api/adsp/v1/campaigns/list",
    {
      method: "POST",
      headers: {
        "Gigi-AmazonDSPAdvertiserId": advertiserId,
      },
      body,
    }
  );
}
