import type { SimpleArgs } from "../datasets";

export async function fetchCampaignFunnelData(args: SimpleArgs) {
  return await $fetch("/relay/rob/api/metadata/campaign-funnels", {
    query: {
      advertiserId: args.advertiserId,
    },
  });
}

export async function fetchCampaignFunnel(
  advertiserId: string,
  campaignId: string
): Promise<string | null> {
  const funnelData = await fetchCampaignFunnelData({ advertiserId });
  const campaign = funnelData.find(
    (item: any) => item.campaignId === campaignId
  );
  return campaign?.funnel || null;
}
