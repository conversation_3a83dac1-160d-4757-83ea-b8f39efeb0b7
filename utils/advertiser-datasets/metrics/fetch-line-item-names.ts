import type { CampaignNamesData, LineItemNamesData } from "./types";
import type { SimpleArgs } from "../datasets";

interface ListAdGroupsResponse {
  adGroups: {
    adGroupId: string;
    name: string;
  }[];
  nextToken?: string;
}

export async function fetchLineItemNames(
  args: SimpleArgs
): Promise<LineItemNamesData> {
  const lineItemNames: Record<string, string> = {};
  let nextToken: string | undefined;

  do {
    const lineItemList = await doFetchLineItems(
      args.advertiserId,
      args.campaignId,
      nextToken
    );
    lineItemList.adGroups.forEach((adGroup) => {
      lineItemNames[adGroup.adGroupId] = adGroup.name;
    });

    nextToken = lineItemList.nextToken;
  } while (nextToken);

  return lineItemNames;
}

function doFetchLineItems(
  advertiserId: string,
  campaignId: string,
  nextToken?: string
) {
  const body: Record<string, any> = {
    campaignIdFilter: [campaignId],
    maxResults: 100,
  };

  if (nextToken) {
    body["nextToken"] = nextToken;
  }

  return $fetch<ListAdGroupsResponse>("/relay/rob/api/adsp/v1/adGroups/list", {
    method: "POST",
    headers: {
      "Gigi-AmazonDSPAdvertiserId": advertiserId,
    },
    body,
  });
}
