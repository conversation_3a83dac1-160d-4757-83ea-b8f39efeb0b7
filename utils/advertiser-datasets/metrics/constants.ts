export const ADSP_CAMPAIGN_METRIC_DEFINITIONS = {
  "3PFees": {
    label: "3rd Party Fees",
    description: "The total CPM charges applied for using 3P data providers.",
    valueType: "currency",
  },
  "3pFeeCPM1": {
    label: "Gigi CPM Fee",
    description: "A third-party fee applied.",
    valueType: "currency",
  },
  "3pFeeCPM1Absorbed": {
    label: "Gigi CPM Fee Amount",
    description:
      "CPM charge applied to impressions to track general 3P technology fees. The cost is itemized in reports, but absorbed by the user and excluded from total costs.",
    valueType: "currency",
  },
  NewToBrandECPP: {
    label: "Customer Acquisition Cost",
    description:
      "Effective (average) cost to acquire a new-to-brand purchase conversion for a promoted product. (New-to-brand eCPP = Total cost / New-to-brand purchases) Use Total new-to-brand eCPP to see all conversions for the brands' products.",
    valueType: "currency",
  },
  addToCart: {
    label: "Add to Cart",
    description:
      "Number of times shoppers added a brand's products to their cart, attributed to an ad view or click.",
    valueType: "numeric",
  },
  addToCartClicks: {
    label: "Add to Cart Clicks",
    description:
      "Number of times shoppers added a brand's products to their cart, attributed to an ad click.",
    valueType: "numeric",
  },
  addToCartRate: {
    label: "Add to Cart Rate",
    description: "Calculated by divididing addToCart by impressions.",
    valueType: "percentage",
  },
  addToCartViews: {
    label: "Add to Cart Views",
    description:
      "Number of times shoppers added the brands' products to their cart, attributed to an ad view.",
    valueType: "numeric",
  },
  addToList: {
    label: "Add to List",
    description:
      "Number of times shoppers added a promoted product to a wish list, gift list, or registry, attributed to an ad view or click. Use Total ATL to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  addToListClicks: {
    label: "Add to List Clicks",
    description:
      "Number of times shoppers added a promoted product to a wish list, gift list, or registry, attributed to an ad click. Use Total ATL clicks to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  addToListRate: {
    label: "Add to List Rate",
    description:
      "Rate of Add to List conversions for promoted products relative to the number of impressions. (ATLR = ATL / Impressions) Use Total ATLR to see all conversions for the brands' products.",
    valueType: "percentage",
  },
  addToListViews: {
    label: "Add to List Views",
    description:
      "Number of times shoppers added a promoted product to a wish list, gift list, or registry, attributed to an ad view. Use Total ATL views to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  addToShoppingCart: {
    label: "Off-Amazon Add to Shopping Cart",
    description: "The number of Add to shopping cart conversions.",
    valueType: "numeric",
  },
  addToShoppingCartCPA: {
    label: "Off-Amazon Add to Shopping Cart CPA",
    description:
      "The average cost to acquire an Add to shopping cart conversion. (ATSC CPA = Total cost / ATSC)",
    valueType: "currency",
  },
  addToShoppingCartCVR: {
    label: "Off-Amazon Add to Shopping Cart CVR",
    description:
      "The number of Add to shopping cart conversions relative to the number of ad impressions. (ATSC CVR = ATSC / Impressions)",
    valueType: "percentage",
  },
  addToShoppingCartClicks: {
    label: "Off-Amazon Add to Shopping Cart Clicks",
    description:
      "The number of Add to shopping cart conversions attributed to an ad click.",
    valueType: "numeric",
  },
  addToShoppingCartValueAverage: {
    label: "Off-Amazon Add to Shopping Cart AOV",
    description:
      "Average value associated with an Add to Shopping cart conversion. (ATSC value average = ATSC value sum / ATSC)",
    valueType: "currency",
  },
  addToShoppingCartValueSum: {
    label: "Off-Amazon Add to Shopping Cart Sales",
    description: "Sum of Add to shopping cart conversion values",
    valueType: "currency",
  },
  addToShoppingCartViews: {
    label: "Off-Amazon Add to Shopping Cart Views",
    description:
      "The number of Add to shopping cart conversions attributed to an ad view.",
    valueType: "numeric",
  },
  agencyFee: {
    label: "Agency Fee",
    description:
      "A percentage or flat fee removed from the total budget to compensate the agency that is managing the media buy.",
    valueType: "currency",
  },
  amazonDSPAudienceFee: {
    label: "Amazon DSP Audience Fee",
    description:
      "CPM charge applied to impressions that leverage Amazon's behavioral targeting.",
    valueType: "currency",
  },
  amazonDSPConsoleFee: {
    label: "Amazon DSP Console Fee",
    description: "The technology fee applied to the media supply costs.",
    valueType: "currency",
  },
  brandSearchRate: {
    label: "Branded Search Rate",
    description:
      "The number of branded search conversions relative to the number of ad impressions. (Branded search rate = Branded searches / Impressions)",
    valueType: "percentage",
  },
  brandSearches: {
    label: "Branded Searches",
    description:
      "The number of times a branded keyword was searched on Amazon based on keywords generated from the featured ASINs in your campaign.",
    valueType: "numeric",
  },
  brandSearchesClicks: {
    label: "Branded Searches Clicks",
    description:
      "The number of branded search conversions attributed to ad click-throughs.",
    valueType: "numeric",
  },
  brandSearchesViews: {
    label: "Branded Searches Views",
    description:
      "The number of branded search conversions attributed to ad impressions.",
    valueType: "numeric",
  },
  checkout: {
    label: "Off-Amazon Checkout",
    description: "The number of Checkout conversions.",
    valueType: "numeric",
  },
  checkoutCPA: {
    label: "Off-Amazon Checkout CPA",
    description:
      "The average cost to acquire a Checkout conversion. (Checkout CPA = Total cost / Checkout)",
    valueType: "currency",
  },
  checkoutCVR: {
    label: "Off-Amazon Checkout CVR",
    description:
      "The number of Checkout conversions relative to the number of ad impressions. (Checkout CVR = Checkout / Impressions)",
    valueType: "percentage",
  },
  checkoutClicks: {
    label: "Off-Amazon Checkout Clicks",
    description:
      "The number of Checkout conversions attributed to an ad click.",
    valueType: "numeric",
  },
  checkoutValueAverage: {
    label: "Off-Amazon Checkout AOV",
    description:
      "Average value associated with a Checkout conversion. (Checkout value average = Checkout value sum / Checkout)",
    valueType: "currency",
  },
  checkoutValueSum: {
    label: "Off-Amazon Checkout Sales",
    description: "Sum of Checkout conversion values",
    valueType: "currency",
  },
  checkoutViews: {
    label: "Off-Amazon Checkout Views",
    description: "The number of Checkout conversions attributed to an ad view.",
    valueType: "numeric",
  },
  clicks: {
    label: "Clicks",
    description: "Total number of clicks on an ad.",
    valueType: "numeric",
  },
  clickThroughRate: {
    label: "Click Through Rate",
    description: "Clicks divided by impressions.",
    valueType: "percentage",
  },
  combinedECPP: {
    label: "Cross Platform Cost Per Purchase",
    description:
      "Effective (average) cost to acquire a purchase conversion on or off Amazon. (Total cost / Combined purchases)",
    valueType: "currency",
  },
  combinedERPM: {
    label: "Cross Platform Revenue per Thousand Impressions",
    description:
      "Effective (average) revenue for sales on and off Amazon generated per thousand impressions. (Combined Sales / (Impressions / 1000))",
    valueType: "currency",
  },
  combinedProductSales: {
    label: "Cross Platform Product Sales",
    description:
      "Sales (in local currency) for purchases on and off Amazon, attributed to an ad view or click.",
    valueType: "currency",
  },
  combinedPurchaseRate: {
    label: "Cross Platform Purchase Rate",
    description:
      "Rate of attributed purchase events on and off Amazon, relative to ad impressions. (Combined purchases / Impressions)",
    valueType: "percentage",
  },
  combinedPurchases: {
    label: "Cross Platform Purchases",
    description:
      "Number of purchase events on and off Amazon, attributed to an ad view or click. (Off-Amazon purchases + Total purchases (Amazon))",
    valueType: "numeric",
  },
  combinedPurchasesClicks: {
    label: "Cross Platform Purchase Clicks",
    description:
      "Number of purchase events on and off Amazon, attributed to an ad click. (Off-Amazon purchases clicks + Total purchases clicks (Amazon))",
    valueType: "numeric",
  },
  combinedPurchasesViews: {
    label: "Cross Platform Purchase Views",
    description:
      "Number of purchase events on and off Amazon, attributed to an ad view. (Off-Amazon purchases views + Total purchases views (Amazon))",
    valueType: "numeric",
  },
  combinedROAS: {
    label: "Cross Platform ROAS",
    description:
      "Return on advertising spend for products sold on and off Amazon, measured as ad-attributed sales per local currency unit of ad spend. (Combined product sales / Total cost)",
    valueType: "numeric",
  },
  combinedUnitsSold: {
    label: "Cross Platform Units Sold",
    description:
      "Units of product sold on and off Amazon, attributed to an ad view or click. A single purchase event can include multiple sold units.",
    valueType: "numeric",
  },
  detailPageViewClicks: {
    label: "Detail Page View Clicks",
    description:
      "Number of detail page views for promoted products, attributed to an ad click. Use Total DPV clicks to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  detailPageViewRate: {
    label: "Detail Page View Rate",
    description:
      "Detail page view rate for promoted products relative to the number of ad impressions. (DPV / Impressions = DPVR) Use Total DPVR to see all conversions for the brands' products.",
    valueType: "percentage",
  },
  detailPageViewViews: {
    label: "Detail Page View Views",
    description:
      "Number of detail page views for promoted products, attributed to an ad view. Use Total DPV views to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  detailPageViews: {
    label: "Detail Page Views",
    description:
      "Number of detail page views occurring within 14 days of an ad click or view.",
    valueType: "numeric",
  },
  eCPAddToCart: {
    label: "Cost per Add to Cart",
    description:
      "Effect cost per add to cart, calculated by cost divided by add-to-cart.",
    valueType: "currency",
  },
  eCPAddToList: {
    label: "Cost per Add to List",
    description:
      "Effective (average) cost to acquire an Add to List conversion for a promoted product. (eCPATL = Total cost / ATL) Use Total eCPATL to see all conversions for the brands' products.",
    valueType: "currency",
  },
  eCPAddToWatchlist: {
    label: "NA",
    description:
      "The average cost to acquire an Add to Watchlist click (eCPATW = Total cost / ATW)",
    valueType: "currency",
  },
  eCPBrandSearch: {
    label: "Cost per Branded Search",
    description: "Effective (average) cost to acquire a Branded Search.",
    valueType: "currency",
  },
  eCPC: {
    label: "Cost per Click Through",
    description: "The average cost paid per click-through.",
    valueType: "currency",
  },
  eCPDetailPageView: {
    label: "Cost per Detail Page View",
    description:
      "Effective (average) cost to acquire a detail page view for a promoted product. (eCPDPV = Total cost / DPV) Use Total eCPDPV to see all conversions for the brands' products.",
    valueType: "currency",
  },
  eCPM: {
    label: "eCPM",
    description: "The total cost per thousand impressions.",
    valueType: "currency",
  },
  eCPP: {
    label: "Cost per Purchase",
    description:
      "Effective (average) cost to acquire a purchase conversion for a promoted product. (eCPP = Total cost / Purchases) Use Total eCPP to see all conversions for the brands' products.",
    valueType: "currency",
  },
  eCPProductReviewPageVisit: {
    label: "Cost per Product Review Page View",
    description:
      "Effective (average) cost to acquire a product review page conversion for a promoted product. (eCPPRPV = Total cost / PRPV) Use Total eCPPRPV to see all conversions for the brands' products.",
    valueType: "currency",
  },
  eCPnewSubscribeAndSave: {
    label: "Cost per New Subscribe and Save",
    description:
      "Effective (average) cost to acquire a Subscribe & Save subscription for a promoted product. (eCPSnSS = Total cost / SnSS) Use Total eCPSnSS to see all conversions for the brands' products.",
    valueType: "currency",
  },
  eRPM: {
    label: "Revenue per Thousand Impressions",
    description:
      "Effective (average) revenue for promoted products generated per thousand impressions. (eRPM = Sales / (Impressions / 1000)) Use Total eRPM to see all conversions for the brands' products.",
    valueType: "currency",
  },
  frequencyAverage: {
    label: "Average Frequency",
    description:
      "The number of times an ad is served to an individual/ device/ account in a period.",
    valueType: "numeric",
  },
  grossClickThroughs: {
    label: "Gross Click Throughs",
    description:
      "The total number of times the ad was clicked. This includes valid, potentially fraudulent, non-human, and other illegitimate clicks.",
    valueType: "numeric",
  },
  grossImpressions: {
    label: "Gross Impressions",
    description:
      "The total number of times the ad was displayed. This includes valid and invalid impressions such as potentially fraudulent, non-human, and other illegitimate impressions.",
    valueType: "numeric",
  },
  householdFrequencyAverage: {
    label: "Household Frequency",
    description:
      "The number of times an ad is served to a household in a period.",
    valueType: "numeric",
  },
  householdReach: {
    label: "Household Reach",
    description:
      "The number of unique households an ad impression is served to in a period.",
    valueType: "numeric",
  },
  impressionFrequencyAverage: {
    label: "Average Frequency per Unique User",
    description: "The average number of exposures per unique user for an ad",
    valueType: "percentage",
  },
  impressions: {
    label: "Impressions",
    description: "Total number of ad impressions.",
    valueType: "numeric",
  },
  interactiveImpressions: {
    label: "Interactive Impressions",
    description:
      "The number of impressions from an interactive creative. This includes, interactive video, interactive audio and interactive display creatives, and more.",
    valueType: "numeric",
  },
  invalidClickThroughRate: {
    label: "Invalid Click Percentage",
    description:
      "The percentage of gross click-throughs that were removed by the traffic quality filter. (Invalid click-throughs rate = invalid click-throughs / gross click-throughs)",
    valueType: "percentage",
  },
  invalidClickThroughs: {
    label: "Invalid Clicks",
    description:
      "Clicks that were removed by the traffic quality filter. This includes potentially fraudulent, non-human, and other illegitimate traffic.",
    valueType: "numeric",
  },
  invalidImpressionRate: {
    label: "Invalid Impression Percentage",
    description:
      "The percentage of gross impressions that were removed by the traffic quality filter. (Invalid impression rate = invalid impressions / gross impressions)",
    valueType: "percentage",
  },
  invalidImpressions: {
    label: "Invalid Impressions",
    description:
      "The number of impressions removed by a traffic quality filter. This includes potentially fraudulent, non-human, and other illegitimate traffic.",
    valueType: "numeric",
  },
  lineItemBudget: {
    label: "Line Item Budget",
    description: "The total amount of money that be consumed by a line item.",
    valueType: "currency",
  },
  lineItemBudgetCap: {
    label: "Line Item Budget Cap",
    description: "The total budget set for the line item.",
    valueType: "currency",
  },
  measurableImpressions: {
    label: "Measurable Impressions",
    description:
      "Number of impressions that were measured for viewability. The viewability metrics are available from October 1, 2019. Selecting a date range prior to October 1, 2019 will result in incomplete or inaccurate metrics.",
    valueType: "numeric",
  },
  measurableRate: {
    label: "Measurable Rate",
    description:
      "Measurable impressions / total impressions. The viewability metrics are available from October 1, 2019. Selecting a date range prior to October 1, 2019 will result in incomplete or inaccurate metrics.",
    valueType: "percentage",
  },
  newSubscribeAndSave: {
    label: "New Subscribe and Saves",
    description:
      "Number of new Subscribe & Save subscriptions for all products, attributed to an ad view or click. This does not include replenishment subscription orders. Use Total SnSS to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  newSubscribeAndSaveClicks: {
    label: "New Subscribe and Saves Clicks",
    description:
      "Number of new Subscribe & Save subscriptions for all products, attributed to ad clicks. This does not include replenishment subscription orders. Use Total SnSS to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  newSubscribeAndSaveRate: {
    label: "New Subscribe and Save Rate",
    description:
      "The number of new Subscribe & Save subscriptions relative to the number of ad impressions",
    valueType: "percentage",
  },
  newSubscribeAndSaveViews: {
    label: "New Subscribe and Saves Views",
    description:
      "Number of new Subscribe & Save subscriptions for all products, attributed to ad views. This does not include replenishment subscription orders. Use Total SnSS to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  newToBrandDetailPageViewClicks: {
    label: "New to Brand DPV Clicks",
    description:
      "Number of new-to-brand detail page views for all the brands' products, attributed to an ad click.",
    valueType: "numeric",
  },
  newToBrandDetailPageViewRate: {
    label: "New to Brand DPV Rate",
    description:
      "Calculated by dividing newToBrandDetailPageViews / impressions.",
    valueType: "percentage",
  },
  newToBrandDetailPageViewViews: {
    label: "New to Brand DPV Views",
    description:
      "Number of new-to-brand detail page views for all the brands' products, attributed to an ad view",
    valueType: "numeric",
  },
  newToBrandDetailPageViews: {
    label: "New to Brand DPV",
    description:
      "The number of new detail page views from shoppers who have not previously viewed a detail page with an ASIN of the same brand in past 365 days and who either clicked or viewed an ad.",
    valueType: "numeric",
  },
  newToBrandECPDetailPageView: {
    label: "New to Brand Cost per Detail Page View",
    description:
      "Effective cost per new-to-brand detail page view, calculated by cost divided by new-to-brand detail page view.",
    valueType: "currency",
  },
  newToBrandERPM: {
    label: "New to Brand Revenue per Thousand Impressions",
    description:
      "Effective (average) revenue generated per thousand impressions from promoted products purchased by new-to-brand shoppers. (NTB eRPM = NTB Sales / (Impressions / 1000)) Use Total new-to-brand eRPM to see all conversions for the brands' products.",
    valueType: "currency",
  },
  newToBrandProductSales: {
    label: "New to Brand Product Sales",
    description:
      "Sales (in local currency) of promoted products to new-to-brand shoppers, attributed to an ad view or click. Use Total new-to-brand product sales to see all conversions for the brands' products.",
    valueType: "currency",
  },
  newToBrandPurchaseRate: {
    label: "New to Brand Purchase Rate",
    description:
      "Rate of new-to-brand purchase conversions for promoted products relative to the number of ad impressions. (New-to-brand purchase rate = New-to-brand purchases / Impressions) Use Total new-to-brand purchase rate to see all conversions for the brands' products.",
    valueType: "percentage",
  },
  newToBrandPurchases: {
    label: "New to Brand Purchases",
    description:
      "The number of first-time orders for brand products over a one-year lookback window resulting from an ad click or view. Not available for book vendors.",
    valueType: "numeric",
  },
  newToBrandPurchasesClicks: {
    label: "New to Brand Purchases Clicks",
    description:
      "The number of first-time orders for brand products over a one-year lookback window resulting from an ad click. Not available for book vendors.",
    valueType: "numeric",
  },
  newToBrandPurchasesPercentage: {
    label: "New to Brand Purchases Percentage",
    description:
      "The percentage of total orders that are new-to-brand orders within 14 days of an ad click. Not available for book vendors.",
    valueType: "percentage",
  },
  newToBrandPurchasesViews: {
    label: "New to Brand Purchases View",
    description:
      "The number of new to branch purchases attributed to an ad view.",
    valueType: "numeric",
  },
  newToBrandROAS: {
    label: "New to Brand ROAS",
    description: "Return on ad spend for new to brand sales",
    valueType: "numeric",
  },
  newToBrandUnitsSold: {
    label: "New to Brand Units Sold",
    description:
      "Total number of attributed units ordered as part of new-to-brand sales occurring within 14 days of an ad click or view. Not available for book vendors.",
    valueType: "numeric",
  },
  offAmazonCPA: {
    label: "Off-Amazon CPA",
    description: "The average cost to acquire for Off Amazon conversions",
    valueType: "currency",
  },
  offAmazonCVR: {
    label: "Off-Amazon CVR",
    description:
      "Number of off-Amazon conversions relative to the number of ad impressions. (Off-Amazon conversions / Impressions)",
    valueType: "percentage",
  },
  offAmazonClicks: {
    label: "Off-Amazon Clicks",
    description:
      "Number of conversions that occured off Amazon attributed to an ad click. This includes all conversion types.",
    valueType: "numeric",
  },
  offAmazonConversions: {
    label: "Off-Amazon Conversions",
    description:
      "Number of conversions that occured off Amazon attributed to an ad view or click. This includes all conversion types.",
    valueType: "numeric",
  },
  offAmazonECPP: {
    label: "Off-Amazon Cost per Purchase",
    description:
      "Effective (average) cost to acquire an off-Amazon purchase event. (Total cost / Off-Amazon purchases)",
    valueType: "currency",
  },
  offAmazonERPM: {
    label: "Off-Amazon Revenue per Thousand Impressions",
    description:
      "Effective (average) revenue for sales included in off-Amazon purchases generated per thousand impressions. (Off-Amazon sales / (Impressions / 1000))",
    valueType: "currency",
  },
  offAmazonProductSales: {
    label: "Off-Amazon Product Sales",
    description:
      "Sales (in local currency) of promoted products to off Amazon shoppers, attributed to an ad view or click.",
    valueType: "currency",
  },
  offAmazonPurchaseRate: {
    label: "Off-Amazon Purchase Rate",
    description:
      "Rate of attributed off-Amazon purchase events relative to ad impressions. (Off-Amazon purchases / Impressions)",
    valueType: "percentage",
  },
  offAmazonPurchases: {
    label: "Off-Amazon Purchases",
    description: "Off Amazon Purchases",
    valueType: "numeric",
  },
  offAmazonPurchasesClicks: {
    label: "Off-Amazon Purchases Clicks",
    description:
      "Number of off-Amazon purchase events attributed to an ad click.",
    valueType: "numeric",
  },
  offAmazonPurchasesViews: {
    label: "Off-Amazon Purchases Views",
    description:
      "Number of off-Amazon purchase events attributed to an ad view.",
    valueType: "numeric",
  },
  offAmazonROAS: {
    label: "Off-Amazon ROAS",
    description: "Return on ad spend for off Amazon sales",
    valueType: "numeric",
  },
  offAmazonUnitsSold: {
    label: "Off-Amazon Units Sold",
    description:
      "Units of product purchased off Amazon, attributed to an ad view or click. A single purchase event can include multiple sold units.",
    valueType: "numeric",
  },
  offAmazonViews: {
    label: "Off-Amazon Views",
    description:
      "Number of conversions that occured off Amazon attributed to an ad view. This includes all conversion types.",
    valueType: "numeric",
  },
  omnichannelMetricsFee: {
    label: "Omnichannel Metrics Fee",
    description:
      "This fee is calculated based on a percentage of the supply cost",
    valueType: "currency",
  },
  orderBudget: {
    label: "Order Budget",
    description: "The total amount of money that be consumed by an order.",
    valueType: "currency",
  },
  pageViewCPA: {
    label: "Off-Amazon Page View CPA",
    description:
      "The average cost to acquire a Page view conversion. (Page view CPA = Total cost / Page view)",
    valueType: "currency",
  },
  pageViewCVR: {
    label: "Off-Amazon Page View Rate",
    description:
      "The number of Page view conversions relative to the number of ad impressions. (Page view CVR = Page view / Impressions)",
    valueType: "percentage",
  },
  pageViewClicks: {
    label: "Off-Amazon Page View Clicks",
    description:
      "The number of Page view conversions attributed to an ad click.",
    valueType: "numeric",
  },
  pageViewValueAverage: {
    label: "Off-Amazon Page View Conversion Rate",
    description:
      "Average value associated with a Page view conversion. (Page view value average = Page view value sum / Page view)",
    valueType: "currency",
  },
  pageViewValueSum: {
    label: "Off-Amazon Page View Conversion Value",
    description: "Sum of Page view conversion values",
    valueType: "currency",
  },
  pageViewViews: {
    label: "Off-Amazon Page View Views",
    description:
      "The number of Page view conversions attributed to an ad view.",
    valueType: "numeric",
  },
  pageViews: {
    label: "Off-Amazon Page Views",
    description: "The number of Page view conversions.",
    valueType: "numeric",
  },
  productReviewPageVisitRate: {
    label: "Product Review Page Visit Rate",
    description:
      "Rate of product review page visits for promoted products, relative to the number of ad impressions. (ATCR = ATC / Impressions) Use Total PRPVR to see all conversions for the brands' products.",
    valueType: "percentage",
  },
  productReviewPageVisits: {
    label: "Product Review Page Vists",
    description:
      "Number of times shoppers visited the product review page for a promoted product, attributed to an ad view or click. Use Total PRPV to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  productReviewPageVisitsClicks: {
    label: "Product Review Page Visits Clicks",
    description:
      "Number of times shoppers visited the product review page for a promoted product, attributed to an ad click. Use Total PRPV clicks to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  productReviewPageVisitsViews: {
    label: "Product Review Page Visits Views",
    description:
      "Number of times shoppers visited the product review page for a promoted product, attributed to an ad view. Use Total PRPV views to see all conversions for the brands' products.",
    valueType: "numeric",
  },
  purchaseRate: {
    label: "Purchase Rate",
    description:
      "Rate of ad-attributed purchase events for promoted products, relative to ad impressions. (Purchase rate = Purchases / Impressions) Use Total purchase rate to see all conversions for the brands' products.",
    valueType: "percentage",
  },
  purchases: {
    label: "Purchases",
    description:
      "Number of attributed conversion events occurring within 14 days of an ad click or view.",
    valueType: "numeric",
  },
  purchasesClicks: {
    label: "Purchases Clicks",
    description:
      "Number of attributed conversion events occurring within 14 days of an ad click.",
    valueType: "numeric",
  },
  purchasesViews: {
    label: "Purchases Views",
    description:
      "Number of attributed conversion events occurring within 14 days of an ad view.",
    valueType: "numeric",
  },
  reach: {
    label: "Reach",
    description:
      "The number of unique individuals an ad impression is served to in a period.",
    valueType: "numeric",
  },
  ROAS: {
    label: "ROAS",
    description: "Return on ad spend.",
    valueType: "numeric",
  },
  sales: {
    label: "Sales",
    description:
      "Total value of sales occurring within 14 days of an ad click or view.",
    valueType: "currency",
  },
  search: {
    label: "Off-Amazon Product Searches",
    description: "The number of Search conversions.",
    valueType: "numeric",
  },
  searchCPA: {
    label: "Off-Amazon Product Searches CPA",
    description:
      "The average cost to acquire a Search conversion. (Search CPA = Total cost / Search)",
    valueType: "currency",
  },
  searchCVR: {
    label: "Off-Amazon Product Searches Rate",
    description:
      "The number of Search conversions relative to the number of ad impressions. (Search CVR = Search / Impressions)",
    valueType: "percentage",
  },
  searchClicks: {
    label: "Off-Amazon Product Searches Clicks",
    description: "The number of Search conversions attributed to an ad click.",
    valueType: "numeric",
  },
  searchValueAverage: {
    label: "Off-Amazon Product Searches Conversion Rate",
    description:
      "Average value associated with a Search conversion. (Search value average = Search value sum / Search)",
    valueType: "currency",
  },
  searchValueSum: {
    label: "Off-Amazon Product Searches Conversion Value",
    description: "Sum of Search conversion values",
    valueType: "currency",
  },
  searchViews: {
    label: "Off-Amazon Product Searches Views",
    description: "The number of Search conversions attributed to an ad view.",
    valueType: "numeric",
  },
  supplyCost: {
    label: "Supply Cost",
    description: "The total amount of money spent on media supply.",
    valueType: "currency",
  },
  totalAddToCart: {
    label: "Total Add to Cart",
    description:
      "Number of times shoppers added the brands' products to their cart, attributed to an ad view or click.",
    valueType: "numeric",
  },
  totalAddToCartClicks: {
    label: "Total Add to Cart Clicks",
    description:
      "Number of times shoppers added the brands' products to their cart, attributed to an ad click.",
    valueType: "numeric",
  },
  totalAddToCartRate: {
    label: "Total Add to Cart Rate",
    description:
      "Rate of Add to Cart conversions for the brands' products relative to the number of ad impressions. (Total ATCR = Total ATC / Impressions)",
    valueType: "percentage",
  },
  totalAddToCartViews: {
    label: "Total Add to Cart Views",
    description:
      "Number of times shoppers added the brands' products to their cart, attributed to an ad view.",
    valueType: "numeric",
  },
  totalAddToList: {
    label: "Total Add to List",
    description:
      "Number of times shoppers added the brands' products to a wish list, gift list, or registry, attributed to an ad view or click.",
    valueType: "numeric",
  },
  totalAddToListClicks: {
    label: "Total Add to List Clicks",
    description:
      "Number of times shoppers added the brands' products to a wish list, gift list, or registry, attributed to an ad click.",
    valueType: "numeric",
  },
  totalAddToListRate: {
    label: "Total Add to List Rate",
    description:
      "Rate of Add to List conversions for the brands' products relative to the number of impressions. (ATLR = ATL / Impressions)",
    valueType: "percentage",
  },
  totalAddToListViews: {
    label: "Total Add to List Views",
    description:
      "Number of times shoppers added the brands' products to a wish list, gift list or registry, attributed to an ad view.",
    valueType: "numeric",
  },
  totalCost: {
    label: "Spend",
    description:
      "The total amount of money spent on running the campaign not including 3P fees paid by the agency.",
    valueType: "currency",
  },
  totalDetailPageView: {
    label: "Total Detail Page Views",
    description:
      "Number of detail page views for all the brands and products attributed to an ad view or click.",
    valueType: "numeric",
  },
  totalDetailPageViewClicks: {
    label: "Total Detail Page View Clicks",
    description:
      "Number of detail page views for all the brands and products attributed to an ad click.",
    valueType: "numeric",
  },
  totalDetailPageViewRate: {
    label: "Total Detail Page View Rate",
    description:
      "Detail page view rate for the brands' products, relative to the number of ad impressions. (Total DPV / Impressions = Total DPVR)",
    valueType: "percentage",
  },
  totalDetailPageViewViews: {
    label: "Total Detail Page View Views",
    description:
      "Number of detail page views for all the brands' products, attributed to an ad view.",
    valueType: "numeric",
  },
  totalECPAddToCart: {
    label: "Total Cost per Add to Cart",
    description:
      "Effective (average) cost to acquire an Add to Cart conversion for the brands' products. (Total eCPATC = Total cost / Total ATC)",
    valueType: "currency",
  },
  totalECPAddToList: {
    label: "Total Cost per Add to List",
    description:
      "Effective (average) cost to acquire an Add to List conversion for the brands' products. (Total eCPATL = Total cost / Total ATL)",
    valueType: "currency",
  },
  totalECPDetailPageView: {
    label: "Total Cost per Detail Page View",
    description:
      "Effective (average) cost to acquire a detail page view for a product in your brand. (Total eCPDPV = Total cost / Total DPV)",
    valueType: "currency",
  },
  totalECPP: {
    label: "Total Cost per Purchase",
    description:
      "Effective (average) cost to acquire a purchase conversion for the brands' products. (Total eCPP = Total cost / Total purchases)",
    valueType: "currency",
  },
  totalECPProductReviewPageVisit: {
    label: "Total Cost per Product Review Page Visit",
    description:
      "Effective (average) cost to acquire a product review page conversion for the brands' products. (Total eCPPRPV = Total cost / Total PRPV)",
    valueType: "currency",
  },
  totalECPSubscribeAndSave: {
    label: "Total Cost per Subscribe and Save",
    description:
      "Effective (average) cost to acquire a Subscribe & Save subscription for the brands' products. (Total eCPSnSS = Total cost / Total SnSS)",
    valueType: "currency",
  },
  totalERPM: {
    label: "Total Cost per Thousand Impressions",
    description:
      "Effective (average) revenue for the brands' products generated per thousand impressions. (Total eRPM = Sales / (Impressions / 1000))",
    valueType: "currency",
  },
  totalFee: {
    label: "Total Fees",
    description: "The sum of all fees.",
    valueType: "currency",
  },
  totalNewToBrandDPVClicks: {
    label: "Total New to Brand DPV Clicks",
    description:
      "Number of new-to-brand detail page views for all the brands' products, attributed to an ad click.",
    valueType: "numeric",
  },
  totalNewToBrandDPVRate: {
    label: "Total New to Brand DPV Rate",
    description:
      "The new-to-brand detail page view rate for the brands' products, relative to the number of ad impressions. (Total NTB - DPV / Impressions = Total DPVR)",
    valueType: "percentage",
  },
  totalNewToBrandDPVViews: {
    label: "Total New to Brand DPV Views",
    description:
      "Number of new-to-brand detail page views for all the brands' products, attributed to an ad view.",
    valueType: "numeric",
  },
  totalNewToBrandDPVs: {
    label: "Total New to Brand DPV",
    description:
      "Number of new-to-brand detail page views for all the brands' products, attributed to an ad view or click.",
    valueType: "numeric",
  },
  totalNewToBrandECPDetailPageView: {
    label: "Total New to Brand Cost per Detail Page View",
    description:
      "Effective total cost for new-to-brand detail page view, calculated by cost divided by new-to-brand detail page view",
    valueType: "currency",
  },
  totalNewToBrandECPP: {
    label: "Total Customer Acquisition Cost",
    description:
      "Effective (average) cost to acquire a new-to-brand purchase conversion for the brands' products. (Total new-to-brand eCPP = Total cost / Total new-to-brand purchases)",
    valueType: "currency",
  },
  totalNewToBrandERPM: {
    label: "Total New to Brand Revenue per Thousand Impressions",
    description:
      "Effective (average) revenue generated per thousand impressions from the brands' products purchased by new-to-brand shoppers. (Total NTB eRPM = Total NTB Sales / (Impressions / 1000))",
    valueType: "currency",
  },
  totalNewToBrandPurchaseRate: {
    label: "Total New to Brand Purchase Rate",
    description:
      "Rate of new-to-brand purchase conversions for the brands' products relative to the number of ad impressions. (Total new-to-brand purchase rate = Total new-to-brand purchases / Impressions)",
    valueType: "percentage",
  },
  totalNewToBrandPurchases: {
    label: "Total New to Brand Purchases",
    description:
      'Number of new-to-brand purchases for the brands\' products, attributed to an ad view or click. Shoppers are "new to brand" if they have not purchased from the brand in the last 365 days.',
    valueType: "numeric",
  },
  totalNewToBrandPurchasesClicks: {
    label: "Total New to Brand Purchases Clicks",
    description:
      'Number of new-to-brand purchases for the brands\' products, attributed to an ad click. Shoppers are "new to brand" if they have not purchased from the brand in the last 365 days.',
    valueType: "numeric",
  },
  totalNewToBrandPurchasesPercentage: {
    label: "Total New to Brand Purchases Percentage",
    description:
      "Rate of new-to-brand purchase conversions for the brands' products relative to the number of ad impressions. (Total new-to-brand purchase rate = Total new-to-brand purchases / Impressions)",
    valueType: "percentage",
  },
  totalNewToBrandPurchasesViews: {
    label: "Total New to Brand Purchases Views",
    description:
      'Number of new-to-brand purchases for the brands\' products, attributed to an ad view. Shoppers are "new to brand" if they have not purchased from the brand in the last 365 days.',
    valueType: "numeric",
  },
  totalNewToBrandROAS: {
    label: "Total New to Brand ROAS",
    description:
      "Return on advertising spend for new-to-brand purchases, measured as ad-attributed new-to-brand sales for the brands' products per local currency unit of ad spend. (Total new-to-brand ROAS = Total new to brand product sales / Total cost)",
    valueType: "numeric",
  },
  totalNewToBrandSales: {
    label: "Total New to Brand Sales",
    description:
      'Sales (in local currency) of the brands\' products purchased by new-to-brand shoppers, attributed to an ad view or click. Shoppers are "new to brand" if they have not purchased from the brand in the last 365 days.',
    valueType: "currency",
  },
  totalNewToBrandUnitsSold: {
    label: "Total New to Brand Units Sold",
    description:
      "Units of the brands' products purchased by shoppers who were new-to-brand, attributed to an ad view or click. A single new-to-brand purchase event can include multiple sold units.",
    valueType: "numeric",
  },
  totalProductReviewPageVisitRate: {
    label: "Total Product Review Page Visit Rate",
    description:
      "Rate of product review page visits for the brands' products, relative to the number of ad impressions. (Total ATCR = Total ATC / Impressions)",
    valueType: "percentage",
  },
  totalProductReviewPageVisits: {
    label: "Total Product Review Page Visits",
    description:
      "Number of times shoppers visited a product review page for the brands' products, attributed to an ad view or click.",
    valueType: "numeric",
  },
  totalProductReviewPageVisitsClicks: {
    label: "Total Product Review Page Visits Clicks",
    description:
      "Number of times shoppers visited a product review page for the brands' products, attributed to an ad click.",
    valueType: "numeric",
  },
  totalProductReviewPageVisitsViews: {
    label: "Total Product Review Page Visits Views",
    description:
      "Number of times shoppers visited a product review page for the brands' products, attributed to an ad view.",
    valueType: "numeric",
  },
  totalPurchaseRate: {
    label: "Total Purchase Rate",
    description:
      "Rate of ad-attributed purchase events for the brands' products relative to ad impressions. (Total purchase rate = Total purchases / Impressions)",
    valueType: "percentage",
  },
  totalPurchases: {
    label: "Total Purchases",
    description:
      "Number of times any quantity of a brand product was included in a purchase event, attributed to an ad view or click. Purchase events include new Subscribe & Save subscriptions and video rentals.",
    valueType: "numeric",
  },
  totalPurchasesClicks: {
    label: "Total Purchases Clicks",
    description:
      "Number of times any quantity of a brand product was included in a purchase event, attributed to an ad click. Purchase events include new Subscribe & Save subscriptions and video rentals.",
    valueType: "numeric",
  },
  totalPurchasesViews: {
    label: "Total Purchases Views",
    description:
      "Number of times any quantity of a brand product was included in a purchase event, attributed to an ad view. Purchase events include new Subscribe & Save subscriptions and video rentals.",
    valueType: "numeric",
  },
  totalROAS: {
    label: "Total ROAS",
    description:
      "Return on ad spend, measured as ad-attributed sales for the brands' products per local currency unit of ad spend. (Total ROAS = Total product sales / Total cost)",
    valueType: "numeric",
  },
  totalSales: {
    label: "Total Sales",
    description:
      "Sales (in local currency) of the brands' products, attributed to an ad view or click.",
    valueType: "currency",
  },
  totalSubscribeAndSave: {
    label: "Total Subscribe and Save",
    description:
      "Number of new Subscribe & Save subscriptions for the brands and products, attributed to an ad view or click. This does not include replenishment subscription orders.",
    valueType: "numeric",
  },
  totalSubscribeAndSaveClicks: {
    label: "Total Subscribe and Saves Clicks",
    description:
      "Number of new Subscribe & Save subscriptions for the brands and products, attributed to an ad click. This does not include replenishment subscription orders.",
    valueType: "numeric",
  },
  totalSubscribeAndSaveRate: {
    label: "Total Subscribe and Save Rate",
    description:
      "Rate of ad-attributed Subscribe & Save conversions for the brands' products relative to ad impressions. (Total SnSSR = Total SnSS / Impressions)",
    valueType: "percentage",
  },
  totalSubscribeAndSaveViews: {
    label: "Total Subscribe and Saves Views",
    description:
      "Number of new Subscribe & Save subscriptions for the brands and products, attributed to an ad view. This does not include replenishment subscription orders.",
    valueType: "numeric",
  },
  totalUnitsSold: {
    label: "Total Units Sold",
    description:
      "Units of the brands' products purchased, attributed to an ad view or click. A single purchase event can include multiple sold units.",
    valueType: "numeric",
  },
  unitsSold: {
    label: "Units Sold",
    description:
      "Number of attributed units sold within 14 days of an ad click or view.",
    valueType: "numeric",
  },
  videoAdClicks: {
    label: "Video Clicks",
    description:
      "The number of times a video ad was clicked excluding clicks to the navigation (e.g. Play or Pause buttons).",
    valueType: "numeric",
  },
  videoAdComplete: {
    label: "Video Completions",
    description:
      "The number of times a video ad played to completion. If rewind occurred, completion was calculated on the total percentage of unduplicated video viewed.",
    valueType: "numeric",
  },
  videoAdCompletionRate: {
    label: "Video Completion Rate",
    description:
      "The number of video completions relative to the number of video starts. (Video completion rate = Video complete / Video start)",
    valueType: "percentage",
  },
  videoAdCreativeViews: {
    label: "Video Creative Views",
    description: "The number of times a portion of a video was seen.",
    valueType: "numeric",
  },
  videoAdEndStateClicks: {
    label: "Video Ad End Clicks",
    description: "The number of ad clicks after a video ends.",
    valueType: "numeric",
  },
  videoAdFirstQuartile: {
    label: "Video First Quartile Views",
    description:
      "The number of times at least 25% of a video ad played. If rewind occurred, percent complete was calculated on the total percentage of unduplicated video viewed.",
    valueType: "numeric",
  },
  videoAdImpressions: {
    label: "Video Ad Impressions",
    description: "The number of times a video was served.",
    valueType: "numeric",
  },
  videoAdMidpoint: {
    label: "Video Ad Midpoint Views",
    description:
      "The number of times at least 50% of a video ad played. If rewind occurred, percent complete was calculated on the total percentage of unduplicated video viewed.",
    valueType: "numeric",
  },
  videoAdMute: {
    label: "Video Ad Mutes",
    description: "The number of times a user muted the video ad.",
    valueType: "numeric",
  },
  videoAdPause: {
    label: "Video Ad Pauses",
    description: "The number of times a user paused the video ad.",
    valueType: "numeric",
  },
  videoAdReplays: {
    label: "Video Ad Replays",
    description: "The number of times a video was replayed.",
    valueType: "numeric",
  },
  videoAdResume: {
    label: "Video Ad Resumes",
    description: "The number of times a user unpaused the video ad.",
    valueType: "numeric",
  },
  videoAdSkipBacks: {
    label: "Video Ad Skip Backs",
    description: "The number of times a video was skipped backwards.",
    valueType: "numeric",
  },
  videoAdSkipForwards: {
    label: "Video Ad Skip Forwards",
    description: "The number of times an ad was skipped forward.",
    valueType: "numeric",
  },
  videoAdStart: {
    label: "Video Ad Start",
    description: "The number of times a video ad was started.",
    valueType: "numeric",
  },
  videoAdThirdQuartile: {
    label: "Video Ad Third Quartile Views",
    description:
      "The number of times at least 75% of a video ad played. If rewind occurred, percent complete was calculated on the total percentage of unduplicated video viewed.",
    valueType: "numeric",
  },
  videoAdUnmute: {
    label: "Video Ad Unmutes",
    description: "The number of times a user unmuted the video ad.",
    valueType: "numeric",
  },
  viewabilityRate: {
    label: "Viewability Rate",
    description: "View-through rate (vtr). Views divided by impressions.",
    valueType: "percentage",
  },
  viewableImpressions: {
    label: "Viewable Impressions",
    description:
      "Number of impressions that met the Media Ratings Council (MRC) viewability standard.",
    valueType: "numeric",
  },
  consolidatedAssistedPurchases: {
    label: "Assisted Purchases",
    description:
      "Total number of purchases influenced by the STV Ad at any point along the customer journey to purchase.",
    valueType: "numeric",
  },
  consolidatedAssistedSales: {
    label: "Assisted Total Sales",
    description:
      "Total sales influenced by the STV Ad at any point along the customer journey to purchase.",
    valueType: "currency",
  },
  consolidatedAssistedNtbPurchases: {
    label: "Assisted NTB Purchases",
    description:
      "Total number of new to brand purchases influenced by the STV Ad at any point along the customer journey to purchase.",
    valueType: "numeric",
  },
  consolidatedAssistedNtbSales: {
    label: "Assisted NTB Total Sales",
    description:
      "Total new to brand sales influenced by the STV Ad at any point along the customer journey to purchase.",
    valueType: "currency",
  },
  consolidatedAssistedDpvs: {
    label: "Assisted DPV",
    description:
      "Total detail page views that were influenced by the STV Ad at any point along the customer journey.",
    valueType: "numeric",
  },
  consolidatedFirstTouchRoas: {
    label: "First Touch ROAS",
    description:
      "Sales / Spend ratio where the sales are attributed to only the first advertisement the user saw.",
    valueType: "numeric",
  },
  consolidatedLinearTouchRoas: {
    label: "Linear ROAS",
    description:
      "Sales / Spend ratio where the sales are equally split among the number of touch points the user had on the path to purchase.",
    valueType: "numeric",
  },
  consolidatedGigiAttributionRoas: {
    label: "Gigi ROAS",
    description:
      "Sales / Spend ratio where the sales are attributed more heavily to the first and last touch points versus the middle touch points. The weightings also vary depending on how many touch points there were in the user journey.",
    valueType: "numeric",
  },
  consolidatedAssistedRoas: {
    label: "Assisted ROAS",
    description: "Assisted Total Sales / Spend",
    valueType: "numeric",
  },
  consolidatedAssistedNtbRoas: {
    label: "Assisted NTB ROAS",
    description: "New to brand Assisted Sales / Spend ratio",
    valueType: "numeric",
  },
  iCpm: {
    label: "Incremental CPM",
    description:
      "Total cost per thousand impressions for users who had never been exposed to our ads before but are being reached by the STV Ad.",
    valueType: "currency",
  },
  incrementalUserReach: {
    label: "Unique Incremental Reach",
    description:
      "How many users who had never been exposed to your ads before, are being reached by the STV Ad.",
    valueType: "numeric",
  },
  consolidatedAssistedDpvr: {
    label: "Assisted DPVR",
    description:
      "Total number of detail page views influenced by the Ad at any point along the customer journey to purchase divided by impressions.",
    valueType: "percentage",
  },
  consolidatedAssistedNtbPurchaseUsers: {
    label: "Assisted Purchase NTB Users",
    description:
      "Number of NTB users who purchased that were influenced by the Ad at any point along the customer journey to purchase.",
    valueType: "numeric",
  },
  consolidatedAssistedPurchaseUsers: {
    label: "Assisted Purchase Users",
    description:
      "Total users who purchased that were influenced by the STV Ad at any point along the customer journey to purchase.",
    valueType: "numeric",
  },
} as const;

export const DEFAULT_METRICS_PER_FUNNEL_TYPE: {
  funnels: Set<string>;
  metrics: (keyof typeof ADSP_CAMPAIGN_METRIC_DEFINITIONS)[];
}[] = [
  {
    funnels: new Set(["STV", "OLV", "TOF"]),
    metrics: [
      "totalCost",
      "consolidatedAssistedSales",
      "impressions",
      "consolidatedAssistedRoas",
      "consolidatedAssistedNtbRoas",
      "consolidatedFirstTouchRoas",
      "consolidatedAssistedDpvr",
      "totalSales",
    ],
  },
  {
    funnels: new Set(["MOF"]),
    metrics: [
      "totalCost",
      "eCPDetailPageView",
      "totalROAS",
      "totalSubscribeAndSave",
      "clickThroughRate",
      "totalDetailPageViewRate",
      "brandSearchRate",
      "totalNewToBrandPurchases",
    ],
  },
  {
    funnels: new Set(["BOF"]),
    metrics: [
      "totalCost",
      "eCPM",
      "totalSales",
      "detailPageViewRate",
      "totalROAS",
      "totalSubscribeAndSave",
      "purchaseRate",
    ],
  },
] as const;
