import type { SimpleArgs } from "../datasets/types";

interface ListAdCreativesResponse {
  adCreatives: {
    adCreativeId: string;
    name: string;
  }[];
  nextToken?: string;
}

export async function fetchAdCreativesNames(args: {
  advertiserId: string;
  adCreativeIds: string[];
}): Promise<Record<string, string>> {
  const adCreatives: Record<string, string> = {};
  let nextToken: string | undefined;

  do {
    const adCreativesList = await doFetchAdCreatives(
      args.advertiserId,
      args.adCreativeIds,
      nextToken
    );
    adCreativesList.adCreatives.forEach((adCreative) => {
      adCreatives[adCreative.adCreativeId] = adCreative.name;
    });

    nextToken = adCreativesList.nextToken;
  } while (nextToken);

  return adCreatives;
}

function doFetchAdCreatives(
  advertiserId: string,
  adCreativeIds: string[],
  nextToken?: string
) {
  const body: Record<string, any> = {
    maxResults: 100,
    adCreativeIdFilter: { include: adCreativeIds },
  };

  if (nextToken) {
    body["nextToken"] = nextToken;
  }

  return $fetch<ListAdCreativesResponse>(
    "/relay/rob/api/adsp/v1/adCreatives/list",
    {
      method: "POST",
      headers: {
        "Gigi-AmazonDSPAdvertiserId": advertiserId,
      },
      body,
    }
  );
}
