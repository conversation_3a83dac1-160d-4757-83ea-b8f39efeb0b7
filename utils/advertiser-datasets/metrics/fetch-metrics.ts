import { startOfDay } from "date-fns";
import { utc } from "@date-fns/utc";
import {
  MetricsFormat,
  MetricsTag,
  MetricsGroup,
  type PerFunnelOverlapMetrics,
  type PerFunnelSearchTermMetrics,
  type PerFunnelStageMetrics as PerFunnelMetrics,
  type PerAsinPairMetrics,
  type PerDspCampaignSingleAggregate,
  type PerAsinMetrics,
  type AdvertiserLevelCacTimeSeries,
  type AdvertiserLevelLtvSingleAggregate,
  type PerAsinGroupPairMetrics,
  type PerAsinGroupMetrics,
  type CampaignSummaryMetrics,
  type CampaignSpendData,
} from "./types";
import type { CampaignIdArgs, DateRangeArgs } from "../datasets";

type GetMetricsFromGroupArgs = {
  advertiserId: string;
  startDate: Date;
  endDate: Date;
  tag: MetricsTag;
  groupName: MetricsGroup;
  format: MetricsFormat;
  metricNames?: string[];
  multiGroupingFilters?: Partial<
    Record<"campaignId" | "lineItemId" | "creativeId", string>
  >[];
};

export async function getMetricsFromGroup<T>({
  advertiserId,
  startDate,
  endDate,
  tag,
  groupName,
  format,
  metricNames,
  multiGroupingFilters,
}: GetMetricsFromGroupArgs) {
  return await $fetch<T>("/relay/metrics/metrics_from_group", {
    method: "POST",
    body: {
      advertiserId,
      startDate: startOfDay(startDate, { in: utc }).toISOString(),
      endDate: startOfDay(endDate, { in: utc }).toISOString(),
      tag,
      groupName,
      format,
      metricNames,
      multiGroupingFilters,
    },
  });
}

export async function fetchPerFunnelOverlapMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerFunnelOverlapMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_FUNNEL_OVERLAP_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerFunnelSearchTermMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerFunnelSearchTermMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_FUNNEL_SEARCH_TERM_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerFunnelMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerFunnelMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_FUNNEL_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerAsinPairMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerAsinPairMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.LIFE_LONG_WEEKLY,
    groupName: MetricsGroup.PER_ASIN_PAIR_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerDspCampaignMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerDspCampaignSingleAggregate>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_DSP_CAMPAIGN_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerAsinMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerAsinMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_ASIN_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchAdvertiserLevelCacMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<AdvertiserLevelCacTimeSeries>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.ADVERTISER_LEVEL_CAC,
    format: MetricsFormat.TIME_SERIES,
  });
}

export async function fetchAdvertiserLevelLtvMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<AdvertiserLevelLtvSingleAggregate>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.LIFE_LONG_WEEKLY,
    groupName: MetricsGroup.ADVERTISER_LEVEL_LTV,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerAsinGroupPairMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerAsinGroupPairMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.LIFE_LONG_WEEKLY,
    groupName: MetricsGroup.PER_ASIN_GROUP_PAIR_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchPerAsinGroupMetrics(args: DateRangeArgs) {
  return await getMetricsFromGroup<PerAsinGroupMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    tag: MetricsTag.WEEKLY,
    groupName: MetricsGroup.PER_ASIN_GROUP_METRICS,
    format: MetricsFormat.SINGLE_AGGREGATE,
  });
}

export async function fetchCampaignSummaryMetrics(
  args: DateRangeArgs & CampaignIdArgs
) {
  return await getMetricsFromGroup<CampaignSummaryMetrics>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    multiGroupingFilters: [{ campaignId: args.campaignId }],
    tag: MetricsTag.CAMPAIGN_SUMMARY_WEEKLY,
    groupName: MetricsGroup.CAMPAIGN_SUMMARY_METRICS,
    format: MetricsFormat.TIME_SERIES,
  });
}

export async function fetchCampaignSpendMetrics(
  args: DateRangeArgs & CampaignIdArgs
) {
  return await getMetricsFromGroup<CampaignSpendData>({
    advertiserId: args.advertiserId,
    startDate: args.startDate,
    endDate: args.endDate,
    multiGroupingFilters: [{ campaignId: args.campaignId }],
    tag: MetricsTag.DAILY,
    groupName: MetricsGroup.CAMPAIGN_SPEND_METRICS,
    format: MetricsFormat.TIME_SERIES,
  });
}
