import type { AsinCategoryData } from "./types";
import { KnowledgeType, type AsinGroupingSettings } from "~/types/knowledge";
import type { KnowledgeItemResponse } from "~/composables/clients/dtos";
import type { SimpleArgs } from "../datasets";

export async function fetchAsinCategory(
  args: SimpleArgs
): Promise<AsinCategoryData> {
  const response = await $fetch<KnowledgeItemResponse[]>(
    "/relay/rob/api/knowledge/list",
    {
      method: "POST",
      body: [KnowledgeType.ASIN_GROUPING_SETTINGS],
      headers: {
        "Gigi-Scope-Filter": JSON.stringify({
          advertiserIds: [args.advertiserId],
        }),
      },
    }
  );

  if (!response || response.length === 0) {
    console.warn(`No asin category found for advertiser ${args.advertiserId}`);
    return [];
  }

  if (response.length > 1) {
    console.warn(
      `Multiple asin categories found for advertiser ${args.advertiserId}`
    );
    return [];
  }

  return response
    .map((item) => item.payload as AsinGroupingSettings)
    .map((payload) => payload.groups)
    .flat()
    .map((group) => ({
      name: group.name,
      asins: group.asins.map((asin) => asin.toLowerCase().trim()),
    }));
}
