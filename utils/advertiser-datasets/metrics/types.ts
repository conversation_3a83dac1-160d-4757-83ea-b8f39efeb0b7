export enum MetricsGroup {
  PER_FUNNEL_OVERLAP_METRICS = "per-funnel-overlap-metrics",
  PER_FUNNEL_SEARCH_TERM_METRICS = "per-funnel-search-term-metrics",
  PER_FUNNEL_METRICS = "per-funnel-metrics",
  PER_ASIN_PAIR_METRICS = "per-asin-pair-metrics",
  PER_DSP_CAMPAIGN_METRICS = "per-dsp-campaign-metrics",
  PER_ASIN_METRICS = "per-asin-metrics",
  ADVERTISER_LEVEL_CAC = "advertiser-level-cac",
  ADVERTISER_LEVEL_LTV = "advertiser-level-ltv",
  PER_ASIN_GROUP_PAIR_METRICS = "per-asin-group-pair-metrics",
  PER_ASIN_GROUP_METRICS = "per-asin-group-metrics",
  PER_DATE_METRICS = "per-date-metrics",
  PER_LINE_ITEM_METRICS = "per-campaign-line-item-metrics",
  PER_LINE_ITEM_CREATIVE_METRICS = "per-campaign-line-item-creative-metrics",
  CAMPAIGN_SUMMARY_METRICS = "campaign-summary",
  CAMPAIGN_SPEND_METRICS = "impressions_and_costs",
}

export enum MetricsTag {
  DAILY = "DAILY",
  WEEKLY = "WEEKLY",
  LIFE_LONG_WEEKLY = "LIFE_LONG_WEEKLY",
  CAMPAIGN_SUMMARY_WEEKLY = "CAMPAIGN_SUMMARY_WEEKLY",
}

export enum MetricsFormat {
  SINGLE_AGGREGATE = "SINGLE_AGGREGATE",
  TIME_SERIES = "TIME_SERIES",
  TIME_SERIES_AND_AGGREGATE = "TIME_SERIES_AND_AGGREGATE",
}

export enum AttributionModel {
  LINEAR_TOUCH = "linearTouch",
  GIGI = "gigi",
  LAST_TOUCH = "lastTouch",
  FIRST_TOUCH = "firstTouch",
}

export type PerFunnelOverlapMetrics = {
  ntbPurchasePercentage: number;
  brandedSearchUsers: number;
  purchaseRate: number;
  brandedSearches: number;
  totalPurchases: number;
  impressionUsers: number;
  influencedSearchPercentage: number;
  ntbPurchaserPercentage: number;
  ntbPurchases: number;
  impressions: number;
  purchaseUsers: number;
  ntbUsers: number;
};

export type PerFunnelSearchTermMetrics = {
  ntbPurchaseUsers: number;
  purchases: number;
  ntbPurchases: number;
  purchaseUsers: number;
  searchUsers: number;
};

export type PerFunnelStageCoreMetrics =
  | "totalProductSales"
  | "totalPurchaseUsers"
  | "ntbPurchaseUsers";

export type PerFunnelStageMetrics = {
  cost: number;
} & {
  [key in `${AttributionModel}${Capitalize<PerFunnelStageCoreMetrics>}`]: number;
};

export type PerAsinPairMetrics = {
  totalUserCount: number;
  totalPurchases: number;
  followPurchases: number;
  followUserCount: number;
  overlapPercentage: number;
};

export type PerDspCampaignCoreMetrics =
  | "totalProductSales"
  | "purchaseUsers"
  | "ntbProductSales"
  | "totalPurchaseUsers";

export type PerDspCampaignSingleAggregate = {
  cost: number;
} & {
  [key in `${AttributionModel}${Capitalize<PerDspCampaignCoreMetrics>}`]: number;
};

export type PerAsinMetrics = {
  ntbPurchaseUsers: number;
  totalProductSales: number;
  ntbProductSales: number;
  totalPurchaseUsers: number;
  cac: number;
};

export type AdvertiserLevelCacTimeSeries = {
  date: string;
  cost: number;
  userCount: number;
  cac: number;
}[];

export type AdvertiserLevelLtvSingleAggregate = {
  user_count: number;
  total_product_sales: number;
  ltv: number;
};

export type CampaignFunnelData = {
  advertiserId: string;
  campaignId: string;
  funnel: string;
}[];

export type AsinCategoryData = {
  name: string;
  asins: string[];
}[];

export type PerAsinGroupPairData = {
  totalUserCount: number;
  totalPurchases: number;
  followPurchases: number;
  followUserCount: number;
  overlapPercentage: number;
};

export type PerAsinGroupPairMetrics = Record<
  string,
  Record<string, PerAsinGroupPairData>
>;

export type PerAsinGroupData = {
  cac: number;
  ntbPurchaseUsers: number;
  totalProductSales: number;
  totalPurchaseUsers: number;
  ntbProductSales: number;
};

export type PerAsinGroupMetrics = Record<string, PerAsinGroupData>;

export type CampaignNamesData = Record<string, string>;

export type TimeSeriesEntry<TMetricKey extends string> = {
  date: Date;
} & Record<TMetricKey, number> & {
    [K in Exclude<string, TMetricKey | "date">]: never;
  };

export type PerDateMetrics<TMetricKey extends string = string> = {
  totalForPeriod: Record<TMetricKey, number>;
  timeSeries: TimeSeriesEntry<TMetricKey>[];
};

export type LineItemNamesData = Record<string, string>;

export type CreativeNamesData = Record<string, string>;

export type PerLineItemMetrics<
  TCampaignID extends string = string,
  TLineItemID extends string = string,
  TMetricKey extends string = string
> = Record<TCampaignID, Record<TLineItemID, Record<TMetricKey, number>>>;

export type PerLineItemCreativeMetrics<
  TCampaignID extends string = string,
  TLineItemID extends string = string,
  TCreativeID extends string = string,
  TMetricKey extends string = string
> = Record<
  TCampaignID,
  Record<TLineItemID, Record<TCreativeID, Record<TMetricKey, number>>>
>;

export type CampaignSpendData = { totalCost: number }[];

export type CampaignSummaryMetrics = {
  medianImpressions: number;
  avgTime: number;
  averageImpressions: number;
}[];
