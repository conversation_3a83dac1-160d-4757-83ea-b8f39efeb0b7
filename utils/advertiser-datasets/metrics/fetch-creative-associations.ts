import type { CampaignIdArgs } from "../datasets/types";

interface ListCreativeAssociationsResponse {
  associations: {
    adGroupId: string;
    adCreativeId: string;
  }[];
  nextToken?: string;
}

export async function fetchAdGroupCreativeAssociations(
  args: CampaignIdArgs
): Promise<Record<string, Set<string>>> {
  const creativeAssociations: Record<string, Set<string>> = {};
  let nextToken: string | undefined;

  do {
    const creativeAssociationsList = await doFetchAdGroupCreativeAssociations(
      args.advertiserId,
      args.campaignId,
      nextToken
    );
    creativeAssociationsList.associations.forEach((association) => {
      if (!creativeAssociations[association.adGroupId]) {
        creativeAssociations[association.adGroupId] = new Set();
      }
      creativeAssociations[association.adGroupId].add(association.adCreativeId);
    });

    nextToken = creativeAssociationsList.nextToken;
  } while (nextToken);

  return creativeAssociations;
}

function doFetchAdGroupCreativeAssociations(
  advertiserId: string,
  campaignId: string,
  nextToken?: string
) {
  const body: Record<string, any> = {
    maxResults: 100,
  };

  if (nextToken) {
    body["nextToken"] = nextToken;
  }

  return $fetch<ListCreativeAssociationsResponse>(
    "/relay/rob/api/adsp/v1/adCreative/associations/adGroups/list",
    {
      method: "POST",
      headers: {
        "Gigi-AmazonDSPAdvertiserId": advertiserId,
      },
      query: {
        campaignId,
      },
      body,
    }
  );
}
