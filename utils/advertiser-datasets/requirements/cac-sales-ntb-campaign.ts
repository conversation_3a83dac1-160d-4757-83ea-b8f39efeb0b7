import type { RequirementDescriptor, CacSalesNtbCampaignArgs } from "./types";
import type { CacSalesNtbCampaignData } from "../transformers";

export const cacSalesNtbCampaignDescriptor: RequirementDescriptor<
  CacSalesNtbCampaignArgs,
  CacSalesNtbCampaignData
> = {
  id: "cac-sales-ntb-campaign",
  datasets: [
    "per-dsp-campaign-metrics",
    "campaign-funnel-data",
    "campaign-names",
  ],
  transformerKey: "cac-sales-ntb-campaign",
};
