import type { CampaignMetricRequest } from "../datasets/campaign-metrics-batched-descriptor";
import type { PerLineItemMetrics } from "../metrics/types";
import type { RequirementDescriptor } from "./types";

export const perLineItemMetricDescriptor: RequirementDescriptor<
  CampaignMetricRequest,
  PerLineItemMetrics
> = {
  id: "per-line-item-metric",
  datasets: ["per-campaign-line-item-metrics"],
};
