import type { DatasetId } from "../datasets";

// Base requirement arguments
export type BaseRequirementArgs = {
  advertiserId: string;
};

// Utility type for date range requirements
export type DateRangeArgs = {
  startDate: Date;
  endDate: Date;
};

// Specific requirement argument types (only what each requirement needs)
export type CacLtvArgs = BaseRequirementArgs & DateRangeArgs;

export type CacSalesNtbAsinArgs = BaseRequirementArgs & DateRangeArgs;

export type CacSalesNtbCampaignArgs = BaseRequirementArgs & DateRangeArgs;

export type PurchaseOverlapArgs = BaseRequirementArgs & DateRangeArgs;

export type CrossSellAsinsArgs = BaseRequirementArgs & DateRangeArgs;

export type TopSearchTermsArgs = BaseRequirementArgs & DateRangeArgs;

export type BrandedSearchOverlapArgs = BaseRequirementArgs & DateRangeArgs;

export type NtbOverlapArgs = BaseRequirementArgs & DateRangeArgs;

export type CrossSellCategoryArgs = BaseRequirementArgs & DateRangeArgs;

export type CacSalesNtbCategoryArgs = BaseRequirementArgs & DateRangeArgs;

export type FunnelStagesArgs = BaseRequirementArgs & DateRangeArgs;

// Simple requirement descriptor interface (value object)
export interface RequirementDescriptor<TArgs, TResult> {
  readonly id: string;
  readonly datasets: DatasetId[];
  readonly transformerKey?: string;
}
