import type { RequirementDescriptor } from "./types";
import { cacLtvDescriptor } from "./cac-ltv";
import { cacSalesNtbAsinDescriptor } from "./cac-sales-ntb-asin";
import { cacSalesNtbCampaignDescriptor } from "./cac-sales-ntb-campaign";
import { purchaseOverlapDescriptor } from "./purchase-overlap";
import { crossSellAsinsDescriptor } from "./cross-sell-asins";
import { topSearchTermsDescriptor } from "./top-search-terms";
import { brandedSearchOverlapDescriptor } from "./branded-search-overlap";
import { ntbOverlapDescriptor } from "./ntb-overlap";
import { crossSellCategoryDescriptor } from "./cross-sell-category";
import { cacSalesNtbCategoryDescriptor } from "./cac-sales-ntb-category";
import { funnelStagesDescriptor } from "./funnel-stages";
import { perLineItemCreativeMetricDescriptor } from "./per-line-item-creative-metric";
import { perDateMetricsDescriptor } from "./per-date-metrics";
import { perLineItemMetricDescriptor } from "./per-line-item-metric";

// Registry mapping requirement IDs to descriptors
export const requirementRegistry = {
  "cac-ltv": cacLtvDescriptor,
  "cac-sales-ntb-asin": cacSalesNtbAsinDescriptor,
  "cac-sales-ntb-campaign": cacSalesNtbCampaignDescriptor,
  "purchase-overlap": purchaseOverlapDescriptor,
  "cross-sell-asins": crossSellAsinsDescriptor,
  "top-search-terms": topSearchTermsDescriptor,
  "branded-search-overlap": brandedSearchOverlapDescriptor,
  "ntb-overlap": ntbOverlapDescriptor,
  "cross-sell-category": crossSellCategoryDescriptor,
  "cac-sales-ntb-category": cacSalesNtbCategoryDescriptor,
  "funnel-stages": funnelStagesDescriptor,
  "per-date-metrics": perDateMetricsDescriptor,
  "per-line-item-metric": perLineItemMetricDescriptor,
  "per-line-item-creative-metric": perLineItemCreativeMetricDescriptor,
} as const;

// Derive types from the registry
export type RequirementId = keyof typeof requirementRegistry;

// Automatically infer argument types from the registry
export type RequirementArgsMap = {
  [K in RequirementId]: (typeof requirementRegistry)[K] extends RequirementDescriptor<
    infer TArgs,
    any
  >
    ? TArgs
    : never;
};

// Automatically infer result types from the registry
export type RequirementResultMap = {
  [K in RequirementId]: (typeof requirementRegistry)[K] extends RequirementDescriptor<
    any,
    infer TResult
  >
    ? TResult
    : never;
};

// Helper type to get the correct args type for a given requirement ID
export type ArgsForRequirement<T extends RequirementId> = RequirementArgsMap[T];

// Helper type to get the correct result type for a given requirement ID
export type ResultForRequirement<T extends RequirementId> =
  RequirementResultMap[T];
