import type { CampaignMetricRequest } from "../datasets/campaign-metrics-batched-descriptor";
import type { PerLineItemCreativeMetrics } from "../metrics/types";
import type { RequirementDescriptor } from "./types";

export const perLineItemCreativeMetricDescriptor: RequirementDescriptor<
  CampaignMetricRequest,
  PerLineItemCreativeMetrics
> = {
  id: "per-line-item-creative-metric",
  datasets: ["per-campaign-line-item-creative-metrics"],
};
