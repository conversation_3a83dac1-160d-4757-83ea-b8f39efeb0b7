import { DataRequirement } from "../data-requirement";
import {
  requirementRegistry,
  type RequirementId,
  type ArgsForRequirement,
  type ResultForRequirement,
} from "./registry";

export class RequirementsFactory {
  /**
   * Creates a type-safe requirement based on the requirement ID and args.
   * TypeScript will infer the correct argument and return types.
   */
  static createRequirement<T extends RequirementId>(
    requirementId: T,
    args: ArgsForRequirement<T>
  ): DataRequirement<ResultForRequirement<T>> {
    const descriptor = requirementRegistry[requirementId];

    return new DataRequirement({
      args,
      datasets: descriptor.datasets,
      transformerKey: descriptor.transformerKey,
    });
  }

  /**
   * Gets the descriptor for a requirement ID (useful for metadata)
   */
  static getDescriptor<T extends RequirementId>(requirementId: T) {
    return requirementRegistry[requirementId];
  }

  /**
   * Gets all available requirement IDs
   */
  static getAvailableRequirements(): RequirementId[] {
    return Object.keys(requirementRegistry) as RequirementId[];
  }
}
