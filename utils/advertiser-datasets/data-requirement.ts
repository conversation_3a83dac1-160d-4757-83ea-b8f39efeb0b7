import {
  type DatasetId,
  DatasetRegistry,
  type BaseDatasetArgs,
} from "./datasets";

export interface DataRequirementConfig {
  args: BaseDatasetArgs;
  datasets: DatasetId | DatasetId[]; // Support single or array
  transformerKey?: string; // Optional for raw data
}

export class DataRequirement<T = any> {
  readonly args: BaseDatasetArgs;
  readonly datasets: DatasetId[]; // Always an array internally
  readonly transformerKey?: string;
  readonly id: string;

  constructor(config: DataRequirementConfig) {
    this.args = config.args;
    // Convert single dataset to array
    this.datasets = Array.isArray(config.datasets)
      ? config.datasets
      : [config.datasets];
    this.transformerKey = config.transformerKey;
    this.id = this.generateId();
  }

  private generateId(): string {
    const datasetIds = [...this.datasets].sort().join(",");
    const argsHash = this.generateArgsHash();
    const transformer = this.transformerKey || "raw";
    return `${this.args.advertiserId}:${datasetIds}:${argsHash}:${transformer}`;
  }

  private generateArgsHash(): string {
    const { advertiserId, ...otherArgs } = this.args;
    return JSON.stringify(otherArgs, Object.keys(otherArgs).sort());
  }

  isValid(): boolean {
    return this.datasets.every((datasetId) => {
      const descriptor = DatasetRegistry.get(datasetId);
      if (!descriptor) return false;

      try {
        // Only validate args, don't make API calls
        descriptor.validateArgs(this.args);
        return true;
      } catch {
        return false;
      }
    });
  }

  // Simplified helper methods
  isRawData(): boolean {
    return !this.transformerKey;
  }

  isSingleDataset(): boolean {
    return this.datasets.length === 1;
  }

  equals(other: DataRequirement<T>): boolean {
    return this.id === other.id;
  }
}
