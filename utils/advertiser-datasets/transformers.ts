import type { DatasetId } from "./datasets-config";
import type {
  AdvertiserLevelCacTimeSeries,
  AdvertiserLevelLtvSingleAggregate,
  PerAsinMetrics,
  PerDspCampaignSingleAggregate,
  CampaignFunnelData,
  PerFunnelOverlapMetrics,
  PerAsinPairMetrics,
  PerFunnelSearchTermMetrics,
  AsinCategoryData,
  PerAsinGroupPairMetrics,
  PerAsinGroupMetrics,
  CampaignNamesData,
  PerFunnelStageMetrics as PerFunnelMetrics,
} from "./metrics/types";

type DatasetTransformer<T> = (data: Map<DatasetId, any>) => T;

export const DatasetTransformers: Record<string, DatasetTransformer<any>> = {
  "cac-ltv": cacLtvTransformer,
  "cac-sales-ntb-asin": cacSalesNtbAsinTransformer,
  "cac-sales-ntb-campaign": cacSalesNtbCampaignTransformer,
  "purchase-overlap": purchaseOverlapTransformer,
  "cross-sell-asins": crossSellAsinsTransformer,
  "top-search-terms": topSearchTermsTransformer,
  "branded-search-overlap": brandedSearchOverlapTransformer,
  "ntb-overlap": ntbOverlapTransformer,
  "cross-sell-category": crossSellCategoryTransformer,
  "cac-sales-ntb-category": cacSalesNtbCategoryTransformer,
  "funnel-stages": funnelStagesTransformer,
} as const;

function getCategory(asin: string, asinCategory: AsinCategoryData) {
  return (
    asinCategory.find((item) => item.asins.includes(asin.toLowerCase().trim()))
      ?.name || "N/A"
  );
}

export type CacLtvData = {
  cac: { label: string; value: number }[];
  ltv: number;
};

function cacLtvTransformer(data: Map<DatasetId, any>): CacLtvData {
  const cac = data.get(
    "advertiser-level-cac-metrics"
  ) as AdvertiserLevelCacTimeSeries;
  const ltv = data.get(
    "advertiser-level-ltv-metrics"
  ) as AdvertiserLevelLtvSingleAggregate;

  if (!cac || !ltv) {
    throw new Error("CAC or LTV data not found");
  }

  const cacTransformed = cac.map((item) => ({
    label: item.date,
    value: item.cac,
  }));

  return { cac: cacTransformed, ltv: ltv.ltv };
}

type CacSalesNtbAsinItem = {
  asin: string;
  category: string;
} & {
  [key in keyof PerAsinMetrics]: number;
};

export type CacSalesNtbAsinData = CacSalesNtbAsinItem[];

function cacSalesNtbAsinTransformer(
  data: Map<DatasetId, any>
): CacSalesNtbAsinData {
  console.info("Transforming CAC sales NTB ASIN data");

  const perAsinMetrics = data.get("per-asin-metrics") as Record<
    string,
    PerAsinMetrics
  >;

  const asinCategory = data.get("asin-category") as AsinCategoryData;

  if (!perAsinMetrics || !asinCategory) {
    throw new Error("Per ASIN metrics or ASIN category not found");
  }

  return Object.entries(perAsinMetrics).map(([asin, metrics]) => ({
    asin,
    category: getCategory(asin, asinCategory),
    ...metrics,
  }));
}

type CacSalesNtbCampaignItem = {
  campaignId: string;
  campaignMetadata: {
    campaignName: string;
    funnel: string;
  };
} & {
  [key in keyof PerDspCampaignSingleAggregate]: number;
};

export type CacSalesNtbCampaignData = CacSalesNtbCampaignItem[];

function cacSalesNtbCampaignTransformer(
  data: Map<DatasetId, any>
): CacSalesNtbCampaignData {
  console.info("Transforming CAC sales NTB campaign data");

  const perDspCampaignMetrics = data.get("per-dsp-campaign-metrics") as Record<
    string,
    PerDspCampaignSingleAggregate
  >;

  const campaignFunnelData = data.get(
    "campaign-funnel-data"
  ) as CampaignFunnelData;
  const campaignFunnelDataMap = new Map(
    campaignFunnelData.map((item) => [item.campaignId, item.funnel])
  );

  const campaignNames = data.get("campaign-names") as CampaignNamesData;

  if (!perDspCampaignMetrics || !campaignFunnelDataMap || !campaignNames) {
    throw new Error(
      "Per DSP campaign metrics, campaign funnel data, or campaign names not found in CAC sales NTB campaign transformer"
    );
  }

  return Object.entries(perDspCampaignMetrics).map(([campaignId, metrics]) => ({
    campaignId,
    campaignMetadata: {
      campaignName: campaignNames[campaignId] || "N/A",
      funnel: campaignFunnelDataMap.get(campaignId) || "N/A",
    },
    ...metrics,
  }));
}

export type PurchaseOverlapData = {
  [key: `${number}`]: {
    purchaseRate: number;
    totalPurchases: number;
    impressionUsers: number;
    purchaseUsers: number;
  };
};

function purchaseOverlapTransformer(
  data: Map<DatasetId, any>
): PurchaseOverlapData {
  const perFunnelOverlapMetrics = data.get(
    "per-funnel-overlap-metrics"
  ) as Record<string, PerFunnelOverlapMetrics>;

  return Object.entries(perFunnelOverlapMetrics).reduce(
    (acc, [key, metrics]) => {
      acc[key as `${number}`] = {
        purchaseRate: metrics.purchaseRate,
        totalPurchases: metrics.totalPurchases,
        impressionUsers: metrics.impressionUsers,
        purchaseUsers: metrics.purchaseUsers,
      };
      return acc;
    },
    {} as PurchaseOverlapData
  );
}

export type CrossSellAsinsData = {
  key: string;
  leadAsin: string;
  leadCategory: string | undefined;
  followUpAsin: string;
  followUpCategory: string | undefined;
  leadPurchases: number;
  followUpPurchases: number;
  overlap: number | undefined;
}[];

function crossSellAsinsTransformer(
  data: Map<DatasetId, any>
): CrossSellAsinsData {
  const perAsinPairMetrics = data.get("per-asin-pair-metrics") as Record<
    string,
    Record<string, PerAsinPairMetrics>
  >;
  const asinCategory = data.get("asin-category") as AsinCategoryData;

  if (!perAsinPairMetrics || !asinCategory) {
    throw new Error("Per ASIN pair metrics or ASIN category not found");
  }

  return Object.entries(perAsinPairMetrics)
    .map(([leadAsin, followUpMetrics]) => {
      return Object.entries(followUpMetrics).map(([followUpAsin, metrics]) => {
        return {
          key: `${leadAsin}-${followUpAsin}`,
          leadAsin,
          leadCategory: getCategory(leadAsin, asinCategory),
          followUpAsin,
          followUpCategory: getCategory(followUpAsin, asinCategory),
          leadPurchases: metrics.totalPurchases,
          followUpPurchases: metrics.followPurchases,
          overlap: metrics.overlapPercentage,
        };
      });
    })
    .flat();
}

export type TopSearchTermsData = {
  funnel: string;
  searchTerm: string;
  ntbPurchaseUsers: number;
  purchases: number;
  ntbPurchases: number;
  purchaseUsers: number;
  searchUsers: number;
}[];

const funnelMap = {
  "16": "STV",
  "8": "TOF",
  "4": "SPO",
  "2": "MOF",
  "1": "BOF",
} as const;

function topSearchTermsTransformer(
  data: Map<DatasetId, any>
): TopSearchTermsData {
  console.info("Transforming top search terms data");

  const perFunnelSearchTermMetrics = data.get(
    "per-funnel-search-term-metrics"
  ) as Record<string, Record<string, PerFunnelSearchTermMetrics>>; // { funnel: { searchTerm: PerFunnelSearchTermMetrics } }

  if (!perFunnelSearchTermMetrics) {
    throw new Error(
      "Per funnel search term metrics not found in top search terms transformer"
    );
  }

  return Object.entries(perFunnelSearchTermMetrics)
    .map(([funnel, data]) => {
      const funnelName = funnelMap[funnel as keyof typeof funnelMap] || "N/A";
      return [funnelName, data] as const;
    })
    .map(([funnelName, data]) => {
      return Object.entries(data).map(([searchTerm, metrics]) => {
        return {
          funnel: funnelName, //funnelMap[funnel as keyof typeof funnelMap],
          searchTerm,
          ntbPurchaseUsers: metrics.ntbPurchaseUsers,
          purchases: metrics.purchases,
          ntbPurchases: metrics.ntbPurchases,
          purchaseUsers: metrics.purchaseUsers,
          searchUsers: metrics.searchUsers,
        };
      });
    })
    .flat();
}

export type BrandedSearchOverlapData = {
  [key: `${number}`]: {
    brandedSearches: number;
    influencedSearchPercentage: number;
  };
};

function brandedSearchOverlapTransformer(
  data: Map<DatasetId, any>
): BrandedSearchOverlapData {
  console.info("Transforming branded search overlap data");

  const perFunnelOverlapMetrics = data.get(
    "per-funnel-overlap-metrics"
  ) as Record<string, PerFunnelOverlapMetrics>;

  if (!perFunnelOverlapMetrics) {
    throw new Error(
      "Per funnel overlap metrics not found in branded search overlap transformer"
    );
  }

  return Object.entries(perFunnelOverlapMetrics).reduce(
    (acc, [key, metrics]) => {
      acc[key as `${number}`] = {
        brandedSearches: metrics.brandedSearches,
        influencedSearchPercentage: metrics.influencedSearchPercentage,
      };
      return acc;
    },
    {} as BrandedSearchOverlapData
  );
}

export type NTBOverlapData = {
  [key: `${number}`]: {
    ntbPurchasePercentage: number;
    ntbPurchaserPercentage: number;
    ntbPurchases: number;
    ntbUsers: number;
  };
};

function ntbOverlapTransformer(data: Map<DatasetId, any>): NTBOverlapData {
  const perFunnelOverlapMetrics = data.get(
    "per-funnel-overlap-metrics"
  ) as Record<string, PerFunnelOverlapMetrics>;

  return Object.entries(perFunnelOverlapMetrics).reduce(
    (acc, [key, metrics]) => {
      acc[key as `${number}`] = {
        ntbPurchasePercentage: metrics.ntbPurchasePercentage,
        ntbPurchaserPercentage: metrics.ntbPurchaserPercentage,
        ntbPurchases: metrics.ntbPurchases,
        ntbUsers: metrics.ntbUsers,
      };
      return acc;
    },
    {} as NTBOverlapData
  );
}

export type CrossSellCategoryData = {
  key: string;
  leadCategory: string;
  followUpCategory: string;
  leadPurchases: number;
  followUpPurchases: number;
  overlap: number;
}[];

function crossSellCategoryTransformer(
  data: Map<DatasetId, any>
): CrossSellCategoryData {
  console.info("Transforming cross sell category data");

  const perAsinGroupPairMetrics = data.get(
    "per-asin-group-pair-metrics"
  ) as PerAsinGroupPairMetrics;

  if (!perAsinGroupPairMetrics) {
    throw new Error(
      "Per ASIN group pair metrics not found in cross sell category transformer"
    );
  }

  return Object.entries(perAsinGroupPairMetrics)
    .map(([leadCategory, data]) => {
      return Object.entries(data).map(([followUpCategory, metrics]) => {
        return {
          key: `${leadCategory}-${followUpCategory}`,
          leadCategory,
          followUpCategory,
          leadPurchases: metrics.totalPurchases,
          followUpPurchases: metrics.followPurchases,
          overlap: metrics.overlapPercentage,
        };
      });
    })
    .flat();
}

export type CacSalesNtbCategoryData = {
  category: string;
  ntbPurchaseUsers: number;
  totalProductSales: number;
  totalPurchaseUsers: number;
  ntbProductSales: number;
  cac: number;
}[];

function cacSalesNtbCategoryTransformer(
  data: Map<DatasetId, any>
): CacSalesNtbCategoryData {
  console.info("Transforming CAC sales NTB category data");

  const perAsinGroupMetrics = data.get(
    "per-asin-group-metrics"
  ) as PerAsinGroupMetrics;

  if (!perAsinGroupMetrics) {
    throw new Error(
      "Per ASIN group metrics not found in CAC sales NTB category transformer"
    );
  }

  return Object.entries(perAsinGroupMetrics).map(([category, metrics]) => {
    return {
      category,
      ntbPurchaseUsers: metrics.ntbPurchaseUsers,
      totalProductSales: metrics.totalProductSales,
      totalPurchaseUsers: metrics.totalPurchaseUsers,
      ntbProductSales: metrics.ntbProductSales,
      cac: metrics.cac,
    };
  });
}

export type FunnelStagesData = {
  [key: string]: {
    cost: number;
    gigiTotalProductSales: number;
    gigiTotalPurchaseUsers: number;
    gigiNtbPurchaseUsers: number;
    linearTouchTotalProductSales: number;
    linearTouchTotalPurchaseUsers: number;
    linearTouchNtbPurchaseUsers: number;
    firstTouchTotalProductSales: number;
    firstTouchTotalPurchaseUsers: number;
    firstTouchNtbPurchaseUsers: number;
    lastTouchTotalProductSales: number;
    lastTouchTotalPurchaseUsers: number;
    lastTouchNtbPurchaseUsers: number;
  };
};

function funnelStagesTransformer(data: Map<DatasetId, any>): FunnelStagesData {
  const perFunnelMetrics = data.get(
    "per-funnel-metrics"
  ) as Record<string, PerFunnelMetrics>;

  return Object.entries(perFunnelMetrics).reduce(
    (acc, [key, metrics]) => {
        acc[funnelMap[key as keyof typeof funnelMap]] = {
          cost: metrics.cost ?? 0,
          gigiTotalProductSales: metrics.gigiTotalProductSales ?? 0,
          gigiTotalPurchaseUsers: metrics.gigiTotalPurchaseUsers ?? 0,
          gigiNtbPurchaseUsers: metrics.gigiNtbPurchaseUsers ?? 0,
          linearTouchTotalProductSales: metrics.linearTouchTotalProductSales ?? 0,
          linearTouchTotalPurchaseUsers: metrics.linearTouchTotalPurchaseUsers ?? 0,
          linearTouchNtbPurchaseUsers: metrics.linearTouchNtbPurchaseUsers ?? 0,
          firstTouchTotalProductSales: metrics.firstTouchTotalProductSales ?? 0,
          firstTouchTotalPurchaseUsers: metrics.firstTouchTotalPurchaseUsers ?? 0,
          firstTouchNtbPurchaseUsers: metrics.firstTouchNtbPurchaseUsers ?? 0,
          lastTouchTotalProductSales: metrics.lastTouchTotalProductSales ?? 0,
          lastTouchTotalPurchaseUsers: metrics.lastTouchTotalPurchaseUsers ?? 0,
          lastTouchNtbPurchaseUsers: metrics.lastTouchNtbPurchaseUsers ?? 0,
        };
      return acc;
    },
    {} as FunnelStagesData
  );
}
