import type { DataRequirement } from "./data-requirement";
import {
  DatasetRegistry,
  type DatasetId,
  type BaseDatasetArgs,
} from "./datasets";
import { DatasetTransformers } from "./transformers";

export class AdvertiserDatasetManager {
  private advertiserId: string;
  private cacheTtlMs: number;
  private maxDatasetCacheSize: number;
  private maxRequirementCacheSize: number;

  private datasetCache = new Map<string, CacheEntry>();
  private requirementCache = new Map<string, CacheEntry>();
  private loadingPromises = new Map<string, Promise<any>>();

  constructor(
    advertiserId: string,
    cacheTtlMinutes: number,
    maxDatasetCacheSize: number = 100,
    maxRequirementCacheSize: number = 50
  ) {
    this.advertiserId = advertiserId;
    this.cacheTtlMs = cacheTtlMinutes * 60 * 1000;
    this.maxDatasetCacheSize = maxDatasetCacheSize;
    this.maxRequirementCacheSize = maxRequirementCacheSize;
  }

  async fulfillRequirement<T>(requirement: DataRequirement<T>): Promise<T> {
    if (!requirement.isValid()) {
      throw new Error(`Invalid data requirement: ${requirement.id}`);
    }

    // Check cache first (for both raw and transformed)
    const cached = this.getFromRequirementCache(requirement.id);
    if (cached) return cached;

    // Unified data loading for both raw and transformed
    const datasets = await this.loadDatasets(
      requirement.datasets,
      requirement.args
    );

    // Create dataMap once for reuse
    const dataMap = new Map(
      requirement.datasets.map((datasetId, index) => [
        datasetId,
        datasets[index],
      ])
    );

    let result: T;

    if (requirement.isRawData()) {
      // Handle raw data
      if (requirement.isSingleDataset()) {
        result = datasets[0] as T;
      } else {
        // Multiple datasets - return the map
        result = dataMap as T;
      }
    } else {
      // Handle transformed data
      const transformer = DatasetTransformers[requirement.transformerKey!];
      if (!transformer) {
        throw new Error(`Transformer not found: ${requirement.transformerKey}`);
      }

      result = transformer(dataMap);
    }

    // Cache the result (same cache for both raw and transformed)
    this.setRequirementCache(requirement.id, result);
    return result;
  }

  // Unified dataset loading method
  private async loadDatasets(
    datasetIds: DatasetId[],
    args: BaseDatasetArgs
  ): Promise<any[]> {
    const datasetPromises = datasetIds.map((datasetId) =>
      this.loadDataset(datasetId, args)
    );
    return await Promise.all(datasetPromises);
  }

  private async loadDataset(
    datasetId: DatasetId,
    args: BaseDatasetArgs
  ): Promise<any> {
    const descriptor = DatasetRegistry.get(datasetId);
    if (!descriptor) {
      throw new Error(`Dataset descriptor not found: ${datasetId}`);
    }

    const cacheKey = descriptor.generateCacheKey(args);

    // Check cache
    const cached = this.getDatasetFromCache(cacheKey);
    if (cached) return cached;

    // Check loading promises
    if (this.loadingPromises.has(cacheKey)) {
      return await this.loadingPromises.get(cacheKey)!;
    }

    // Start loading using descriptor
    const loadingPromise = descriptor.fetch(args);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const data = await loadingPromise;
      this.setDatasetCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  // Cache management with size limits
  private setDatasetCache(key: string, data: any): void {
    // Clean up if approaching limit
    if (this.datasetCache.size >= this.maxDatasetCacheSize) {
      this.cleanOldestEntries(
        this.datasetCache,
        Math.floor(this.maxDatasetCacheSize * 0.7)
      );
    }

    this.datasetCache.set(key, { data, timestamp: Date.now() });
  }

  private setRequirementCache(key: string, data: any): void {
    // Clean up if approaching limit
    if (this.requirementCache.size >= this.maxRequirementCacheSize) {
      this.cleanOldestEntries(
        this.requirementCache,
        Math.floor(this.maxRequirementCacheSize * 0.7)
      );
    }

    this.requirementCache.set(key, { data, timestamp: Date.now() });
  }

  private cleanOldestEntries(
    cache: Map<string, CacheEntry>,
    targetSize: number
  ): void {
    const entries = Array.from(cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    const toDelete = entries.slice(0, entries.length - targetSize);
    toDelete.forEach(([key]) => cache.delete(key));
  }

  // Other cache methods remain similar...
  private getDatasetFromCache(key: string): any | null {
    return this.getCacheValue(this.datasetCache, key);
  }

  private getFromRequirementCache(key: string): any | null {
    return this.getCacheValue(this.requirementCache, key);
  }

  private getCacheValue(
    cache: Map<string, CacheEntry>,
    key: string
  ): any | null {
    const entry = cache.get(key);
    if (!entry) return null;

    const isExpired = Date.now() - entry.timestamp > this.cacheTtlMs;
    if (isExpired) {
      cache.delete(key);
      return null;
    }

    return entry.data;
  }

  // Debug/monitoring methods
  getCacheStats() {
    return {
      advertiserId: this.advertiserId,
      datasetCache: {
        size: this.datasetCache.size,
        maxSize: this.maxDatasetCacheSize,
      },
      requirementCache: {
        size: this.requirementCache.size,
        maxSize: this.maxRequirementCacheSize,
      },
      loadingPromises: this.loadingPromises.size,
    };
  }
}
interface CacheEntry {
  data: any;
  timestamp: number;
}
