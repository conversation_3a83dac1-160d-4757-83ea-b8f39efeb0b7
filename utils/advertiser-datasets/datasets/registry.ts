import type { DatasetId } from "./types";
import type { DatasetDescriptor } from "./base-descriptor";

// Internal dataset registry (not exported from main module)
class DatasetRegistry {
  private static descriptors = new Map<DatasetId, DatasetDescriptor>();

  static register<T extends DatasetDescriptor>(descriptor: T): void {
    this.descriptors.set(descriptor.id, descriptor);
  }

  static get(datasetId: DatasetId): DatasetDescriptor | undefined {
    return this.descriptors.get(datasetId);
  }

  static getAll(): Map<DatasetId, DatasetDescriptor> {
    return new Map(this.descriptors);
  }
}

export { DatasetRegistry };
