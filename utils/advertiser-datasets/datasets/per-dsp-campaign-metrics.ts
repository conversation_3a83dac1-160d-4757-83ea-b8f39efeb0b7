import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerDspCampaignMetrics } from "../metrics/fetch-metrics";
import type { PerDspCampaignSingleAggregate } from "../metrics/types";

export class PerDspCampaignMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-dsp-campaign-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-dsp campaign metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerDspCampaignSingleAggregate> {
    return await fetchPerDspCampaignMetrics(args);
  }
}
