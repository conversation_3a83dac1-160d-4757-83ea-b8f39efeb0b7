import {
  MetricsFormat,
  MetricsGroup,
  type PerLineItemMetrics,
} from "../metrics/types";
import {
  CampaignMetricsBatchedDescriptor,
  type CampaignMetricRequest,
} from "./campaign-metrics-batched-descriptor";

// --- Splitter Implementation ---
function splitBatchResponse(
  batchResponse: PerLineItemMetrics,
  originalRequests: CampaignMetricRequest[]
): PerLineItemMetrics[] {
  return originalRequests.map((request) => {
    const campaignData = batchResponse[request.campaignId];

    if (!campaignData) {
      return {};
    }

    const filteredLineItems: Record<string, Record<string, number>> = {};

    for (const lineItemId of Object.keys(campaignData)) {
      const lineItemData = campaignData[lineItemId];
      if (lineItemData[request.metricName] !== undefined) {
        filteredLineItems[lineItemId] = {
          [request.metricName]: lineItemData[request.metricName],
        };
      }
    }

    return {
      [request.campaignId]: filteredLineItems,
    };
  });
}

// --- Descriptor Configuration and Instantiation ---
export const perLineItemMetricsDescriptor =
  new CampaignMetricsBatchedDescriptor<PerLineItemMetrics>({
    id: "per-campaign-line-item-metrics",
    metricGroup: MetricsGroup.PER_LINE_ITEM_METRICS,
    metricFormat: MetricsFormat.SINGLE_AGGREGATE,
    splitter: splitBatchResponse,
  });
