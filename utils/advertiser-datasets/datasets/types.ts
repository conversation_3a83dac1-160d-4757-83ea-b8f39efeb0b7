// Base argument types
export type BaseDatasetArgs = {
  advertiserId: string;
} & {
  [key: string]: any;
};

export type DateRangeArgs = BaseDatasetArgs & {
  startDate: Date;
  endDate: Date;
};

export type SimpleArgs = BaseDatasetArgs;

export type CampaignIdArgs = BaseDatasetArgs & {
  campaignId: string;
};

export type DatasetId =
  | "per-funnel-overlap-metrics"
  | "per-funnel-search-term-metrics"
  | "per-funnel-metrics"
  | "per-asin-pair-metrics"
  | "per-dsp-campaign-metrics"
  | "per-asin-metrics"
  | "advertiser-level-cac-metrics"
  | "advertiser-level-ltv-metrics"
  | "campaign-funnel-data"
  | "asin-category"
  | "per-asin-group-pair-metrics"
  | "per-asin-group-metrics"
  | "campaign-names"
  | "line-item-names"
  | "creative-names"
  | "per-date-metrics"
  | "per-campaign-line-item-creative-metrics"
  | "campaign-spend"
  | "campaign-summary-metrics"
  | (string & {});
