import { DatasetDescriptor } from "./base-descriptor";
import type { SimpleArgs } from "./types";
import { fetchAsinCategory } from "../metrics/fetch-asin-category";
import type { AsinCategoryData } from "../metrics/types";

export class AsinCategoryDescriptor extends DatasetDescriptor<SimpleArgs> {
  readonly id = "asin-category" as const;

  async doFetch(args: SimpleArgs): Promise<AsinCategoryData> {
    return await fetchAsinCategory(args);
  }
}
