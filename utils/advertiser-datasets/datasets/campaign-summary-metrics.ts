import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import type { CampaignSummaryMetrics } from "../metrics/types";
import { fetchCampaignSummaryMetrics } from "../metrics/fetch-metrics";

export class CampaignSummaryMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "campaign-summary-metrics";

  async doFetch(args: DateRangeArgs): Promise<CampaignSummaryMetrics> {
    return await fetchCampaignSummaryMetrics(args);
  }
}
