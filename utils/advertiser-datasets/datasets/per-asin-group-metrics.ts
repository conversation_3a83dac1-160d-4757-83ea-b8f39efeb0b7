import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerAsinGroupMetrics } from "../metrics/fetch-metrics";
import type { PerAsinGroupMetrics } from "../metrics/types";

export class PerAsinGroupMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-asin-group-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-asin group metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerAsinGroupMetrics> {
    return await fetchPerAsinGroupMetrics(args);
  }
}
