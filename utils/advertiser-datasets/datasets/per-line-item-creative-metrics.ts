import {
  MetricsFormat,
  MetricsGroup,
  type PerLineItemCreativeMetrics,
} from "../metrics/types";
import {
  CampaignMetricsBatchedDescriptor,
  type CampaignMetricRequest,
} from "./campaign-metrics-batched-descriptor";

// --- Splitter Implementation ---
function splitBatchResponse(
  batchResponse: PerLineItemCreativeMetrics,
  originalRequests: CampaignMetricRequest[]
): PerLineItemCreativeMetrics[] {
  return originalRequests.map((request) => {
    const campaignData = batchResponse[request.campaignId];

    if (!campaignData) {
      return {};
    }

    const filteredLineItems: Record<
      string,
      Record<string, Record<string, number>>
    > = {};

    for (const lineItemId of Object.keys(campaignData)) {
      const lineItemData = campaignData[lineItemId];
      const filteredCreatives: Record<string, Record<string, number>> = {};

      for (const creativeId of Object.keys(lineItemData)) {
        const creativeMetrics = lineItemData[creativeId];
        if (creativeMetrics[request.metricName] !== undefined) {
          filteredCreatives[creativeId] = {
            [request.metricName]: creativeMetrics[request.metricName],
          };
        }
      }

      if (Object.keys(filteredCreatives).length > 0) {
        filteredLineItems[lineItemId] = filteredCreatives;
      }
    }

    return {
      [request.campaignId]: filteredLineItems,
    };
  });
}

// --- Descriptor Configuration and Instantiation ---
export const perLineItemCreativeMetricsDescriptor =
  new CampaignMetricsBatchedDescriptor<PerLineItemCreativeMetrics>({
    id: "per-campaign-line-item-creative-metrics",
    metricGroup: MetricsGroup.PER_LINE_ITEM_CREATIVE_METRICS,
    metricFormat: MetricsFormat.SINGLE_AGGREGATE,
    splitter: splitBatchResponse,
  });
