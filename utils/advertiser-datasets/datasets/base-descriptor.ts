import type { BaseDatasetArgs, DatasetId } from "./types";

// Abstract base class following template method pattern
export abstract class DatasetDescriptor<
  TArgs extends BaseDatasetArgs = BaseDatasetArgs
> {
  abstract readonly id: DatasetId;

  // Template method - public interface
  async fetch(args: TArgs): Promise<any> {
    this.validateArgs(args);
    return this.doFetch(args);
  }

  // Abstract methods for subclasses
  abstract doFetch(args: TArgs): Promise<any>;

  // Default cache key generation - can be overridden
  generateCacheKey(args: TArgs): string {
    return `${this.id}:${this.generateArgsHash(args)}`;
  }

  // Default validation - can be overridden
  validateArgs(args: TArgs): void {
    if (!args.advertiserId) {
      throw new Error(`advertiserId is required for dataset ${this.id}`);
    }
  }

  protected generateArgsHash(args: TArgs): string {
    const { advertiserId, ...otherArgs } = args;
    return JSON.stringify(otherArgs, Object.keys(otherArgs).sort());
  }
}
