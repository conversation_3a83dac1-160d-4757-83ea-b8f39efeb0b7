import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerAsinPairMetrics } from "../metrics/fetch-metrics";
import type { PerAsinPairMetrics } from "../metrics/types";

export class PerAsinPairMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-asin-pair-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-asin pair metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerAsinPairMetrics> {
    return await fetchPerAsinPairMetrics(args);
  }
}
