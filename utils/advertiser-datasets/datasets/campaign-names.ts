import { DatasetDescriptor } from "./base-descriptor";
import type { SimpleArgs } from "./types";
import { fetchCampaignNames } from "../metrics/fetch-campaign-names";
import type { CampaignNamesData } from "../metrics/types";

export class CampaignNamesDescriptor extends DatasetDescriptor<SimpleArgs> {
  readonly id = "campaign-names" as const;

  async doFetch(args: SimpleArgs): Promise<CampaignNamesData> {
    return await fetchCampaignNames(args);
  }
}
