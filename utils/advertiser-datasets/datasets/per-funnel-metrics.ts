import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerFunnelMetrics } from "../metrics/fetch-metrics";
import type { PerFunnelStageMetrics as PerFunnelMetrics } from "../metrics/types";

export class PerFunnelMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-funnel-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error("Start and end date are required for per-funnel metrics");
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerFunnelMetrics> {
    return await fetchPerFunnelMetrics(args);
  }
}
