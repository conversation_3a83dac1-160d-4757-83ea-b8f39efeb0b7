import type { LineItemNamesData } from "../metrics/types";
import { DatasetDescriptor } from "./base-descriptor";
import type { SimpleArgs } from "./types";
import { fetchLineItemNames } from "../metrics/fetch-line-item-names";

export class LineItemNamesDescriptor extends DatasetDescriptor<SimpleArgs> {
  readonly id = "line-item-names" as const;

  doFetch(args: SimpleArgs): Promise<LineItemNamesData> {
    return fetchLineItemNames(args);
  }
}
