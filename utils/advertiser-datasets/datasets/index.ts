// Export types and base classes
export type {
  BaseDatasetArgs,
  DateRangeArgs,
  CampaignIdArgs,
  SimpleArgs,
  DatasetId,
} from "./types";
export { DatasetDescriptor } from "./base-descriptor";

// Import all dataset descriptors
import { PerFunnelOverlapMetricsDescriptor } from "./per-funnel-overlap-metrics";
import { PerFunnelSearchTermMetricsDescriptor } from "./per-funnel-search-term-metrics";
import { PerFunnelMetricsDescriptor } from "./per-funnel-metrics";
import { PerAsinPairMetricsDescriptor } from "./per-asin-pair-metrics";
import { PerDspCampaignMetricsDescriptor } from "./per-dsp-campaign-metrics";
import { PerAsinMetricsDescriptor } from "./per-asin-metrics";
import { AdvertiserLevelCacMetricsDescriptor } from "./advertiser-level-cac-metrics";
import { AdvertiserLevelLtvMetricsDescriptor } from "./advertiser-level-ltv-metrics";
import { PerAsinGroupPairMetricsDescriptor } from "./per-asin-group-pair-metrics";
import { PerAsinGroupMetricsDescriptor } from "./per-asin-group-metrics";
import { CampaignFunnelDataDescriptor } from "./campaign-funnel-data";
import { AsinCategoryDescriptor } from "./asin-category";
import { CampaignNamesDescriptor } from "./campaign-names";
import { LineItemNamesDescriptor } from "./line-item-names";
import { CreativeNamesDescriptor } from "./creative-names";
import { perDateMetricsDescriptor } from "./per-date-metrics";
import { perLineItemMetricsDescriptor } from "./per-line-item-metrics";
import { perLineItemCreativeMetricsDescriptor } from "./per-line-item-creative-metrics";
import { CampaignSpendDescriptor } from "./campaign-spend";
import { CampaignSummaryMetricsDescriptor } from "./campaign-summary-metrics";

// Import registry (internal use only)
import { DatasetRegistry } from "./registry";

// Register all descriptors
DatasetRegistry.register(new PerFunnelOverlapMetricsDescriptor());
DatasetRegistry.register(new PerFunnelSearchTermMetricsDescriptor());
DatasetRegistry.register(new PerFunnelMetricsDescriptor());
DatasetRegistry.register(new PerAsinPairMetricsDescriptor());
DatasetRegistry.register(new PerDspCampaignMetricsDescriptor());
DatasetRegistry.register(new PerAsinMetricsDescriptor());
DatasetRegistry.register(new AdvertiserLevelCacMetricsDescriptor());
DatasetRegistry.register(new AdvertiserLevelLtvMetricsDescriptor());
DatasetRegistry.register(new PerAsinGroupPairMetricsDescriptor());
DatasetRegistry.register(new PerAsinGroupMetricsDescriptor());
DatasetRegistry.register(new CampaignFunnelDataDescriptor());
DatasetRegistry.register(new AsinCategoryDescriptor());
DatasetRegistry.register(new CampaignNamesDescriptor());
DatasetRegistry.register(new LineItemNamesDescriptor());
DatasetRegistry.register(new CreativeNamesDescriptor());
DatasetRegistry.register(perDateMetricsDescriptor);
DatasetRegistry.register(perLineItemMetricsDescriptor);
DatasetRegistry.register(perLineItemCreativeMetricsDescriptor);
DatasetRegistry.register(new CampaignSpendDescriptor());
DatasetRegistry.register(new CampaignSummaryMetricsDescriptor());

// Export registry for internal use by other modules in the advertiser-datasets package
export { DatasetRegistry };
