import {
  MetricsFormat,
  MetricsGroup,
  type PerDateMetrics,
} from "../metrics/types";
import {
  CampaignMetricsBatchedDescriptor,
  type CampaignMetricRequest,
} from "./campaign-metrics-batched-descriptor";

// --- Splitter Implementation ---
function splitBatchResponse(
  batchResponse: {
    totalForPeriod: Record<string, number>;
    timeSeries: Array<{ date: Date } & Record<string, number>>;
  },
  originalRequests: CampaignMetricRequest[]
): PerDateMetrics[] {
  return originalRequests.map((request) => ({
    totalForPeriod: {
      [request.metricName]: batchResponse.totalForPeriod[request.metricName],
    },
    timeSeries: batchResponse.timeSeries.map((item) => {
      return {
        date: item.date,
        [request.metricName]: item[request.metricName],
      } as { date: Date } & Record<string, number>;
    }),
  }));
}

// --- Descriptor Configuration and Instantiation ---
export const perDateMetricsDescriptor =
  new CampaignMetricsBatchedDescriptor<PerDateMetrics>({
    id: "per-date-metrics",
    metricGroup: MetricsGroup.PER_DATE_METRICS,
    metricFormat: MetricsFormat.TIME_SERIES_AND_AGGREGATE,
    splitter: splitBatchResponse,
  });
