import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerFunnelOverlapMetrics } from "../metrics/fetch-metrics";
import type { PerFunnelOverlapMetrics } from "../metrics/types";

export class PerFunnelOverlapMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-funnel-overlap-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-funnel overlap metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerFunnelOverlapMetrics> {
    return await fetchPerFunnelOverlapMetrics(args);
  }
}
