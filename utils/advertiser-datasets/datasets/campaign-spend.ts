import { DatasetDescriptor } from "./base-descriptor";
import type { CampaignSpendData } from "../metrics/types";
import { fetchCampaignSpendMetrics } from "../metrics/fetch-metrics";
import type { DateRangeArgs } from "./types";

export class CampaignSpendDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "campaign-spend" as const;

  async doFetch(args: DateRangeArgs): Promise<CampaignSpendData> {
    return await fetchCampaignSpendMetrics(args);
  }
}
