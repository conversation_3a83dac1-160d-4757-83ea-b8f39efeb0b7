import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerAsinGroupPairMetrics } from "../metrics/fetch-metrics";
import type { PerAsinGroupPairMetrics } from "../metrics/types";

export class PerAsinGroupPairMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-asin-group-pair-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-asin group pair metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerAsinGroupPairMetrics> {
    return await fetchPerAsinGroupPairMetrics(args);
  }
}
