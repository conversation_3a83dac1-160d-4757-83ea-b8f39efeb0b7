import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerAsinMetrics } from "../metrics/fetch-metrics";
import type { PerAsinMetrics } from "../metrics/types";

export class PerAsinMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-asin-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error("Start and end date are required for per-asin metrics");
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerAsinMetrics> {
    return await fetchPerAsinMetrics(args);
  }
}
