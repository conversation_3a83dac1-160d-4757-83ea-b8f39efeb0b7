import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchPerFunnelSearchTermMetrics } from "../metrics/fetch-metrics";
import type { PerFunnelSearchTermMetrics } from "../metrics/types";

export class PerFunnelSearchTermMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "per-funnel-search-term-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for per-funnel search term metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<PerFunnelSearchTermMetrics> {
    return await fetchPerFunnelSearchTermMetrics(args);
  }
}
