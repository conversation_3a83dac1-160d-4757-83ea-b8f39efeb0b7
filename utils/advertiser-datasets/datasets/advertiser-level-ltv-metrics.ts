import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchAdvertiserLevelLtvMetrics } from "../metrics/fetch-metrics";
import type { AdvertiserLevelLtvSingleAggregate } from "../metrics/types";

export class AdvertiserLevelLtvMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "advertiser-level-ltv-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for advertiser-level LTV metrics"
      );
    }
  }

  async doFetch(
    args: DateRangeArgs
  ): Promise<AdvertiserLevelLtvSingleAggregate> {
    return await fetchAdvertiserLevelLtvMetrics(args);
  }
}
