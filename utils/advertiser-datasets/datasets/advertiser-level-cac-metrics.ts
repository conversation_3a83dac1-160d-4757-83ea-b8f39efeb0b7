import { DatasetDescriptor } from "./base-descriptor";
import type { DateRangeArgs } from "./types";
import { fetchAdvertiserLevelCacMetrics } from "../metrics/fetch-metrics";
import type { AdvertiserLevelCacTimeSeries } from "../metrics/types";

export class AdvertiserLevelCacMetricsDescriptor extends DatasetDescriptor<DateRangeArgs> {
  readonly id = "advertiser-level-cac-metrics" as const;

  override validateArgs(args: DateRangeArgs): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(
        "Start and end date are required for advertiser-level CAC metrics"
      );
    }
  }

  async doFetch(args: DateRangeArgs): Promise<AdvertiserLevelCacTimeSeries> {
    return await fetchAdvertiserLevelCacMetrics(args);
  }

  generateCacheKey(args: DateRangeArgs): string {
    return `${args.advertiserId}:${
      this.id
    }:${args.startDate.toISOString()}:${args.endDate.toISOString()}`;
  }
}
