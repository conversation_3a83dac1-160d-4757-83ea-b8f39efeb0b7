import type { CreativeNamesData } from "../metrics/types";
import { DatasetDescriptor } from "./base-descriptor";
import type { CampaignIdArgs } from "./types";
import { fetchAdGroupCreativeAssociations } from "../metrics/fetch-creative-associations";
import { fetchAdCreativesNames } from "../metrics/fetch-ad-creatives-names";
import { fetchLineItemNames } from "../metrics/fetch-line-item-names";

export class CreativeNamesDescriptor extends DatasetDescriptor<CampaignIdArgs> {
  readonly id = "creative-names" as const;

  override validateArgs(args: CampaignIdArgs): void {
    super.validateArgs(args);

    if (!args.campaignId) {
      throw new Error("campaignId is required");
    }
  }

  async doFetch(args: CampaignIdArgs): Promise<CreativeNamesData> {
    console.info(
      `Fetching creative names for advertiser ${args.advertiserId} campaign ${args.campaignId}`
    );

    const [lineItemNames, creativeAssociations] = await Promise.all([
      fetchLineItemNames(args),
      fetchAdGroupCreativeAssociations(args),
    ]);

    const campaignLineItemIds = Object.keys(lineItemNames);

    console.info(
      `Found ${campaignLineItemIds.length} line item ids for campaign ${args.campaignId}`
    );

    const adCreativeIds = campaignLineItemIds.flatMap((lineItemId) => {
      return Array.from(creativeAssociations[lineItemId]);
    });

    const uniqueAdCreativeIds = [...new Set(adCreativeIds)];

    console.log(uniqueAdCreativeIds);

    console.info(
      `Found ${uniqueAdCreativeIds.length} unique ad creative ids for campaign ${args.campaignId}`
    );

    console.info(
      `Fetching ${uniqueAdCreativeIds.length} ad creative names for campaign ${args.campaignId}`
    );

    return await fetchAdCreativesNames({
      advertiserId: args.advertiserId,
      adCreativeIds: uniqueAdCreativeIds,
    });
  }
}
