import { DatasetDescriptor } from "./base-descriptor";
import type { SimpleArgs } from "./types";
import { fetchCampaignFunnelData } from "../metrics/fetch-campaign-funnel";
import type { CampaignFunnelData } from "../metrics/types";

export class CampaignFunnelDataDescriptor extends DatasetDescriptor<SimpleArgs> {
  readonly id = "campaign-funnel-data" as const;

  async doFetch(args: SimpleArgs): Promise<CampaignFunnelData> {
    return await fetchCampaignFunnelData(args);
  }
}
