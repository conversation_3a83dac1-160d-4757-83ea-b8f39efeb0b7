import { DatasetDescriptor } from "./base-descriptor";
import { RequestBatcher, type BatchProcessor } from "../../batching";
import { getMetricsFromGroup } from "../metrics/fetch-metrics";
import {
  MetricsTag,
  type MetricsGroup,
  type MetricsFormat,
} from "../metrics/types";
import type { BaseDatasetArgs } from "./types";
import type { DateRangeArgs } from "../requirements/types";

// --- Campaign Metrics Request and Response Types ---
export type CampaignMetricRequest = BaseDatasetArgs &
  DateRangeArgs & {
    campaignId: string;
    lineItemIds?: string[];
    creativeIds?: string[];
    metricName: string;
    viewType: "day" | "week";
  };

export type CampaignMetricBatchRequest = BaseDatasetArgs &
  DateRangeArgs & {
    campaignId: string;
    lineItemIds?: string[];
    creativeIds?: string[];
    metricNames: string[];
    viewType: "day" | "week";
  };

// --- Configuration for Campaign Metrics Descriptor ---
export type CampaignMetricsConfig<TResponse> = {
  id: string;
  metricGroup: MetricsGroup;
  metricFormat: MetricsFormat;
  splitter: (
    batchResponse: any,
    originalRequests: CampaignMetricRequest[]
  ) => TResponse[];
};

export class CampaignMetricsBatchedDescriptor<
  TResponse
> extends DatasetDescriptor<CampaignMetricRequest> {
  readonly id: string;
  private requestBatcher: RequestBatcher<
    CampaignMetricRequest,
    CampaignMetricBatchRequest,
    any,
    TResponse
  >;
  private config: CampaignMetricsConfig<TResponse>;

  constructor(config: CampaignMetricsConfig<TResponse>) {
    super();
    this.id = config.id;
    this.config = config;

    const batchProcessor: BatchProcessor<
      CampaignMetricRequest,
      CampaignMetricBatchRequest,
      any,
      TResponse
    > = {
      generateBatchingKey: (request) => this.generateBatchingKey(request),
      combineToBatchRequest: (requests) => this.combineToBatchRequest(requests),
      executeBatch: (batchRequest) => this.executeBatch(batchRequest),
      splitBatchResponse: (batchResponse, originalRequests) =>
        this.config.splitter(batchResponse, originalRequests),
    };

    this.requestBatcher = new RequestBatcher(batchProcessor, {
      batchWindowMs: 10,
      maxBatchSize: 50,
    });
  }

  // --- Campaign metrics validation ---
  override validateArgs(args: CampaignMetricRequest): void {
    super.validateArgs(args);
    if (!args.startDate || !args.endDate) {
      throw new Error(`Start and end date are required for ${this.config.id}`);
    }
    if (!args.campaignId) {
      throw new Error("campaignId is required");
    }
    if (!args.metricName) {
      throw new Error("metricName is required");
    }
  }

  // --- Batch Processor Implementation ---
  private generateBatchingKey(request: CampaignMetricRequest): string {
    return `${
      request.advertiserId
    }:${request.startDate.toISOString()}:${request.endDate.toISOString()}:${
      request.campaignId
    }`;
  }

  private combineToBatchRequest(
    requests: CampaignMetricRequest[]
  ): CampaignMetricBatchRequest {
    if (requests.length === 0) {
      throw new Error("Cannot create batch request from empty requests array");
    }
    const baseRequest = requests[0];
    const metricNames = requests.map((req) => req.metricName);

    return {
      advertiserId: baseRequest.advertiserId,
      startDate: baseRequest.startDate,
      endDate: baseRequest.endDate,
      campaignId: baseRequest.campaignId,
      lineItemIds: baseRequest.lineItemIds,
      creativeIds: baseRequest.creativeIds,
      metricNames,
      viewType: baseRequest.viewType,
    };
  }

  private async executeBatch(
    batchRequest: CampaignMetricBatchRequest
  ): Promise<any> {
    const { campaignId, lineItemIds, creativeIds } = batchRequest;

    const lineItemFilters = lineItemIds
      ? lineItemIds.map((lineItemId) => ({ lineItemId }))
      : [];

    const creativeFilters = creativeIds
      ? creativeIds.map((creativeId) => ({ creativeId }))
      : [];

    const totalFilters = creativeFilters.length + lineItemFilters.length;
    const multiGroupingFilters =
      totalFilters > 0
        ? [...lineItemFilters, ...creativeFilters]
        : [{ campaignId }];

    const tagMap: Record<string, MetricsTag> = {
      day: MetricsTag.DAILY,
      week: MetricsTag.WEEKLY,
    };

    const tag = tagMap[batchRequest.viewType];

    if (!tag) {
      throw new Error(`Invalid view type: ${batchRequest.viewType}`);
    }

    const requestArgs: any = {
      advertiserId: batchRequest.advertiserId,
      startDate: batchRequest.startDate,
      endDate: batchRequest.endDate,
      tag,
      groupName: this.config.metricGroup,
      format: this.config.metricFormat,
      metricNames: batchRequest.metricNames,
      multiGroupingFilters,
    };

    return getMetricsFromGroup(requestArgs);
  }

  // --- Fetch method ---
  async doFetch(args: CampaignMetricRequest): Promise<TResponse> {
    return this.requestBatcher.handleRequest(args);
  }

  // --- Monitoring ---
  getBatchingInfo() {
    return {
      activeBatches: this.requestBatcher.getActiveBatchCount(),
      batchDetails: this.requestBatcher.getBatchingInfo(),
      batchConfig: this.requestBatcher.getBatchConfig(),
    };
  }
}
