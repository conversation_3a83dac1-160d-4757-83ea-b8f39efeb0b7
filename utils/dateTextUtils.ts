export function timeAgoText(date?: Date) {
  if (!date) return "";

  const withPlural = (value: number, unit: string) => {
    return value === 1 ? unit : `${unit}s`;
  };

  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());

  // Convert to seconds
  const seconds = Math.floor(diffTime / 1000);

  if (seconds < 60) {
    return `less than a minute ago`;
  }

  // Convert to minutes
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes} ${withPlural(minutes, "minute")} ago`;
  }

  // Convert to hours
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours} ${withPlural(hours, "hour")} ago`;
  }

  // Convert to days
  const days = Math.floor(hours / 24);
  return `${days} ${withPlural(days, "day")} ago`;
}

export function dateText(date?: Date) {
  if (!date) return "";

  const withinWeek =
    Math.abs(date.getTime() - new Date().getTime()) < 1000 * 60 * 60 * 24 * 7;

  if (withinWeek) {
    return date.toLocaleString(undefined, {
      weekday: "short",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  return date.toLocaleString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export function dateFullNumericText(date?: Date) {
  if (!date) return "";

  return date.toLocaleString(undefined, {
    year: "2-digit",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}
