import type {
  ConnectionState,
  ConnectionHookType,
  ConnectionHookCallback,
} from "./types";

export class EventSourceConnectionManager {
  private _state = ref<ConnectionState>({
    status: "disconnected",
    source: null,
    clientId: crypto.randomUUID(),
  });

  private hooks = new Map<ConnectionHookType, Set<any>>();
  private reconnectInterval: ReturnType<typeof setInterval> | null = null;
  private reconnectIntervalMs = 3000;

  constructor(clientId?: string) {
    if (clientId) {
      this._state.value.clientId = clientId;
    }

    // Initialize hook sets
    this.hooks.set("onConnect", new Set());
    this.hooks.set("onDisconnect", new Set());
    this.hooks.set("onError", new Set());
  }

  get state(): Readonly<Ref<ConnectionState>> {
    return readonly(this._state);
  }

  connect(url: string): void {
    if (
      this._state.value.status === "connecting" ||
      this._state.value.status === "connected"
    ) {
      return;
    }

    if (
      this._state.value.source &&
      this._state.value.source.readyState === EventSource.OPEN
    ) {
      this._state.value.status = "connected";
      return;
    }

    if (
      this._state.value.source &&
      this._state.value.source.readyState === EventSource.CLOSED
    ) {
      this._state.value.source.onopen = null;
      this._state.value.source.onerror = null;
      this._state.value.source = null;
    }

    this._state.value.status = "connecting";
    console.info("Attempting to connect to server events...");

    try {
      this._state.value.source = new EventSource(url);
    } catch (error) {
      console.error("Error initializing EventSource:", error);
      this._state.value.source = null;
      this.handleConnectionError();
      return;
    }

    this._state.value.source.onopen = () => {
      this._state.value.status = "connected";
      console.info("Server events connected.");

      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = null;
        console.info("Reconnection interval cleared.");
      }

      this.triggerHooks("onConnect");
    };

    this._state.value.source.onerror = (event) => {
      console.error("EventSource error occurred.", event);
      if (this._state.value.source) {
        this._state.value.source.close();
        this._state.value.source = null;
      }
      this.handleConnectionError();
      this.triggerHooks("onError", event);
    };

    this._state.value.source.onmessage = (event) => {
      console.info("Received event", event);
    };
  }

  disconnect(): void {
    console.info("Explicit disconnect called.");

    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
      console.info("Reconnection interval cleared due to explicit disconnect.");
    }

    if (this._state.value.source) {
      this._state.value.source.close();
      this._state.value.source = null;
      console.info("Server events source explicitly disconnected.");
    }

    this._state.value.status = "disconnected";
    this.triggerHooks("onDisconnect");
  }

  registerHook<T extends ConnectionHookType>(
    hookType: T,
    callback: ConnectionHookCallback[T]
  ): void {
    this.hooks.get(hookType)?.add(callback);
  }

  unregisterHook<T extends ConnectionHookType>(
    hookType: T,
    callback: ConnectionHookCallback[T]
  ): void {
    this.hooks.get(hookType)?.delete(callback);
  }

  addEventListener(
    eventName: string,
    listener: globalThis.EventListener
  ): void {
    if (
      this._state.value.source &&
      this._state.value.source.readyState === EventSource.OPEN
    ) {
      this._state.value.source.addEventListener(eventName, listener);
    }
  }

  removeEventListener(
    eventName: string,
    listener: globalThis.EventListener
  ): void {
    if (this._state.value.source) {
      this._state.value.source.removeEventListener(eventName, listener);
    }
  }

  private handleConnectionError(): void {
    this._state.value.status = "disconnected";

    if (this.reconnectInterval) {
      console.info(
        "Connection error. Reconnection attempt already in progress."
      );
      return;
    }

    console.info("Connection error. Starting reconnection attempts...");
    this.reconnectInterval = setInterval(() => {
      console.info("Interval: Attempting to reconnect...");
      const currentUrl = this._state.value.source?.url;
      if (currentUrl) {
        this.connect(currentUrl);
      }
    }, this.reconnectIntervalMs);
  }

  private triggerHooks<T extends ConnectionHookType>(
    hookType: T,
    ...args: Parameters<ConnectionHookCallback[T]>
  ): void {
    const callbacks = this.hooks.get(hookType);
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`Error in ${hookType} hook:`, error);
        }
      });
    }
  }
}
