import type { TopicInfo } from "@/types/server-events";

export interface ConnectionState {
  status: "connected" | "disconnected" | "connecting";
  source: EventSource | null;
  clientId: string;
}

export type ConnectionHookType = "onConnect" | "onDisconnect" | "onError";

export type ConnectionHookCallback = {
  onConnect: () => void;
  onDisconnect: () => void;
  onError: (error: Event) => void;
};

export type SSEEventListener = (event: MessageEvent) => void;
export type ServerEventName = "feed:item-update" | "chat" | (string & {});

export interface TopicsManagerConfig {
  clientId: string;
  baseUrl: string;
}

export interface SSEMediatorConfig {
  baseUrl: string;
  reconnectInterval?: number;
  clientId?: string;
}

export { type TopicInfo };
