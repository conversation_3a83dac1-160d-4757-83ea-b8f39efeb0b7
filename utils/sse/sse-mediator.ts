import { EventSourceConnectionManager } from "./connection-manager";
import { TopicsManager } from "./topics-manager";
import { EventListenersManager } from "./listeners-manager";
import type {
  SSEMediatorConfig,
  TopicInfo,
  ServerEventName,
  SSEEventListener,
} from "./types";

export class SSEMediator {
  private connectionManager: EventSourceConnectionManager;
  private topicsManager: TopicsManager;
  private listenersManager: EventListenersManager;
  private config: SSEMediatorConfig | null = null;
  private currentUrl: string | null = null;
  private hasHeartbeatListener = false;

  constructor() {
    this.connectionManager = new EventSourceConnectionManager();
    this.topicsManager = new TopicsManager();
    this.listenersManager = new EventListenersManager();

    this.setupConnectionHooks();
  }

  get connectionStatus() {
    return computed(() => this.connectionManager.state.value.status);
  }

  get activeTopics() {
    return this.topicsManager.activeTopics;
  }

  get clientId(): string {
    return this.connectionManager.state.value.clientId;
  }

  initialize(config: SSEMediatorConfig): void {
    this.config = config;

    // Update connection manager with custom clientId if provided
    if (config.clientId) {
      this.connectionManager = new EventSourceConnectionManager(
        config.clientId
      );
      this.setupConnectionHooks();
    }

    // Initialize topics manager
    this.topicsManager.initialize({
      clientId: this.clientId,
      baseUrl: config.baseUrl,
    });

    // Set up the connection URL
    this.currentUrl = `${config.baseUrl}/api/events/stream?clientId=${this.clientId}`;

    this.setupHeartbeatListener();
  }

  connect(): void {
    if (!this.config || !this.currentUrl) {
      throw new Error("SSEMediator not initialized. Call initialize() first.");
    }

    this.connectionManager.connect(this.currentUrl);
  }

  disconnect(): void {
    this.connectionManager.disconnect();
  }

  async subscribeToTopic(topics: TopicInfo | TopicInfo[]): Promise<void> {
    if (this.connectionStatus.value !== "connected") {
      console.info(
        "Server events are not connected. Topics will be subscribed when connection is established."
      );
      // Still add topics to manager for later subscription
      this.topicsManager.addTopicsToQueue(topics);
      return;
    }

    await this.topicsManager.subscribe(topics);
  }

  async unsubscribeFromTopic(topics: TopicInfo | TopicInfo[]): Promise<void> {
    await this.topicsManager.unsubscribe(topics);
  }

  async unsubscribeFromAllTopics(): Promise<void> {
    await this.topicsManager.unsubscribeFromAllTopics();
  }

  on(eventName: ServerEventName, listener: SSEEventListener): void {
    this.listenersManager.addListener(eventName, listener);

    // Register with connection if already connected
    if (this.connectionStatus.value === "connected") {
      this.connectionManager.addEventListener(
        eventName,
        listener as globalThis.EventListener
      );
    }
  }

  off(eventName: ServerEventName, listener: SSEEventListener): void {
    this.listenersManager.removeListener(eventName, listener);
    this.connectionManager.removeEventListener(
      eventName,
      listener as globalThis.EventListener
    );
  }

  destroy(): void {
    this.connectionManager.disconnect();
    this.topicsManager.clear();
    this.listenersManager.clear();
    this.hasHeartbeatListener = false;
  }

  private setupHeartbeatListener(): void {
    if (!this.hasHeartbeatListener) {
      this.on("heartbeat", () => {
        console.log("[SSE] heartbeat received on client", this.clientId);
      });
      this.hasHeartbeatListener = true;
    }
  }

  private setupConnectionHooks(): void {
    this.connectionManager.registerHook("onConnect", () => {
      console.info(
        "SSEMediator: Connection established, setting up subscriptions and listeners"
      );

      // Re-register all listeners
      this.listenersManager.registerAllListeners((eventName, listener) => {
        this.connectionManager.addEventListener(
          eventName,
          listener as globalThis.EventListener
        );
      });

      // Resubscribe to all topics
      this.topicsManager.resubscribeAll().catch((error) => {
        console.error("Error resubscribing to topics on connection:", error);
      });
    });

    this.connectionManager.registerHook("onDisconnect", () => {
      console.info("SSEMediator: Connection lost");
    });

    this.connectionManager.registerHook("onError", (error) => {
      console.error("SSEMediator: Connection error", error);
    });
  }
}
