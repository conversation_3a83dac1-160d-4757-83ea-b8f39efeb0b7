import type { TopicInfo, TopicsManagerConfig } from "./types";

export class TopicsManager {
  private _activeTopics = ref<Set<string>>(new Set());
  private config: TopicsManagerConfig | null = null;

  get activeTopics(): Readonly<Ref<ReadonlySet<string>>> {
    return readonly(this._activeTopics);
  }

  initialize(config: TopicsManagerConfig): void {
    this.config = config;
  }

  async subscribe(topics: TopicInfo | TopicInfo[]): Promise<void> {
    if (!this.config) {
      throw new Error(
        "TopicsManager not initialized. Call initialize() first."
      );
    }

    try {
      await this.handleSubscriptionRequest(topics, "subscribe");
      this.addTopics(topics);
    } catch (error) {
      console.error("Error subscribing to topic", error);
      throw error;
    }
  }

  async unsubscribe(topics: TopicInfo | TopicInfo[]): Promise<void> {
    if (!this.config) {
      throw new Error(
        "TopicsManager not initialized. Call initialize() first."
      );
    }

    try {
      await this.handleSubscriptionRequest(topics, "unsubscribe");
      this.deleteTopics(topics);
    } catch (error) {
      console.error("Error unsubscribing from topic", error);
      throw error;
    }
  }

  async unsubscribeFromAllTopics(): Promise<void> {
    if (!this.config) {
      throw new Error(
        "TopicsManager not initialized. Call initialize() first."
      );
    }

    try {
      await this.handleSubscriptionRequest(
        Array.from(this._activeTopics.value).map((topic) =>
          this.getTopicConfig(topic)
        ),
        "unsubscribe"
      );
      this.clear();
    } catch (error) {
      console.error("Error unsubscribing from all topics", error);
      throw error;
    }
  }

  async resubscribeAll(): Promise<void> {
    if (!this.config) {
      console.warn("TopicsManager not initialized. Cannot resubscribe.");
      return;
    }

    if (this._activeTopics.value.size === 0) {
      console.info("No active topics to resubscribe.");
      return;
    }

    const topicsInfo = Array.from(this._activeTopics.value).map((topic) =>
      this.getTopicConfig(topic)
    );

    try {
      await this.handleSubscriptionRequest(topicsInfo, "subscribe");
      console.info("Successfully resubscribed to all topics");
    } catch (error) {
      console.error("Error resubscribing to topics", error);
      throw error;
    }
  }

  clear(): void {
    this._activeTopics.value.clear();
  }

  addTopicsToQueue(topicInfo: TopicInfo | TopicInfo[]): void {
    this.addTopics(topicInfo);
  }

  private getTopicConfig(topic: string): TopicInfo {
    return {
      topicLevel: topic.split(":")[0] as "USER" | "ADVERTISER" | "AGENCY",
      topicIdentifier: topic.split(":")[1],
    };
  }

  private async handleSubscriptionRequest(
    topics: TopicInfo | TopicInfo[],
    action: "subscribe" | "unsubscribe"
  ): Promise<void> {
    if (!this.config) {
      throw new Error("TopicsManager not initialized");
    }

    const topicsAsList = Array.isArray(topics) ? topics : [topics];
    const request = topicsAsList.map((t) => ({
      ...t,
      clientId: this.config!.clientId,
    }));

    console.info(
      `Making ${action} request for topics: ${topicsAsList
        .map(
          ({ topicLevel, topicIdentifier }) =>
            `${topicLevel}:${topicIdentifier}`
        )
        .join(", ")}`
    );

    await $fetch("/api/events/subscriptions", {
      method: action === "subscribe" ? "POST" : "DELETE",
      body: request,
    });

    console.info(
      `${action} request completed for topics: ${topicsAsList
        .map(
          ({ topicLevel, topicIdentifier }) =>
            `${topicLevel}:${topicIdentifier}`
        )
        .join(", ")}`
    );
  }

  private addTopics(topicInfo: TopicInfo | TopicInfo[]): void {
    const topics = Array.isArray(topicInfo) ? topicInfo : [topicInfo];
    topics.forEach((t) => {
      this._activeTopics.value.add(`${t.topicLevel}:${t.topicIdentifier}`);
    });
  }

  private deleteTopics(topicInfo: TopicInfo | TopicInfo[]): void {
    const topics = Array.isArray(topicInfo) ? topicInfo : [topicInfo];
    topics.forEach((t) => {
      this._activeTopics.value.delete(`${t.topicLevel}:${t.topicIdentifier}`);
    });
  }
}
