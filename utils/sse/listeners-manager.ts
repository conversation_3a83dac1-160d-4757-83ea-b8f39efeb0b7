import type { SSEEventListener, ServerEventName } from "./types";

export class EventListenersManager {
  private _activeListeners = ref<Map<string, SSEEventListener[]>>(new Map());

  get activeListeners(): Readonly<
    Ref<ReadonlyMap<string, readonly SSEEventListener[]>>
  > {
    return readonly(this._activeListeners);
  }

  addListener(eventName: ServerEventName, listener: SSEEventListener): void {
    console.info("Adding listener for event", eventName);

    if (!this._activeListeners.value.has(eventName)) {
      this._activeListeners.value.set(eventName, []);
    }

    const listeners = this._activeListeners.value.get(eventName)!;
    if (!listeners.includes(listener)) {
      listeners.push(listener);
    }
  }

  removeListener(eventName: ServerEventName, listener: SSEEventListener): void {
    console.info("Removing listener for event", eventName);

    const listeners = this._activeListeners.value.get(eventName);
    if (listeners) {
      const updatedListeners = listeners.filter((l) => l !== listener);
      if (updatedListeners.length === 0) {
        this._activeListeners.value.delete(eventName);
      } else {
        this._activeListeners.value.set(eventName, updatedListeners);
      }
    }
  }

  registerAllListeners(
    registerFn: (eventName: string, listener: SSEEventListener) => void
  ): void {
    console.info("Registering all active listeners with connection");

    this._activeListeners.value.forEach((listeners, eventName) => {
      listeners.forEach((listener) => {
        registerFn(eventName, listener);
      });
    });
  }

  clear(): void {
    console.info("Clearing all listeners");
    this._activeListeners.value.clear();
  }

  getListenersForEvent(eventName: string): SSEEventListener[] {
    return this._activeListeners.value.get(eventName) || [];
  }

  hasListeners(): boolean {
    return this._activeListeners.value.size > 0;
  }

  getEventNames(): string[] {
    return Array.from(this._activeListeners.value.keys());
  }
}
