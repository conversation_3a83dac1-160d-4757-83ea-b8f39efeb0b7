function createNamingSettingsKnowledgeItem(
  agencyId: string
): KnowledgeItem<KnowledgeType.NAMING_SETTINGS> {
  return {
    id: undefined,
    knowledgeType: KnowledgeType.NAMING_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      orderNameTemplate: "",
      lineItemNameTemplate: "",
    },
    displayName: "Naming Settings",
    vectorize: false,
    ownerType: "AGENCY",
    ownerId: agencyId,
  };
}

function createLineItemSettingsKnowledgeItem(
  agencyId: string
): KnowledgeItem<KnowledgeType.LINE_ITEM_SETTINGS> {
  return {
    id: undefined,
    knowledgeType: KnowledgeType.LINE_ITEM_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      configuration: "",
    },
    displayName: "Line Item Settings",
    vectorize: false,
    ownerType: "AGENCY",
    ownerId: agencyId,
  };
}

function createAgencyAdditionalInfoKnowledgeItem(
  agencyId: string
): KnowledgeItem<KnowledgeType.AGENCY_ADDITIONAL_INFO> {
  return {
    id: undefined,
    knowledgeType: KnowledgeType.AGENCY_ADDITIONAL_INFO,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      info: "",
    },
    displayName: "Agency Additional Info",
    vectorize: false,
    ownerType: "AGENCY",
    ownerId: agencyId,
  };
}

function createTargetingSettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.TARGETING_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.TARGETING_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      remarketing: "",
      retargeting: "",
      NTB: "",
    },
    displayName: "Targeting Settings",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

function createKpiSettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.KPI_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.KPI_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      funnelKpis: [
        {
          funnelName: "Top of Funnel",
          kpis: [],
        },
        {
          funnelName: "Middle of Funnel",
          kpis: [],
        },
        {
          funnelName: "Bottom of Funnel",
          kpis: [],
        },
      ],
    },
    displayName: "KPI Settings",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

function createCurrentObjectivesSettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.CURRENT_OBJECTIVES_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.CURRENT_OBJECTIVES_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      objectives: "",
    },
    displayName: "Current Objectives",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

function createDspStrategySettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.DSP_STRATEGY_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.DSP_STRATEGY_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      strategy: "",
    },
    displayName: "DSP Strategy",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

function createAsinGroupingSettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.ASIN_GROUPING_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.ASIN_GROUPING_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      groups: [],
    },
    displayName: "ASIN Grouping",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

function createCompetitorsAsinsSettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.COMPETITORS_ASINS_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.COMPETITORS_ASINS_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      competitors: [],
      groups: [],
    },
    displayName: "Competitors ASINS",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

export function createBrandSafetySettingsKnowledgeItem(
  advertiserId: string
): KnowledgeItem<KnowledgeType.BRAND_SAFETY_SETTINGS> {
  return {
    knowledgeType: KnowledgeType.BRAND_SAFETY_SETTINGS,
    payload: {
      _type: KnowledgeItemPayloadType.JSON,
      excludedDomains: [],
    },
    displayName: "Brand Safety",
    ownerType: "ADVERTISER",
    ownerId: advertiserId,
  };
}

const knowledgeItemRegistry = {
  [KnowledgeType.NAMING_SETTINGS]: createNamingSettingsKnowledgeItem,
  [KnowledgeType.LINE_ITEM_SETTINGS]: createLineItemSettingsKnowledgeItem,
  [KnowledgeType.AGENCY_ADDITIONAL_INFO]: createAgencyAdditionalInfoKnowledgeItem,
  [KnowledgeType.TARGETING_SETTINGS]: createTargetingSettingsKnowledgeItem,
  [KnowledgeType.KPI_SETTINGS]: createKpiSettingsKnowledgeItem,
  [KnowledgeType.CURRENT_OBJECTIVES_SETTINGS]:
    createCurrentObjectivesSettingsKnowledgeItem,
  [KnowledgeType.DSP_STRATEGY_SETTINGS]: createDspStrategySettingsKnowledgeItem,
  [KnowledgeType.ASIN_GROUPING_SETTINGS]:
    createAsinGroupingSettingsKnowledgeItem,
  [KnowledgeType.COMPETITORS_ASINS_SETTINGS]:
    createCompetitorsAsinsSettingsKnowledgeItem,
  [KnowledgeType.BRAND_SAFETY_SETTINGS]: createBrandSafetySettingsKnowledgeItem,
} as Record<KnowledgeType, (ownerId: string) => KnowledgeItem<KnowledgeType>>;

export function createKnowledgeItem<T extends KnowledgeType>(
  type: T,
  ownerId: string
): KnowledgeItem<T> {
  return knowledgeItemRegistry[type](ownerId) as KnowledgeItem<T>;
}
