import { isBefore } from "date-fns";
import {
  FlightSchema,
  type DspListCampaignFlightsResponseContent,
  type Flight,
} from "~/types/amazon";

export async function getCampaignFlights(
  campaignId: string,
  advertiserId: string
): Promise<Flight[]> {
  try {
    const response = await $fetch<DspListCampaignFlightsResponseContent>(
      `/relay/rob/api/adsp/v1/campaigns/${campaignId}/flights/list`,
      {
        method: "POST",
        headers: withForwardedHeaders({
          "Gigi-AmazonDSPAdvertiserId": advertiserId,
        }),
      }
    );

    return response.flights.map((flight) => FlightSchema.parse(flight));
  } catch (error) {
    console.error(error);
    return [];
  }
}

export function getCurrentFlight(flights: Flight[]): Flight | undefined {
  return flights
    ?.filter((flight) => flight.startDateTime && flight.endDateTime)
    .find((flight) => {
      const now = new Date();
      return (
        isBefore(flight.startDateTime!, now) &&
        isBefore(now, flight.endDateTime!)
      );
    });
}

export function getCampaignStartDate(flights: Flight[]): Date | undefined {
  if (!flights.length) return undefined;

  const flightsWithDates = flights.filter((flight) => flight.startDateTime);

  if (!flightsWithDates.length) return undefined;

  return flightsWithDates.reduce((earliest, flight) => {
    return flight.startDateTime! < earliest ? flight.startDateTime! : earliest;
  }, flightsWithDates[0].startDateTime!);
}
