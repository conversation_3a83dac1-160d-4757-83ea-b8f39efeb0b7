export interface BatchConfig {
  batchWindowMs: number; // milliseconds to wait for batching
  maxBatchSize: number; // maximum items per batch
}

export interface BatchResolver<TResponse> {
  resolve: (value: TResponse) => void;
  reject: (error: Error) => void;
}

export interface PendingRequest<TRequest, TResponse> {
  request: TRequest;
  resolver: BatchResolver<TResponse>;
}

export interface BatchableRequest {
  [key: string]: any;
}

// Generic interfaces for batch processing
export interface BatchProcessor<
  TRequest,
  TBatchRequest,
  TBatchResponse,
  TResponse
> {
  // Generate a key for grouping requests (excludes the batching field)
  generateBatchingKey(request: TRequest): string;

  // Combine multiple requests into a single batch request
  combineToBatchRequest(
    requests: TRequest[],
    batchingKey: string
  ): TBatchRequest;

  // Execute the actual batch operation
  executeBatch(batchRequest: TBatchRequest): Promise<TBatchResponse>;

  // Split batch response back into individual responses
  splitBatchResponse(
    batchResponse: TBatchResponse,
    originalRequests: TRequest[]
  ): TResponse[];
}
