import { BatchSlot } from "./batch-slot";
import type { BatchConfig, BatchProcessor, BatchableRequest } from "./types";

class ConcreteBatchSlot<
  TRequest,
  TBatchRequest,
  TBatchResponse,
  TResponse
> extends BatchSlot<TRequest, TBatchRequest, TBatchResponse, TResponse> {
  // Concrete implementation that just inherits all functionality
}

export class RequestBatcher<
  TRequest extends BatchableRequest,
  TBatchRequest,
  TBatchResponse,
  TResponse
> {
  protected activeBatchSlots = new Map<
    string,
    BatchSlot<TRequest, TBatchRequest, TBatchResponse, TResponse>
  >();
  protected batchConfig: BatchConfig;

  constructor(
    protected batchProcessor: BatchProcessor<
      TRequest,
      TBatchRequest,
      TBatchResponse,
      TResponse
    >,
    config: Partial<BatchConfig> = {}
  ) {
    this.batchConfig = {
      batchWindowMs: 10, // 10ms default
      maxBatchSize: 50, // 50 items max per batch
      ...config,
    };
  }

  async handleRequest(request: TRequest): Promise<TResponse> {
    const batchingKey = this.batchProcessor.generateBatchingKey(request);

    // Get or create batch slot for this key
    let batchSlot = this.activeBatchSlots.get(batchingKey);

    if (!batchSlot || batchSlot.hasExecuted) {
      // Create new batch slot immediately - no waiting for other batches
      batchSlot = this.createBatchSlot(batchingKey);
      this.activeBatchSlots.set(batchingKey, batchSlot);
    }

    // Add request to batch slot
    return batchSlot.addRequestToBatch(request);
  }

  protected createBatchSlot(
    batchingKey: string
  ): BatchSlot<TRequest, TBatchRequest, TBatchResponse, TResponse> {
    return new ConcreteBatchSlot(
      this.batchConfig,
      batchingKey,
      this.batchProcessor,
      this.onBatchSlotComplete
    );
  }

  protected onBatchSlotComplete = (batchingKey: string): void => {
    // Cleanup completed batch slot
    this.activeBatchSlots.delete(batchingKey);
  };

  // Monitoring and utility methods
  getActiveBatchCount(): number {
    return this.activeBatchSlots.size;
  }

  getBatchingInfo(): Array<{
    batchingKey: string;
    pendingRequestCount: number;
    hasExecuted: boolean;
  }> {
    return Array.from(this.activeBatchSlots.entries()).map(([key, slot]) => ({
      batchingKey: key,
      pendingRequestCount: slot.pendingRequestCount,
      hasExecuted: slot.hasExecuted,
    }));
  }

  // For testing - get config details
  getBatchConfig(): BatchConfig {
    return { ...this.batchConfig };
  }

  // For testing - force execute all pending batches
  async flushAllPendingBatches(): Promise<void> {
    const slots = Array.from(this.activeBatchSlots.values());
    await Promise.all(
      slots.map((slot) => {
        // Trigger immediate execution by simulating timeout
        return new Promise<void>((resolve) => {
          setTimeout(() => resolve(), 0);
        });
      })
    );
  }
}
