import type { PendingRequest, BatchConfig, BatchProcessor } from "./types";

export abstract class BatchSlot<
  TRequest,
  TBatchRequest,
  TBatchResponse,
  TResponse
> {
  protected pendingRequests: PendingRequest<TRequest, TResponse>[] = [];
  protected batchTimeoutId: NodeJS.Timeout | null = null;
  protected isExecuted = false;

  constructor(
    protected batchConfig: BatchConfig,
    protected batchingKey: string,
    protected batchProcessor: BatchProcessor<
      TRequest,
      TBatchRequest,
      TBatchResponse,
      TResponse
    >,
    protected onBatchComplete: (batchingKey: string) => void
  ) {}

  addRequestToBatch(request: TRequest): Promise<TResponse> {
    if (this.isExecuted) {
      throw new Error(
        `Batch with key "${this.batchingKey}" has already been executed`
      );
    }

    return new Promise<TResponse>((resolve, reject) => {
      // Add request to pending list
      this.pendingRequests.push({
        request,
        resolver: { resolve, reject },
      });

      // Reset timeout to extend batch window
      this.resetBatchTimeout();

      // Execute immediately if batch reaches max size
      if (this.pendingRequests.length >= this.batchConfig.maxBatchSize) {
        this.executeBatchNow();
      }
    });
  }

  private resetBatchTimeout(): void {
    if (this.batchTimeoutId) {
      clearTimeout(this.batchTimeoutId);
    }

    this.batchTimeoutId = setTimeout(() => {
      this.executeBatchNow();
    }, this.batchConfig.batchWindowMs);
  }

  private async executeBatchNow(): Promise<void> {
    if (this.isExecuted) return;

    this.isExecuted = true;

    if (this.batchTimeoutId) {
      clearTimeout(this.batchTimeoutId);
      this.batchTimeoutId = null;
    }

    const requestsSnapshot = [...this.pendingRequests];
    const originalRequests = requestsSnapshot.map((pr) => pr.request);

    try {
      // Combine requests into batch format
      const batchRequest = this.batchProcessor.combineToBatchRequest(
        originalRequests,
        this.batchingKey
      );

      // Execute batch operation
      const batchResponse = await this.batchProcessor.executeBatch(
        batchRequest
      );

      // Split response back to individual responses
      const individualResponses = this.batchProcessor.splitBatchResponse(
        batchResponse,
        originalRequests
      );

      // Resolve individual promises
      for (let i = 0; i < requestsSnapshot.length; i++) {
        requestsSnapshot[i].resolver.resolve(individualResponses[i]);
      }
    } catch (error) {
      // Reject all promises in this batch
      for (const pendingRequest of requestsSnapshot) {
        pendingRequest.resolver.reject(error as Error);
      }
    } finally {
      // Cleanup and notify completion
      this.cleanupBatch();
    }
  }

  private cleanupBatch(): void {
    this.pendingRequests.length = 0;
    this.onBatchComplete(this.batchingKey);
  }

  get pendingRequestCount(): number {
    return this.pendingRequests.length;
  }

  get hasExecuted(): boolean {
    return this.isExecuted;
  }

  get key(): string {
    return this.batchingKey;
  }
}
