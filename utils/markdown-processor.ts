import remarkGfm from "remark-gfm";
import remarkRehype from "remark-rehype";
import rehypeRaw from "rehype-raw";
import rehypeStringify from "rehype-stringify";
import rehypeSanitize from "rehype-sanitize";
import remarkParse from "remark-parse";

import { unified } from "unified";
import { defaultSchema } from "rehype-sanitize";

const schema = {
  ...defaultSchema,
  tagNames: [...(defaultSchema.tagNames || []), "br"],
  attributes: {
    ...(defaultSchema.attributes || {}),
    br: [],
  },
};

const basePlugins: any[] = [
  remarkParse,
  remarkGfm,
  [remarkRehype, { allowDangerousHtml: true }],
  rehypeRaw,
  [rehypeSanitize, schema],
  [rehypeStringify, { allowDangerousHtml: true }],
];

export class MarkdownProcessor {
  private processor = unified();
  private breaklines: boolean;
  private sanitize: boolean;

  constructor({
    breaklines = false,
    sanitize = true,
    plugins = [],
  }: {
    breaklines?: boolean;
    sanitize?: boolean;
    plugins?: any[];
  }) {
    this.breaklines = breaklines;
    this.sanitize = sanitize;

    const pipeline = sanitize
      ? basePlugins
      : basePlugins.filter((p) => p !== rehypeSanitize);

    pipeline.push(...plugins);
    pipeline.forEach((pl) =>
      Array.isArray(pl)
        ? this.processor.use(pl[0], pl[1])
        : this.processor.use(pl)
    );
  }

  async process(markdown: string) {
    if (this.breaklines) {
      markdown = markdown.replace(/(?<!\n)\n(?!\n)/g, "  \n");
    }
    const file = await this.processor.process(markdown);
    return String(file);
  }
}
