type FormatCurrencyOptions = {
  compact?: boolean;
  precision?: number | { minimum?: number; maximum?: number };
};

export function formatCurrency(
  value: number | undefined | null,
  currencyCode: string = "USD",
  { compact = false, precision = 2 }: FormatCurrencyOptions = {}
) {
  if (value === undefined || value === null) return undefined;

  const truncated = Math.trunc(value * 100) / 100;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    notation: compact ? "compact" : "standard",
    minimumFractionDigits:
      typeof precision === "number" ? precision : precision.minimum,
    maximumFractionDigits:
      typeof precision === "number" ? precision : precision.maximum,
    currency: currencyCode,
  }).format(truncated);
}
