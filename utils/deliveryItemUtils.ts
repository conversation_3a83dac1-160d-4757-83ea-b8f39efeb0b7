import type { SuggestionType } from "~/types/delivery-item/suggestion/suggestion-type";
import type { Suggestion } from "~/types/delivery-item/suggestion";
import { SuggestionSchema } from "~/types/delivery-item/suggestion";
import type { DeliveryConfig } from "~/types/delivery-item/config";
import type { DeliveryItem } from "~/types/delivery-item/delivery-item";
import { DeliveryItemTypeSchema } from '~/types/delivery-item/delivery-item';
import { z } from "zod";

export function parseFullMessage(item: any) {
  try {
    if (item.type === DeliveryItemTypeSchema.Enum.ECPM_SPIKE_MONITOR) {
      return JSON.parse(item.fullMessage);
    }
    return item.fullMessage;
  } catch {
    return {};
  }
}

export function getOverriddenFullMessage(item: any) {
  if (item.type === DeliveryItemTypeSchema.Enum.ECPM_SPIKE_MONITOR) {
    return parseFullMessage(item).decision_summary;
  }
  return item.fullMessage;
}

export function getActionByActionType<T extends DeliveryItem>(
  item: T,
  suggestionType: SuggestionType
): Suggestion | undefined {
  return item.actionSuggestions?.find(
    (action) => action.actionType === suggestionType
  );
}

export function validateItemActionSuggestions<T extends DeliveryItem>(
  item: T,
  typeConfig: DeliveryConfig["types"][T["type"]]
) {
  const expectedActions = typeConfig.actionSuggestions.map(
    (actionConfig) => actionConfig.actionType
  );

  const existingActions = item.actionSuggestions?.map(
    (action) => action.actionType
  );

  return existingActions?.every((action) => expectedActions.includes(action));
}

export function isActionedOrNotRelevant(item: DeliveryItem): boolean {
  return !!(item.actionBy || item.actionAt || !item.isRelevant);
}

export function approvedAcceptedText(actionBy: string, actionAt: Date): string {
  return `${actionBy} approved these changes on ${dateText(actionAt)}`;
}

export function gotItAcceptedText(actionBy: string, actionAt: Date): string {
  return `${actionBy} got it on ${dateText(actionAt)}`;
}

type UnionMembers<U> = U extends z.ZodDiscriminatedUnion<any, infer Options>
  ? Options[number]
  : never;

type MemberSchema = UnionMembers<typeof SuggestionSchema>;

export function getSuggestionPayload<
  T extends DeliveryItem,
  Z extends MemberSchema
>(item: T, suggestionSchema: Z): z.infer<Z["shape"]["payload"]> {
  const expectedActionType = suggestionSchema.shape.actionType._def.value;
  const suggestion = getActionByActionType(item, expectedActionType);

  if (!suggestion) {
    throw new Error(
      `Suggestion with type '${expectedActionType}' not found on item '${item.id}'.`
    );
  }

  return suggestion.payload;
}
