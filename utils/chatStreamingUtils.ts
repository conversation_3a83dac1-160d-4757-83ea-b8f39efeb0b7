import type { z } from "zod";
import { PartEventSchema } from "~/types/chat/api-schemas";
import type { StatefulAiMessage } from "~/types/chat/models";

export function applyChatStreamEvent(
  message: StatefulAiMessage,
  eventData: z.infer<typeof PartEventSchema>
): StatefulAiMessage {
  console.log("Chat Streaming: Applying event", eventData.type, eventData);

  switch (eventData.type) {
    case "textPart":
      return handleTextPart(message, eventData);
    case "documentPart":
      return handleDocumentPart(message, eventData);
    case "filePart":
      return handleFilePart(message, eventData);
    case "formPart":
      return handleFormPart(message, eventData);
    case "thoughtStep":
      return handleThoughtStep(message, eventData);
    case "thoughtText":
      return handleThoughtText(message, eventData);
    case "thinkingDone":
      return handleThinkingDone(message, eventData);
    case "documentCreated":
    case "messageEnd":
      // These events should be handled by the caller, not here
      console.log(
        `Chat Streaming: ${eventData.type} should be handled by caller`
      );
      return message;
    default:
      console.warn("Chat Streaming: Unknown event type", eventData);
      return message; // Return unchanged if unknown event
  }
}

function handleTextPart(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "textPart" }>
): StatefulAiMessage {
  console.log("Chat Streaming: Handling textPart", data.content);

  const parts = [...message.parts];
  const lastPart = parts[parts.length - 1];

  if (lastPart && lastPart.kind === "text") {
    parts[parts.length - 1] = {
      ...lastPart,
      content:
        lastPart.content + (lastPart.content ? "\n\n" : "") + data.content,
    };
  } else {
    parts.push({
      kind: "text",
      content: data.content,
    });
  }

  return {
    ...message,
    parts,
  };
}

function handleDocumentPart(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "documentPart" }>
): StatefulAiMessage {
  console.log(
    "Chat Streaming: Handling documentPart",
    data.instance,
    data.version
  );

  return {
    ...message,
    parts: [
      ...message.parts,
      {
        kind: "document",
        instance: data.instance,
        version: data.version,
      },
    ],
  };
}

function handleFilePart(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "filePart" }>
): StatefulAiMessage {
  console.log("Chat Streaming: Handling filePart", data.fileName);

  return {
    ...message,
    parts: [
      ...message.parts,
      {
        kind: "file",
        fileName: data.fileName,
        fileType: data.fileType,
        referenceableId: data.referenceableId,
      },
    ],
  };
}

function handleFormPart(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "formPart" }>
): StatefulAiMessage {
  console.log("Chat Streaming: Handling formPart", data.formId);

  return {
    ...message,
    parts: [
      ...message.parts,
      {
        kind: "form",
        formConfig: {
          formId: data.formId,
          formType: data.formType,
          formData: data.defaultValues,
          state: "pending",
        },
      },
    ],
  };
}

function handleThoughtStep(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "thoughtStep" }>
): StatefulAiMessage {
  console.log("Chat Streaming: Handling thoughtStep", data.title);

  return {
    ...message,
    thoughtSteps: [
      ...message.thoughtSteps,
      {
        title: data.title,
        content: data.content,
      },
    ],
  };
}

function handleThoughtText(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "thoughtText" }>
): StatefulAiMessage {
  console.log(
    "Chat Streaming: Handling thoughtText",
    data.content.substring(0, 50) + "..."
  );

  const thoughtSteps = [...message.thoughtSteps];
  const lastThoughtStep = thoughtSteps[thoughtSteps.length - 1];

  if (lastThoughtStep) {
    const existingContent = lastThoughtStep.content || "";
    thoughtSteps[thoughtSteps.length - 1] = {
      ...lastThoughtStep,
      content: existingContent + (existingContent ? "\n\n" : "") + data.content,
    };
  } else {
    console.log(
      "Chat Streaming: No thought step found for thoughtText, creating one"
    );
    thoughtSteps.push({
      content: data.content,
    });
  }

  return {
    ...message,
    thoughtSteps,
  };
}

function handleThinkingDone(
  message: StatefulAiMessage,
  data: Extract<z.infer<typeof PartEventSchema>, { type: "thinkingDone" }>
): StatefulAiMessage {
  console.log("Chat Streaming: Handling thinkingDone", data.thoughtFor);

  // Mark thinking phase as complete
  // For now, we just return the message unchanged since the thinking steps are already in place
  return message;
}
