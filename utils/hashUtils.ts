export function hashKey(key: string) {
  return crypto.subtle
    .digest("SHA-256", new TextEncoder().encode(key))
    .then((hash) => {
      return Array.from(new Uint8Array(hash))
        .map((byte) => byte.toString(16).padStart(2, "0"))
        .join("");
    });
}

// Synchronous version for use in composables and other synchronous contexts
export function hashKeySync(key: string): string {
  // Simple hash function using string character codes
  let hash = 0;
  if (key.length === 0) return hash.toString();

  for (let i = 0; i < key.length; i++) {
    const char = key.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Convert to positive hex string
  return Math.abs(hash).toString(16);
}
